[["/home/<USER>/workspace/文档/09-学习笔记/前端特效控制原理学习笔记.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/09-学习笔记/前端特效控制原理学习笔记.md"}}], ["/home/<USER>/workspace/文档/10-历史归档/开发日志归档/开发日志/0729.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/10-历史归档/开发日志归档/开发日志/0729.md"}}], ["/home/<USER>/workspace/文档/10-历史归档/开发日志归档/开发日志/0729-2.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/10-历史归档/开发日志归档/开发日志/0729-2.md"}}], ["/home/<USER>/workspace/文档/AI工作协议/RIPER-5_教学协议_完整版.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/AI工作协议/RIPER-5_教学协议_完整版.md"}}], ["/home/<USER>/workspace/static/css/reading-effects.css", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "static/css/reading-effects.css"}}], ["/home/<USER>/workspace/文档/文档导航中心.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/文档导航中心.md"}}], ["/home/<USER>/workspace/文档/10-历史归档/开发日志归档/开发日志/0730.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/10-历史归档/开发日志归档/开发日志/0730.md"}}], ["/home/<USER>/workspace/templates/reading_page_new.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/reading_page_new.html"}}], ["/home/<USER>/workspace/文档/开发日志/0608.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/开发日志/0608.md"}}], ["/home/<USER>/workspace/文档/开发日志/0801.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/开发日志/0801.md"}}], ["/home/<USER>/workspace/文档/05-前端开发/01-Vue开发规范.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/05-前端开发/01-Vue开发规范.md"}}], ["/home/<USER>/workspace/文档/03-UI设计/02-页面设计/管理后台页面设计/01-控制面板页面分析.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/02-页面设计/管理后台页面设计/01-控制面板页面分析.md"}}], ["/home/<USER>/workspace/文档/开发日志/0802.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/开发日志/0802.md"}}], ["/home/<USER>/workspace/文档/03-UI设计/02-页面设计/管理后台页面设计/03-用户管理页面分析.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/02-页面设计/管理后台页面设计/03-用户管理页面分析.md"}}], ["/home/<USER>/workspace/文档/开发日志/0805.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/开发日志/0805.md"}}], ["/home/<USER>/workspace/文档/03-UI设计/01-设计系统/01-颜色系统.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/01-设计系统/01-颜色系统.md"}}], ["/home/<USER>/workspace/文档/03-UI设计/02-页面设计/管理后台页面设计/02-文章管理页面分析.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/02-页面设计/管理后台页面设计/02-文章管理页面分析.md"}}], ["/home/<USER>/workspace/文档/02-功能模块/09-1-阅读页面音频朗读功能完整技术规范.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/02-功能模块/09-1-阅读页面音频朗读功能完整技术规范.md"}}], ["/home/<USER>/workspace/文档/03-UI设计/01-UI设计规范.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/01-UI设计规范.md"}}], ["/home/<USER>/workspace/文档/03-UI设计/UI设计系统总览.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/UI设计系统总览.md"}}], ["/home/<USER>/workspace/文档/03-UI设计/02-页面设计/阅读页面设计/09-1-阅读页面音频朗读功能完整技术规范.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/03-UI设计/02-页面设计/阅读页面设计/09-1-阅读页面音频朗读功能完整技术规范.md"}}], ["/home/<USER>/workspace/文档/00-项目概览/完整业务逻辑文档.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/00-项目概览/完整业务逻辑文档.md"}}], ["/home/<USER>/workspace/admin-vue/src/router/index.ts", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/router/index.ts"}}], ["/home/<USER>/workspace/admin-vue/src/views/articles/ArticleEditView.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/articles/ArticleEditView.vue"}}], ["/home/<USER>/workspace/admin-vue/src/views/authors/AuthorListView.vue", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/authors/AuthorListView.vue"}}], ["/home/<USER>/workspace/文档/04-API接口/07-管理后台统计接口.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/04-API接口/07-管理后台统计接口.md"}}], ["/home/<USER>/workspace/blueprints/admin.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/admin.py"}}]]