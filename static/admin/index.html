<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>塔罗牌管理后台</title>
    <!-- 强制刷新缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <meta name="description" content="塔罗牌网站管理后台系统" />
    <meta name="keywords" content="塔罗牌,管理后台,Vue3,Element Plus" />

    <!-- 添加必要的外部依赖 - 完全复刻原版 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">

    <!-- 内联关键样式 - 确保深色主题正确显示 -->
    <style>
      /* 管理后台样式 - 完全复刻原版Flask模板设计 */
      body {
        background-color: #121212 !important;
        color: #e0e0e0 !important;
        font-family: 'Helvetica Neue', 'Arial', sans-serif !important;
      }

      /* 侧边栏样式 - 完全复刻原版 */
      .admin-sidebar {
        background-color: #1a1a1a !important;
        border-right: 1px solid #2a2a2a !important;
      }

      /* Logo样式 - 完全复刻原版 */
      .admin-logo {
        color: #f7c46c !important;
        font-weight: bold !important;
        font-size: 1.5rem !important;
      }

      /* 卡片样式 */
      .admin-card {
        background-color: #1e1e1e !important;
        border: 1px solid #2a2a2a !important;
        border-radius: 0.5rem !important;
      }

      /* 导航项样式 */
      .admin-nav-item {
        color: #e0e0e0 !important;
        transition: all 0.3s !important;
        padding: 0.75rem 1rem !important;
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem !important;
      }

      .admin-nav-item:hover {
        background-color: #f7c46c22 !important;
        color: #f7c46c !important;
        font-weight: 500 !important;
      }

      .admin-nav-item.active {
        background-color: #f7c46c22 !important;
        color: #f7c46c !important;
        font-weight: 500 !important;
      }

      /* 按钮样式 */
      .admin-btn-primary {
        background-color: #facc15 !important;
        color: #1f2937 !important;
        padding: 0.5rem 1rem !important;
        border-radius: 0.375rem !important;
        transition: all 0.3s !important;
        border: none !important;
        cursor: pointer !important;
        text-decoration: none !important;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
      }

      .admin-btn-primary:hover {
        background-color: #e6b35c !important;
        text-decoration: none !important;
        color: #1f2937 !important;
      }

      /* 统计卡片样式 - 完全复刻原版 */
      .stat-card {
        background-color: #1e1e1e !important;
        border: 1px solid #2a2a2a !important;
        border-radius: 0.5rem !important;
        padding: 1.5rem !important;
        transition: all 0.3s !important;
      }

      .stat-card:hover {
        border-color: #f7c46c !important;
        box-shadow: 0 0 10px rgba(247, 196, 108, 0.2) !important;
      }

      .stat-card-title {
        color: #a0a0a0 !important;
        font-size: 0.875rem !important;
        font-weight: 500 !important;
        margin-bottom: 0.5rem !important;
      }

      .stat-card-value {
        color: #f7c46c !important;
        font-size: 1.75rem !important;
        font-weight: bold !important;
        line-height: 1 !important;
      }
    </style>
    <script type="module" crossorigin src="/admin/js/index-Bo6OtMFR.js"></script>
    <link rel="stylesheet" crossorigin href="/admin/css/index-BRhmD2QS.css">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
