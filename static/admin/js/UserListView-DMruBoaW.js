var h=(a,d,g)=>new Promise((_,m)=>{var l=x=>{try{y(g.next(x))}catch(b){m(b)}},r=x=>{try{y(g.throw(x))}catch(b){m(b)}},y=x=>x.done?_(x.value):Promise.resolve(x.value).then(l,r);y((g=g.apply(a,d)).next())});import{h as n,d as W,i as q,m as U,k as X,c,a as t,g as D,t as i,p as I,v as Y,q as Z,s as F,e as S,F as N,l as O,E as p,n as C,y as tt,o as u}from"./index-Bo6OtMFR.js";import{E as et}from"./index-DU0nyBIP.js";import{_ as st}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DM_Oxv62.js";const V={getList(a){return n.get("/users",{params:a})},getDetail(a){return n.get(`/users/${a}`)},create(a){return n.post("/users",a)},update(a,d){return n.put(`/users/${a}`,d)},delete(a){return n.delete(`/users/${a}`)},batchDelete(a){return n.post("/users/batch-delete",{ids:a})},toggleActive(a,d){return n.put(`/users/${a}/active`,{is_active:d})},toggleAdmin(a,d){return n.put(`/users/${a}/admin`,{is_admin:d})},resetPassword(a,d){return n.put(`/users/${a}/reset-password`,{password:d})},getStats(){return n.get("/users/stats")},search(a){return n.get("/users/search",{params:{q:a}})},adjustFinance(a){return n.post("/users/adjust-finance",a)},getFinanceDetail(a){return n.get(`/users/${a}/finance`)},getConsumptionRecords(a,d){return n.get(`/users/${a}/consumption`,{params:d})},getReferralRecords(a,d){return n.get(`/users/${a}/referrals`,{params:d})},exportData(a){return n.get("/users/export",{params:a})},exportUsers(a){return n.get("/users/export",{params:a,responseType:"blob"})}},ot={class:"user-management"},at={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},rt={class:"admin-card p-4"},nt={class:"flex items-center"},lt={class:"ml-4"},it={class:"text-2xl font-bold text-white"},dt={class:"admin-card p-4"},ct={class:"flex items-center"},pt={class:"ml-4"},ut={class:"text-2xl font-bold text-white"},xt={class:"admin-card p-4"},ft={class:"flex items-center"},gt={class:"ml-4"},mt={class:"text-2xl font-bold text-white"},yt={class:"admin-card p-4"},vt={class:"flex items-center"},_t={class:"ml-4"},bt={class:"text-2xl font-bold text-white"},ht={class:"admin-card p-4 mb-6"},wt={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},kt={class:"admin-card p-4 mb-6 overflow-x-auto"},Ct={key:0,id:"loading",class:"text-center py-8"},Vt={key:1,id:"users-table"},$t={class:"table-admin w-full"},Pt={id:"users-tbody"},Ut={class:"px-4 py-3 border-t border-gray-700"},Dt={class:"px-4 py-3 border-t border-gray-700"},It={class:"user-info"},St={class:"user-name text-white font-medium"},Bt={class:"user-email text-gray-400 text-sm"},Rt={class:"px-4 py-3 border-t border-gray-700"},Et={class:"px-4 py-3 border-t border-gray-700"},Lt={class:"vip-info"},Mt={key:0,class:"vip-expire text-xs text-gray-400 mt-1"},Tt={class:"px-4 py-3 border-t border-gray-700"},jt={class:"quota-info text-sm"},qt={key:0},Ft={class:"px-4 py-3 border-t border-gray-700"},Nt={class:"balance text-green-400 font-mono"},Ot={class:"px-4 py-3 border-t border-gray-700 text-sm text-gray-400"},At={class:"px-4 py-3 border-t border-gray-700"},zt={class:"action-buttons flex space-x-1"},Kt=["onClick"],Gt=["onClick"],Ht=["onClick"],Jt=["onClick"],Qt={key:2,id:"no-data",class:"text-center py-8"},Wt={key:3,class:"pagination"},Xt=["disabled"],Yt={class:"page-numbers"},Zt=["onClick"],te=["disabled"],ee=W({__name:"UserListView",setup(a){const d=tt(),g=q([]),_=q(!1),m=U({total_users:0,vip_users:0,active_users:0,today_users:0}),l=U({search:"",role:"",vip_level:"",page:1,per_page:20}),r=U({page:1,pages:1,total:0,per_page:20,has_prev:!1,has_next:!1,prev_num:null,next_num:null}),y=s=>new Date(s).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}),x=s=>!s||s===0?"普通用户":s===2?"高级VIP":"普通VIP",b=s=>!s||s===0?"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300":s===2?"px-2 py-1 rounded-full text-xs bg-purple-900 text-purple-300":"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300",A=()=>{const s=[],e=r.page,o=r.pages,f=Math.max(1,e-2),k=Math.min(o,e+2);for(let v=f;v<=k;v++)s.push(v);return s},w=()=>h(this,null,function*(){_.value=!0;try{const s={page:l.page,per_page:l.per_page,search:l.search||void 0,role:l.role||void 0,vip_level:l.vip_level||void 0},e=yield V.getList(s);e.success?(g.value=e.users||[],r.page=e.current_page||1,r.pages=e.pages||1,r.total=e.total||0,r.per_page=s.per_page||20,r.has_prev=r.page>1,r.has_next=r.page<r.pages):p.error(e.message||"加载用户列表失败")}catch(s){console.error("加载用户失败:",s),p.error("加载用户列表失败")}finally{_.value=!1}}),B=()=>h(this,null,function*(){try{const s=yield V.getStats();s.success&&Object.assign(m,s.data)}catch(s){console.error("加载统计数据失败:",s)}}),z=()=>{w(),B()},R=()=>{l.page=1,w()},K=()=>h(this,null,function*(){try{p.info("正在导出用户数据...");const s=yield V.exportData(l);if(s.success){const e=new Blob([s.data],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),o=window.URL.createObjectURL(e),f=document.createElement("a");f.href=o,f.download=`用户数据_${new Date().toISOString().split("T")[0]}.xlsx`,f.click(),window.URL.revokeObjectURL(o),p.success("导出成功")}else p.error(s.message||"导出失败")}catch(s){console.error("导出失败:",s),p.error("导出失败")}}),G=s=>{d.push(`/users/${s.id}/detail`)},H=s=>{p.info("编辑用户功能开发中...")},J=s=>{p.info("VIP管理功能开发中...")},Q=s=>h(this,null,function*(){try{yield et.confirm(`确定要重置用户"${s.username}"的密码吗？`,"重置密码确认",{confirmButtonText:"确定重置",cancelButtonText:"取消",type:"warning"});const e=yield V.resetPassword(s.id);e.success?p.success("密码重置成功"):p.error(e.message||"密码重置失败")}catch(e){e!=="cancel"&&(console.error("密码重置失败:",e),p.error("密码重置失败"))}}),$=s=>{s&&s!==r.page&&(l.page=s,r.page=s,w())};return X(()=>{w(),B()}),(s,e)=>(u(),c("div",ot,[t("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[e[7]||(e[7]=t("h1",{class:"text-2xl font-bold text-white"},"用户管理",-1)),t("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[t("button",{onClick:z,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},e[5]||(e[5]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),D("刷新列表 ",-1)])),t("button",{onClick:K,class:"admin-btn-primary px-4 py-2 rounded-lg text-center"},e[6]||(e[6]=[t("i",{class:"fas fa-download mr-2"},null,-1),D("导出数据 ",-1)]))])]),t("div",at,[t("div",rt,[t("div",nt,[e[9]||(e[9]=t("div",{class:"p-3 rounded-full bg-blue-500 bg-opacity-20"},[t("i",{class:"fas fa-users text-blue-400 text-xl"})],-1)),t("div",lt,[e[8]||(e[8]=t("p",{class:"text-gray-400 text-sm"},"总用户数",-1)),t("p",it,i(m.total_users||"-"),1)])])]),t("div",dt,[t("div",ct,[e[11]||(e[11]=t("div",{class:"p-3 rounded-full bg-yellow-500 bg-opacity-20"},[t("i",{class:"fas fa-crown text-yellow-400 text-xl"})],-1)),t("div",pt,[e[10]||(e[10]=t("p",{class:"text-gray-400 text-sm"},"VIP用户",-1)),t("p",ut,i(m.vip_users||"-"),1)])])]),t("div",xt,[t("div",ft,[e[13]||(e[13]=t("div",{class:"p-3 rounded-full bg-green-500 bg-opacity-20"},[t("i",{class:"fas fa-check-circle text-green-400 text-xl"})],-1)),t("div",gt,[e[12]||(e[12]=t("p",{class:"text-gray-400 text-sm"},"已验证邮箱",-1)),t("p",mt,i(m.verified_users||"-"),1)])])]),t("div",yt,[t("div",vt,[e[15]||(e[15]=t("div",{class:"p-3 rounded-full bg-purple-500 bg-opacity-20"},[t("i",{class:"fas fa-calendar-day text-purple-400 text-xl"})],-1)),t("div",_t,[e[14]||(e[14]=t("p",{class:"text-gray-400 text-sm"},"今日新增",-1)),t("p",bt,i(m.today_users||"-"),1)])])])]),t("div",ht,[t("div",wt,[t("div",null,[e[16]||(e[16]=t("label",{for:"search",class:"block mb-2 text-gray-300"},"搜索用户",-1)),I(t("input",{"onUpdate:modelValue":e[0]||(e[0]=o=>l.search=o),type:"text",id:"search",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"用户名或邮箱...",onKeyup:Z(R,["enter"])},null,544),[[Y,l.search]])]),t("div",null,[e[18]||(e[18]=t("label",{for:"role-filter",class:"block mb-2 text-gray-300"},"角色过滤",-1)),I(t("select",{"onUpdate:modelValue":e[1]||(e[1]=o=>l.role=o),id:"role-filter",class:"form-input w-full px-3 py-2 rounded-lg"},e[17]||(e[17]=[t("option",{value:""},"全部角色",-1),t("option",{value:"user"},"普通用户",-1),t("option",{value:"admin"},"管理员",-1),t("option",{value:"editor"},"编辑",-1)]),512),[[F,l.role]])]),t("div",null,[e[20]||(e[20]=t("label",{for:"vip-filter",class:"block mb-2 text-gray-300"},"VIP状态",-1)),I(t("select",{"onUpdate:modelValue":e[2]||(e[2]=o=>l.vip_level=o),id:"vip-filter",class:"form-input w-full px-3 py-2 rounded-lg"},e[19]||(e[19]=[t("option",{value:""},"全部状态",-1),t("option",{value:"0"},"普通用户",-1),t("option",{value:"1"},"普通VIP",-1),t("option",{value:"2"},"高级VIP",-1)]),512),[[F,l.vip_level]])]),t("div",{class:"flex items-end"},[t("button",{onClick:R,id:"search-btn",class:"admin-btn-primary px-4 py-2 rounded-lg w-full"},e[21]||(e[21]=[t("i",{class:"fas fa-search mr-2"},null,-1),D("搜索 ",-1)]))])])]),t("div",kt,[_.value?(u(),c("div",Ct,e[22]||(e[22]=[t("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400"},null,-1),t("p",{class:"text-gray-400 mt-2"},"加载中...",-1)]))):g.value.length>0?(u(),c("div",Vt,[t("table",$t,[e[27]||(e[27]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-2 text-left"},"ID"),t("th",{class:"px-4 py-2 text-left"},"用户信息"),t("th",{class:"px-4 py-2 text-left"},"角色"),t("th",{class:"px-4 py-2 text-left"},"VIP状态"),t("th",{class:"px-4 py-2 text-left"},"配额使用"),t("th",{class:"px-4 py-2 text-left"},"余额"),t("th",{class:"px-4 py-2 text-left"},"注册时间"),t("th",{class:"px-4 py-2 text-left"},"操作")])],-1)),t("tbody",Pt,[(u(!0),c(N,null,O(g.value,o=>{var f,k,v,E,L,M,T,j;return u(),c("tr",{key:o.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",Ut,i(o.id),1),t("td",Dt,[t("div",It,[t("div",St,i(o.username),1),t("div",Bt,i(o.email||"无邮箱"),1),t("div",{class:C(["email-status text-xs",o.email_verified?"text-green-400":"text-red-400"])},i(o.email_verified?"✓ 已验证":"✗ 未验证"),3)])]),t("td",Rt,[t("span",{class:C(["px-2 py-1 rounded-full text-xs",o.role==="admin"?"bg-red-900 text-red-300":"bg-gray-700 text-gray-300"])},i(o.role==="admin"?"管理员":"普通用户"),3)]),t("td",Et,[t("div",Lt,[t("span",{class:C(b((f=o.finance)==null?void 0:f.vip_level))},i(x((k=o.finance)==null?void 0:k.vip_level)),3),(v=o.finance)!=null&&v.vip_expire_at&&((E=o.finance)==null?void 0:E.vip_level)>0?(u(),c("div",Mt," 到期: "+i(y(o.finance.vip_expire_at)),1)):S("",!0)])]),t("td",Tt,[t("div",jt,[t("div",null,"基础: "+i(((L=o.finance)==null?void 0:L.basic_quota_used)||0)+"/10",1),((M=o.finance)==null?void 0:M.vip_level)>=2?(u(),c("div",qt," 高级: "+i(((T=o.finance)==null?void 0:T.premium_quota_used)||0)+"/10 ",1)):S("",!0)])]),t("td",Ft,[t("span",Nt,"¥"+i((((j=o.finance)==null?void 0:j.balance)||0).toFixed(2)),1)]),t("td",Ot,i(y(o.created_at)),1),t("td",At,[t("div",zt,[t("button",{onClick:P=>G(o),class:"admin-btn-secondary px-2 py-1 rounded text-sm",title:"查看详情"},e[23]||(e[23]=[t("i",{class:"fas fa-eye"},null,-1)]),8,Kt),t("button",{onClick:P=>H(),class:"admin-btn-primary px-2 py-1 rounded text-sm",title:"编辑"},e[24]||(e[24]=[t("i",{class:"fas fa-edit"},null,-1)]),8,Gt),t("button",{onClick:P=>J(),class:"admin-btn-secondary px-2 py-1 rounded text-sm",title:"VIP管理"},e[25]||(e[25]=[t("i",{class:"fas fa-crown"},null,-1)]),8,Ht),t("button",{onClick:P=>Q(o),class:"admin-btn-warning px-2 py-1 rounded text-sm",title:"重置密码"},e[26]||(e[26]=[t("i",{class:"fas fa-key"},null,-1)]),8,Jt)])])])}),128))])])])):(u(),c("div",Qt,e[28]||(e[28]=[t("i",{class:"fas fa-users text-4xl text-gray-600 mb-4"},null,-1),t("p",{class:"text-gray-400"},"暂无用户数据",-1)]))),r.pages>1?(u(),c("div",Wt,[t("button",{class:"btn-secondary",disabled:!r.has_prev,onClick:e[3]||(e[3]=o=>$(r.prev_num))}," 上一页 ",8,Xt),t("div",Yt,[(u(!0),c(N,null,O(A(),o=>(u(),c("button",{key:o,class:C(["page-btn",o===r.page?"active":""]),onClick:f=>$(o)},i(o),11,Zt))),128))]),t("button",{class:"btn-secondary",disabled:!r.has_next,onClick:e[4]||(e[4]=o=>$(r.next_num))}," 下一页 ",8,te)])):S("",!0)])]))}}),le=st(ee,[["__scopeId","data-v-ab3968fc"]]);export{le as default};
