{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/views/articles/ArticleBuyersView.vue"}, "originalCode": "<template>\n  <div class=\"article-buyers\">\n    <!-- 页面标题 - 完全复刻原版Flask -->\n    <div class=\"mb-6 flex justify-between items-center\">\n      <h1 class=\"text-xl font-bold text-white\">{{ article?.title || '文章购买者列表' }}</h1>\n      <router-link to=\"/articles\" class=\"admin-btn-secondary px-4 py-2 rounded-lg\">\n        <i class=\"fas fa-arrow-left mr-2\"></i>返回文章列表\n      </router-link>\n    </div>\n\n    <!-- 文章基本信息 - 复刻原版Flask -->\n    <div v-if=\"article\" class=\"admin-card p-4 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div>\n          <span class=\"text-gray-400\">ID:</span>\n          <span class=\"ml-2 text-white\">{{ article.id }}</span>\n        </div>\n        <div>\n          <span class=\"text-gray-400\">状态:</span>\n          <span class=\"ml-2 px-2 py-1 rounded-full text-xs\"\n                :class=\"getStatusClass(article.status)\">\n            {{ getStatusText(article.status) }}\n          </span>\n        </div>\n        <div>\n          <span class=\"text-gray-400\">创建时间:</span>\n          <span class=\"ml-2 text-white\">{{ formatDateTime(article.created_at) }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 购买者列表 - 复刻原版Flask -->\n    <div class=\"admin-card p-4\">\n      <h2 class=\"text-lg font-bold text-white mb-4\">购买者列表</h2>\n\n      <!-- 加载状态 -->\n      <div v-if=\"loading\" id=\"buyers-loading\" class=\"text-center py-8\">\n        <div class=\"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500\"></div>\n        <p class=\"mt-2 text-gray-400\">正在加载购买者数据...</p>\n      </div>\n\n      <!-- 错误状态 -->\n      <div v-else-if=\"error\" id=\"buyers-error\" class=\"text-center py-8\">\n        <div class=\"text-red-400 mb-4\">\n          <i class=\"fas fa-exclamation-triangle text-2xl mb-2\"></i>\n          <p id=\"error-message\">{{ error }}</p>\n        </div>\n        <button @click=\"loadBuyers\" class=\"admin-btn-primary px-4 py-2 rounded-lg\">\n          <i class=\"fas fa-redo mr-2\"></i>重新加载\n        </button>\n      </div>\n\n      <!-- 无数据状态 -->\n      <div v-else-if=\"buyers.length === 0\" id=\"buyers-empty\" class=\"text-center py-8\">\n        <div class=\"text-gray-400 mb-4\">\n          <i class=\"fas fa-users text-4xl mb-4\"></i>\n          <p class=\"text-lg\">暂无购买者</p>\n          <p class=\"text-sm\">该文章还没有用户购买</p>\n        </div>\n      </div>\n\n      <!-- 购买者列表 -->\n      <div v-else id=\"buyers-list\" class=\"overflow-x-auto\">\n        <table class=\"table-admin w-full\">\n          <thead>\n            <tr>\n              <th class=\"px-3 py-2 text-left\">用户信息</th>\n              <th class=\"px-3 py-2 text-left\">内容类型</th>\n              <th class=\"px-3 py-2 text-left\">支付信息</th>\n              <th class=\"px-3 py-2 text-left\">配额消耗</th>\n              <th class=\"px-3 py-2 text-left\">积分消耗</th>\n              <th class=\"px-3 py-2 text-left\">用户状态</th>\n              <th class=\"px-3 py-2 text-left\">购买时间</th>\n            </tr>\n          </thead>\n          <tbody id=\"buyers-table-body\">\n            <tr\n              v-for=\"buyer in buyers\"\n              :key=\"buyer.user_id\"\n              class=\"hover:bg-gray-800 transition-colors\"\n            >\n              <!-- 用户信息 -->\n              <td class=\"px-3 py-2\">\n                <div class=\"text-white font-medium\">{{ buyer.username }}</div>\n                <div class=\"text-xs text-gray-400\">ID: {{ buyer.user_id }}</div>\n                <div class=\"text-xs text-gray-400\">{{ buyer.email }}</div>\n              </td>\n\n              <!-- 内容类型 -->\n              <td class=\"px-3 py-2\">\n                <span class=\"px-2 py-1 rounded text-xs font-medium\"\n                      :class=\"getContentTypeClass(buyer.content_type)\">\n                  {{ getContentTypeText(buyer.content_type) }}\n                </span>\n              </td>\n\n              <!-- 支付信息 -->\n              <td class=\"px-3 py-2\">\n                <div class=\"text-green-400 font-medium\">\n                  <span class=\"px-2 py-1 rounded text-xs\"\n                        :class=\"getCostTypeClass(buyer.cost_type)\">\n                    {{ getCostTypeText(buyer.cost_type) }}\n                  </span>\n                </div>\n                <div class=\"text-xs text-gray-400 mt-1\">\n                  总价值: ¥{{ buyer.cost_amount.toFixed(2) }}\n                </div>\n                <div class=\"text-xs text-gray-400\">\n                  实付: ¥{{ buyer.amount_paid.toFixed(2) }}\n                </div>\n                <div v-if=\"buyer.transaction_id\" class=\"text-xs text-gray-500 mt-1\">\n                  单号: {{ buyer.transaction_id }}\n                </div>\n              </td>\n\n              <!-- 配额消耗 -->\n              <td class=\"px-3 py-2\">\n                <div v-if=\"buyer.quota_used > 0\" class=\"text-yellow-400 font-medium\">\n                  -{{ buyer.quota_used }} {{ getQuotaTypeText(buyer.quota_type) }}\n                </div>\n                <div v-else class=\"text-gray-500\">无配额消耗</div>\n              </td>\n\n              <!-- 积分消耗 -->\n              <td class=\"px-3 py-2\">\n                <div v-if=\"buyer.points_used > 0\" class=\"text-blue-400 font-medium\">\n                  -{{ buyer.points_used }} 积分\n                </div>\n                <div v-else class=\"text-gray-500\">无积分消耗</div>\n                <div v-if=\"buyer.points_balance_before !== null\" class=\"text-xs text-gray-400\">\n                  余额: {{ buyer.points_balance_before }} → {{ buyer.points_balance_after }}\n                </div>\n              </td>\n\n              <!-- 用户状态 -->\n              <td class=\"px-3 py-2\">\n                <div class=\"text-xs\">\n                  <span class=\"px-2 py-1 rounded text-xs\"\n                        :class=\"getVipLevelClass(buyer.vip_level_at_purchase)\">\n                    {{ getVipLevelText(buyer.vip_level_at_purchase) }}\n                  </span>\n                  <div class=\"text-xs text-gray-400 mt-1\">\n                    购买时等级\n                  </div>\n                </div>\n\n                <!-- 支付构成 -->\n                <div class=\"text-xs text-gray-400 mt-2\">\n                  <div class=\"font-medium text-white\">支付构成:</div>\n                  <div class=\"text-xs\">{{ buyer.cost_type }}</div>\n                </div>\n\n                  <!-- 配额部分 -->\n                  <div v-if=\"buyer.quota_used > 0\" class=\"flex justify-between\">\n                    <span>配额:</span>\n                    <span class=\"text-yellow-400\">{{ buyer.quota_used }}篇</span>\n                  </div>\n\n                  <!-- 免费购买标识 -->\n                  <div v-if=\"buyer.is_free_purchase\" class=\"text-xs text-orange-400 mt-1\">\n                    <i class=\"fas fa-gift mr-1\"></i>免费获得\n                  </div>\n                </div>\n              </td>\n\n              <!-- 购买时间 -->\n              <td class=\"px-3 py-2 text-gray-300 text-sm\">\n                {{ formatDateTime(buyer.purchase_time) }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { articlesApi } from '@/api/articles'\n\n// 路由\nconst route = useRoute()\nconst articleId = parseInt(route.params.id as string)\n\n// 响应式数据\nconst loading = ref(false)\nconst error = ref('')\nconst article = ref(null)\nconst buyers = ref([])\nconst totalCount = ref(0)\n\n// 状态映射\nconst getStatusText = (status: string) => {\n  const statusMap = {\n    'published': '已发布',\n    'draft': '草稿',\n    'archived': '已归档'\n  }\n  return statusMap[status] || status\n}\n\nconst getStatusClass = (status: string) => {\n  const classMap = {\n    'published': 'bg-green-900 text-green-300',\n    'draft': 'bg-gray-700 text-gray-300',\n    'archived': 'bg-red-900 text-red-300'\n  }\n  return classMap[status] || 'bg-gray-700 text-gray-300'\n}\n\n// 内容类型映射\nconst getContentTypeText = (type: string) => {\n  const typeMap = {\n    'premium': '高级内容',\n    'basic': '基础内容',\n    'full': '完整内容'\n  }\n  return typeMap[type] || type\n}\n\nconst getContentTypeClass = (type: string) => {\n  const classMap = {\n    'premium': 'bg-yellow-600 text-yellow-100',\n    'basic': 'bg-blue-600 text-blue-100',\n    'full': 'bg-green-600 text-green-100'\n  }\n  return classMap[type] || 'bg-gray-600 text-gray-100'\n}\n\n// 配额类型映射\nconst getQuotaTypeText = (type: string) => {\n  const typeMap = {\n    'premium': '高级配额',\n    'basic': '基础配额',\n    'vip': 'VIP配额',\n    'recharge': '充值配额'\n  }\n  return typeMap[type] || type || '未知'\n}\n\n// VIP等级映射\nconst getVipLevelText = (level: number) => {\n  const levelMap = {\n    0: '普通用户',\n    1: '初级VIP',\n    2: 'VIP Pro'\n  }\n  return levelMap[level] || '未知'\n}\n\nconst getVipLevelClass = (level: number) => {\n  const classMap = {\n    0: 'bg-gray-700 text-gray-300',\n    1: 'bg-blue-700 text-blue-300',\n    2: 'bg-purple-700 text-purple-300'\n  }\n  return classMap[level] || 'bg-gray-700 text-gray-300'\n}\n\n// 消费方式映射\nconst getCostTypeText = (type: string) => {\n  const typeMap = {\n    '现金支付': '现金支付',\n    '积分支付': '积分支付',\n    '基础配额': '基础配额',\n    '高级配额': '高级配额',\n    '混合支付': '混合支付',\n    '自动赠送': '自动赠送',\n    'cash': '现金支付',\n    'quota': '配额使用',\n    'points': '积分支付',\n    'mixed': '混合支付',\n    'grant': '自动赠送',\n    'balance': '余额支付',\n    'unknown': '未知'\n  }\n  return typeMap[type] || type\n}\n\nconst getCostTypeClass = (type: string) => {\n  const classMap = {\n    '现金支付': 'bg-green-700 text-green-300',\n    '积分支付': 'bg-blue-700 text-blue-300',\n    '基础配额': 'bg-yellow-700 text-yellow-300',\n    '高级配额': 'bg-orange-700 text-orange-300',\n    '混合支付': 'bg-purple-700 text-purple-300',\n    '自动赠送': 'bg-gray-700 text-gray-300',\n    'cash': 'bg-green-700 text-green-300',\n    'quota': 'bg-yellow-700 text-yellow-300',\n    'points': 'bg-blue-700 text-blue-300',\n    'mixed': 'bg-purple-700 text-purple-300',\n    'grant': 'bg-orange-700 text-orange-300',\n    'balance': 'bg-teal-700 text-teal-300',\n    'unknown': 'bg-gray-700 text-gray-300'\n  }\n  return classMap[type] || 'bg-gray-700 text-gray-300'\n}\n\n// 格式化日期时间\nconst formatDateTime = (dateString: string) => {\n  if (!dateString) return '未知'\n  const date = new Date(dateString)\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\n// 加载购买者数据\nconst loadBuyers = async () => {\n  loading.value = true\n  error.value = ''\n\n  try {\n    // 调用真实API\n    const response = await articlesApi.getBuyers(articleId)\n\n    if (response.success) {\n      article.value = response.article\n      buyers.value = response.buyers || []\n      totalCount.value = response.total_count || 0\n    } else {\n      error.value = response.error || '加载购买者数据失败'\n    }\n  } catch (err) {\n    console.error('加载购买者失败:', err)\n    error.value = '网络错误，请稍后重试'\n  } finally {\n    loading.value = false\n  }\n}\n\n// 组件挂载时加载数据\nonMounted(() => {\n  if (articleId) {\n    loadBuyers()\n  } else {\n    error.value = '无效的文章ID'\n  }\n})\n</script>\n\n<style scoped>\n/* 复用管理后台的样式 */\n.admin-card {\n  @apply bg-gray-800 border border-gray-700 rounded-lg;\n}\n\n.admin-btn-primary {\n  @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium transition-colors;\n}\n\n.admin-btn-secondary {\n  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium transition-colors;\n}\n\n.table-admin {\n  @apply bg-gray-800;\n}\n\n.table-admin th {\n  @apply bg-gray-700 text-gray-300 font-medium;\n}\n\n.table-admin td {\n  @apply border-t border-gray-700;\n}\n</style>\n", "modifiedCode": "<template>\n  <div class=\"article-buyers\">\n    <!-- 页面标题 - 完全复刻原版Flask -->\n    <div class=\"mb-6 flex justify-between items-center\">\n      <h1 class=\"text-xl font-bold text-white\">{{ article?.title || '文章购买者列表' }}</h1>\n      <router-link to=\"/articles\" class=\"admin-btn-secondary px-4 py-2 rounded-lg\">\n        <i class=\"fas fa-arrow-left mr-2\"></i>返回文章列表\n      </router-link>\n    </div>\n\n    <!-- 文章基本信息 - 复刻原版Flask -->\n    <div v-if=\"article\" class=\"admin-card p-4 mb-6\">\n      <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <div>\n          <span class=\"text-gray-400\">ID:</span>\n          <span class=\"ml-2 text-white\">{{ article.id }}</span>\n        </div>\n        <div>\n          <span class=\"text-gray-400\">状态:</span>\n          <span class=\"ml-2 px-2 py-1 rounded-full text-xs\"\n                :class=\"getStatusClass(article.status)\">\n            {{ getStatusText(article.status) }}\n          </span>\n        </div>\n        <div>\n          <span class=\"text-gray-400\">创建时间:</span>\n          <span class=\"ml-2 text-white\">{{ formatDateTime(article.created_at) }}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- 购买者列表 - 复刻原版Flask -->\n    <div class=\"admin-card p-4\">\n      <h2 class=\"text-lg font-bold text-white mb-4\">购买者列表</h2>\n\n      <!-- 加载状态 -->\n      <div v-if=\"loading\" id=\"buyers-loading\" class=\"text-center py-8\">\n        <div class=\"inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500\"></div>\n        <p class=\"mt-2 text-gray-400\">正在加载购买者数据...</p>\n      </div>\n\n      <!-- 错误状态 -->\n      <div v-else-if=\"error\" id=\"buyers-error\" class=\"text-center py-8\">\n        <div class=\"text-red-400 mb-4\">\n          <i class=\"fas fa-exclamation-triangle text-2xl mb-2\"></i>\n          <p id=\"error-message\">{{ error }}</p>\n        </div>\n        <button @click=\"loadBuyers\" class=\"admin-btn-primary px-4 py-2 rounded-lg\">\n          <i class=\"fas fa-redo mr-2\"></i>重新加载\n        </button>\n      </div>\n\n      <!-- 无数据状态 -->\n      <div v-else-if=\"buyers.length === 0\" id=\"buyers-empty\" class=\"text-center py-8\">\n        <div class=\"text-gray-400 mb-4\">\n          <i class=\"fas fa-users text-4xl mb-4\"></i>\n          <p class=\"text-lg\">暂无购买者</p>\n          <p class=\"text-sm\">该文章还没有用户购买</p>\n        </div>\n      </div>\n\n      <!-- 购买者列表 -->\n      <div v-else id=\"buyers-list\" class=\"overflow-x-auto\">\n        <table class=\"table-admin w-full\">\n          <thead>\n            <tr>\n              <th class=\"px-3 py-2 text-left\">用户信息</th>\n              <th class=\"px-3 py-2 text-left\">内容类型</th>\n              <th class=\"px-3 py-2 text-left\">支付信息</th>\n              <th class=\"px-3 py-2 text-left\">配额消耗</th>\n              <th class=\"px-3 py-2 text-left\">积分消耗</th>\n              <th class=\"px-3 py-2 text-left\">用户状态</th>\n              <th class=\"px-3 py-2 text-left\">购买时间</th>\n            </tr>\n          </thead>\n          <tbody id=\"buyers-table-body\">\n            <tr\n              v-for=\"buyer in buyers\"\n              :key=\"buyer.user_id\"\n              class=\"hover:bg-gray-800 transition-colors\"\n            >\n              <!-- 用户信息 -->\n              <td class=\"px-3 py-2\">\n                <div class=\"text-white font-medium\">{{ buyer.username }}</div>\n                <div class=\"text-xs text-gray-400\">ID: {{ buyer.user_id }}</div>\n                <div class=\"text-xs text-gray-400\">{{ buyer.email }}</div>\n              </td>\n\n              <!-- 内容类型 -->\n              <td class=\"px-3 py-2\">\n                <span class=\"px-2 py-1 rounded text-xs font-medium\"\n                      :class=\"getContentTypeClass(buyer.content_type)\">\n                  {{ getContentTypeText(buyer.content_type) }}\n                </span>\n              </td>\n\n              <!-- 支付信息 -->\n              <td class=\"px-3 py-2\">\n                <div class=\"text-green-400 font-medium\">\n                  <span class=\"px-2 py-1 rounded text-xs\"\n                        :class=\"getCostTypeClass(buyer.cost_type)\">\n                    {{ getCostTypeText(buyer.cost_type) }}\n                  </span>\n                </div>\n                <div class=\"text-xs text-gray-400 mt-1\">\n                  总价值: ¥{{ buyer.cost_amount.toFixed(2) }}\n                </div>\n                <div class=\"text-xs text-gray-400\">\n                  实付: ¥{{ buyer.amount_paid.toFixed(2) }}\n                </div>\n                <div v-if=\"buyer.transaction_id\" class=\"text-xs text-gray-500 mt-1\">\n                  单号: {{ buyer.transaction_id }}\n                </div>\n              </td>\n\n              <!-- 配额消耗 -->\n              <td class=\"px-3 py-2\">\n                <div v-if=\"buyer.quota_used > 0\" class=\"text-yellow-400 font-medium\">\n                  -{{ buyer.quota_used }} {{ getQuotaTypeText(buyer.quota_type) }}\n                </div>\n                <div v-else class=\"text-gray-500\">无配额消耗</div>\n              </td>\n\n              <!-- 积分消耗 -->\n              <td class=\"px-3 py-2\">\n                <div v-if=\"buyer.points_used > 0\" class=\"text-blue-400 font-medium\">\n                  -{{ buyer.points_used }} 积分\n                </div>\n                <div v-else class=\"text-gray-500\">无积分消耗</div>\n                <div v-if=\"buyer.points_balance_before !== null\" class=\"text-xs text-gray-400\">\n                  余额: {{ buyer.points_balance_before }} → {{ buyer.points_balance_after }}\n                </div>\n              </td>\n\n              <!-- 用户状态 -->\n              <td class=\"px-3 py-2\">\n                <div class=\"text-xs\">\n                  <span class=\"px-2 py-1 rounded text-xs\"\n                        :class=\"getVipLevelClass(buyer.vip_level_at_purchase)\">\n                    {{ getVipLevelText(buyer.vip_level_at_purchase) }}\n                  </span>\n                  <div class=\"text-xs text-gray-400 mt-1\">\n                    购买时等级\n                  </div>\n                </div>\n\n                <!-- 支付构成 -->\n                <div class=\"text-xs text-gray-400 mt-2\">\n                  <div class=\"font-medium text-white\">支付构成:</div>\n                  <div class=\"text-xs\">{{ buyer.cost_type }}</div>\n                </div>\n              </td>\n              </td>\n\n              <!-- 购买时间 -->\n              <td class=\"px-3 py-2 text-gray-300 text-sm\">\n                {{ formatDateTime(buyer.purchase_time) }}\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue'\nimport { useRoute } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { articlesApi } from '@/api/articles'\n\n// 路由\nconst route = useRoute()\nconst articleId = parseInt(route.params.id as string)\n\n// 响应式数据\nconst loading = ref(false)\nconst error = ref('')\nconst article = ref(null)\nconst buyers = ref([])\nconst totalCount = ref(0)\n\n// 状态映射\nconst getStatusText = (status: string) => {\n  const statusMap = {\n    'published': '已发布',\n    'draft': '草稿',\n    'archived': '已归档'\n  }\n  return statusMap[status] || status\n}\n\nconst getStatusClass = (status: string) => {\n  const classMap = {\n    'published': 'bg-green-900 text-green-300',\n    'draft': 'bg-gray-700 text-gray-300',\n    'archived': 'bg-red-900 text-red-300'\n  }\n  return classMap[status] || 'bg-gray-700 text-gray-300'\n}\n\n// 内容类型映射\nconst getContentTypeText = (type: string) => {\n  const typeMap = {\n    'premium': '高级内容',\n    'basic': '基础内容',\n    'full': '完整内容'\n  }\n  return typeMap[type] || type\n}\n\nconst getContentTypeClass = (type: string) => {\n  const classMap = {\n    'premium': 'bg-yellow-600 text-yellow-100',\n    'basic': 'bg-blue-600 text-blue-100',\n    'full': 'bg-green-600 text-green-100'\n  }\n  return classMap[type] || 'bg-gray-600 text-gray-100'\n}\n\n// 配额类型映射\nconst getQuotaTypeText = (type: string) => {\n  const typeMap = {\n    'premium': '高级配额',\n    'basic': '基础配额',\n    'vip': 'VIP配额',\n    'recharge': '充值配额'\n  }\n  return typeMap[type] || type || '未知'\n}\n\n// VIP等级映射\nconst getVipLevelText = (level: number) => {\n  const levelMap = {\n    0: '普通用户',\n    1: '初级VIP',\n    2: 'VIP Pro'\n  }\n  return levelMap[level] || '未知'\n}\n\nconst getVipLevelClass = (level: number) => {\n  const classMap = {\n    0: 'bg-gray-700 text-gray-300',\n    1: 'bg-blue-700 text-blue-300',\n    2: 'bg-purple-700 text-purple-300'\n  }\n  return classMap[level] || 'bg-gray-700 text-gray-300'\n}\n\n// 消费方式映射\nconst getCostTypeText = (type: string) => {\n  const typeMap = {\n    '现金支付': '现金支付',\n    '积分支付': '积分支付',\n    '基础配额': '基础配额',\n    '高级配额': '高级配额',\n    '混合支付': '混合支付',\n    '自动赠送': '自动赠送',\n    'cash': '现金支付',\n    'quota': '配额使用',\n    'points': '积分支付',\n    'mixed': '混合支付',\n    'grant': '自动赠送',\n    'balance': '余额支付',\n    'unknown': '未知'\n  }\n  return typeMap[type] || type\n}\n\nconst getCostTypeClass = (type: string) => {\n  const classMap = {\n    '现金支付': 'bg-green-700 text-green-300',\n    '积分支付': 'bg-blue-700 text-blue-300',\n    '基础配额': 'bg-yellow-700 text-yellow-300',\n    '高级配额': 'bg-orange-700 text-orange-300',\n    '混合支付': 'bg-purple-700 text-purple-300',\n    '自动赠送': 'bg-gray-700 text-gray-300',\n    'cash': 'bg-green-700 text-green-300',\n    'quota': 'bg-yellow-700 text-yellow-300',\n    'points': 'bg-blue-700 text-blue-300',\n    'mixed': 'bg-purple-700 text-purple-300',\n    'grant': 'bg-orange-700 text-orange-300',\n    'balance': 'bg-teal-700 text-teal-300',\n    'unknown': 'bg-gray-700 text-gray-300'\n  }\n  return classMap[type] || 'bg-gray-700 text-gray-300'\n}\n\n// 格式化日期时间\nconst formatDateTime = (dateString: string) => {\n  if (!dateString) return '未知'\n  const date = new Date(dateString)\n  return date.toLocaleString('zh-CN', {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit'\n  })\n}\n\n// 加载购买者数据\nconst loadBuyers = async () => {\n  loading.value = true\n  error.value = ''\n\n  try {\n    // 调用真实API\n    const response = await articlesApi.getBuyers(articleId)\n\n    if (response.success) {\n      article.value = response.article\n      buyers.value = response.buyers || []\n      totalCount.value = response.total_count || 0\n    } else {\n      error.value = response.error || '加载购买者数据失败'\n    }\n  } catch (err) {\n    console.error('加载购买者失败:', err)\n    error.value = '网络错误，请稍后重试'\n  } finally {\n    loading.value = false\n  }\n}\n\n// 组件挂载时加载数据\nonMounted(() => {\n  if (articleId) {\n    loadBuyers()\n  } else {\n    error.value = '无效的文章ID'\n  }\n})\n</script>\n\n<style scoped>\n/* 复用管理后台的样式 */\n.admin-card {\n  @apply bg-gray-800 border border-gray-700 rounded-lg;\n}\n\n.admin-btn-primary {\n  @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium transition-colors;\n}\n\n.admin-btn-secondary {\n  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium transition-colors;\n}\n\n.table-admin {\n  @apply bg-gray-800;\n}\n\n.table-admin th {\n  @apply bg-gray-700 text-gray-300 font-medium;\n}\n\n.table-admin td {\n  @apply border-t border-gray-700;\n}\n</style>\n"}