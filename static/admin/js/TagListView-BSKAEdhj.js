var x=(a,r,m)=>new Promise((b,p)=>{var i=n=>{try{g(m.next(n))}catch(l){p(l)}},f=n=>{try{g(m.throw(n))}catch(l){p(l)}},g=n=>n.done?b(n.value):Promise.resolve(n.value).then(i,f);g((m=m.apply(a,r)).next())});import{h as c,d as P,i as w,m as T,j as q,k as G,c as j,a as e,e as H,g as M,t as u,n as J,p as v,x as S,F as K,l as Q,v as V,A as R,B as W,E as d,o as $}from"./index-Bo6OtMFR.js";import{E as A}from"./index-DU0nyBIP.js";import{_ as X}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DM_Oxv62.js";const y={getList(a){return c.get("/tags",{params:a})},getDetail(a){return c.get(`/tags/${a}`)},create(a){return c.post("/tags",a)},update(a,r){return c.put(`/tags/${a}`,r)},delete(a){return c.delete(`/tags/${a}`)},batchDelete(a){return c.post("/tags/batch_delete",{ids:a})},getStats(){return c.get("/tags/stats")},getTagArticles(a,r){return c.get(`/tags/${a}/articles`,{params:r})},getPopularTags(a=10){return c.get("/tags/popular",{params:{limit:a}})},mergeTags(a,r){return c.post("/tags/merge",{source_id:a,target_id:r})},getSuggestions(a){return c.get("/tags/suggestions",{params:{q:a}})}},Y={class:"tag-management"},Z={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ee={class:"admin-card p-4 bg-gradient-to-br from-blue-900 to-indigo-900 border border-blue-700"},te={class:"flex items-center justify-between"},se={class:"text-2xl font-bold text-white"},le={class:"admin-card p-4 bg-gradient-to-br from-green-900 to-emerald-900 border border-green-700"},oe={class:"flex items-center justify-between"},ae={class:"text-2xl font-bold text-white"},ne={class:"admin-card p-4 bg-gradient-to-br from-yellow-900 to-amber-900 border border-yellow-700"},re={class:"flex items-center justify-between"},ie={class:"text-2xl font-bold text-white"},de={class:"admin-card p-4 bg-gradient-to-br from-purple-900 to-indigo-900 border border-purple-700"},ce={class:"flex items-center justify-between"},ue={class:"text-2xl font-bold text-white"},pe={class:"admin-card p-4 mb-6"},ge={class:"mb-4 flex justify-start items-center"},me={class:"flex gap-2"},fe=["disabled"],be={class:"overflow-x-auto"},xe={class:"table-admin w-full"},ve={class:"px-4 py-2 text-left w-12"},ye={class:"px-4 py-3 text-center"},_e=["value"],he={class:"px-4 py-3"},we={class:"px-4 py-3"},ke={class:"px-4 py-3"},Ce={class:"px-4 py-3"},Te={class:"flex space-x-1"},je=["onClick","data-id","data-name"],Ve=["onClick","data-id"],$e={class:"pagination"},Be=["disabled"],De={class:"page-info"},Me=["disabled"],Se={class:"modal-header"},Ae={class:"modal-body"},Ee={class:"form-group"},Ue={class:"form-group"},Ie={class:"form-group"},Le={class:"color-picker"},Ne={class:"form-group"},Oe=P({__name:"TagListView",setup(a){const r=w([]),m=w(!1),b=w(!1),p=w(!1),i=w([]),f=T({total_tags:0,used_tags:0,unused_tags:0,most_used_tags:[]}),g=T({search:"",page:1,per_page:20}),n=T({page:1,pages:1,total:0,per_page:20}),l=T({id:null,name:"",description:"",color:"#3b82f6"}),B=q({get:()=>i.value.length===r.value.length&&r.value.length>0,set:s=>{s?i.value=r.value.map(t=>t.id):i.value=[]}}),E=s=>{if(!s)return"#000000";const t=s.replace("#",""),C=parseInt(t.substr(0,2),16),o=parseInt(t.substr(2,2),16),h=parseInt(t.substr(4,2),16);return(C*299+o*587+h*114)/1e3>128?"#000000":"#ffffff"},U=()=>x(this,null,function*(){try{const s=yield y.getStats();s.success&&Object.assign(f,s.data||s)}catch(s){console.error("获取标签统计失败:",s)}}),_=()=>x(this,null,function*(){m.value=!0;try{const s={page:g.page,per_page:g.per_page,search:g.search||void 0},t=yield y.getList(s);t.success?(r.value=t.tags||[],n.page=1,n.pages=1,n.total=r.value.length,n.per_page=r.value.length):d.error(t.message||"加载标签列表失败")}catch(s){console.error("加载标签失败:",s),d.error("加载标签列表失败")}finally{m.value=!1}}),I=()=>{},L=()=>x(this,null,function*(){if(i.value.length===0){d.warning("请至少选择一个标签进行删除");return}try{yield A.confirm(`确定要删除选中的 ${i.value.length} 个标签吗？此操作无法撤销。`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const s=yield y.batchDelete(i.value);s.success?(d.success("批量删除成功"),i.value=[],_()):d.error(s.message||"批量删除失败")}catch(s){s!=="cancel"&&(console.error("批量删除失败:",s),d.error("批量删除失败"))}}),N=()=>{p.value=!1,Object.assign(l,{id:null,name:"",description:"",color:"#3b82f6"}),b.value=!0},O=s=>{p.value=!0,Object.assign(l,s),b.value=!0},z=s=>x(this,null,function*(){try{yield A.confirm(`确定要删除标签"${s.name}"吗？此操作无法撤销。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const t=yield y.delete(s.id);t.success?(d.success("删除成功"),_()):d.error(t.message||"删除失败")}catch(t){t!=="cancel"&&(console.error("删除失败:",t),d.error("删除失败"))}}),F=()=>x(this,null,function*(){try{if(!l.name.trim()){d.warning("请输入标签名称");return}let s;p.value?s=yield y.update(l.id,{name:l.name,description:l.description,color:l.color}):s=yield y.create({name:l.name,description:l.description,color:l.color}),s.success?(d.success(p.value?"更新成功":"创建成功"),k(),_()):d.error(s.message||(p.value?"更新失败":"创建失败"))}catch(s){console.error("保存失败:",s),d.error(p.value?"更新失败":"创建失败")}}),k=()=>{b.value=!1},D=s=>{g.page=s,n.page=s,_()};return G(()=>{U(),_()}),(s,t)=>{var C;return $(),j("div",Y,[e("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[t[10]||(t[10]=e("h1",{class:"text-2xl font-bold text-white"},"标签列表",-1)),e("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[e("button",{onClick:N,class:"admin-btn-primary px-4 py-2 rounded-lg text-center"},t[9]||(t[9]=[e("i",{class:"fas fa-plus mr-2"},null,-1),M("添加标签 ",-1)]))])]),e("div",Z,[e("div",ee,[e("div",te,[e("div",null,[t[11]||(t[11]=e("h2",{class:"text-gray-300 mb-1"},"总标签数",-1)),e("p",se,u(f.total_tags),1)]),t[12]||(t[12]=e("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-blue-800 text-blue-200"},[e("i",{class:"fas fa-tags text-xl"})],-1))])]),e("div",le,[e("div",oe,[e("div",null,[t[13]||(t[13]=e("h2",{class:"text-gray-300 mb-1"},"已使用",-1)),e("p",ae,u(f.used_tags),1)]),t[14]||(t[14]=e("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-green-800 text-green-200"},[e("i",{class:"fas fa-check-circle text-xl"})],-1))])]),e("div",ne,[e("div",re,[e("div",null,[t[15]||(t[15]=e("h2",{class:"text-gray-300 mb-1"},"未使用",-1)),e("p",ie,u(f.unused_tags),1)]),t[16]||(t[16]=e("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-yellow-800 text-yellow-200"},[e("i",{class:"fas fa-exclamation-circle text-xl"})],-1))])]),e("div",de,[e("div",ce,[e("div",null,[t[17]||(t[17]=e("h2",{class:"text-gray-300 mb-1"},"热门标签",-1)),e("p",ue,u(((C=f.most_used_tags)==null?void 0:C.length)||0),1)]),t[18]||(t[18]=e("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-purple-800 text-purple-200"},[e("i",{class:"fas fa-fire text-xl"})],-1))])])]),e("div",pe,[e("div",ge,[e("div",me,[e("button",{id:"btn-batch-delete-tags",disabled:i.value.length===0,onClick:L,class:J(["admin-btn-danger px-4 py-2 rounded-lg text-center",{"opacity-50 cursor-not-allowed":i.value.length===0}])},t[19]||(t[19]=[e("i",{class:"fas fa-trash-alt mr-2"},null,-1),M(" 批量删除选中 ",-1)]),10,fe)])]),e("div",be,[e("table",xe,[e("thead",null,[e("tr",null,[e("th",ve,[v(e("input",{type:"checkbox",id:"select-all-tags","onUpdate:modelValue":t[0]||(t[0]=o=>B.value=o),onChange:I,class:"form-checkbox h-4 w-4 text-yellow-500 border-gray-500 rounded focus:ring-yellow-400 bg-gray-600"},null,544),[[S,B.value]])]),t[20]||(t[20]=e("th",{class:"px-4 py-2 text-left"},"ID",-1)),t[21]||(t[21]=e("th",{class:"px-4 py-2 text-left"},"标签名",-1)),t[22]||(t[22]=e("th",{class:"px-4 py-2 text-left"},"文章数量",-1)),t[23]||(t[23]=e("th",{class:"px-4 py-2 text-left"},"操作",-1))])]),e("tbody",null,[($(!0),j(K,null,Q(r.value,o=>($(),j("tr",{key:o.id,class:"hover:bg-gray-700/50 transition-colors"},[e("td",ye,[v(e("input",{type:"checkbox",value:o.id,"onUpdate:modelValue":t[1]||(t[1]=h=>i.value=h),class:"select-tag-item form-checkbox h-4 w-4 text-yellow-500 border-gray-500 rounded focus:ring-yellow-400 bg-gray-600"},null,8,_e),[[S,i.value]])]),e("td",he,u(o.id),1),e("td",we,u(o.name),1),e("td",ke,u(o.articles_count!==void 0?o.articles_count:"N/A"),1),e("td",Ce,[e("div",Te,[e("button",{onClick:h=>O(o),class:"admin-btn-secondary px-2 py-1 rounded-lg text-sm","data-id":o.id,"data-name":o.name,title:"编辑"},t[24]||(t[24]=[e("i",{class:"fas fa-edit"},null,-1)]),8,je),e("button",{onClick:h=>z(o),class:"admin-btn-danger px-2 py-1 rounded-lg text-sm","data-id":o.id,title:"删除"},t[25]||(t[25]=[e("i",{class:"fas fa-trash-alt"},null,-1)]),8,Ve)])])]))),128))])])]),e("div",$e,[e("button",{class:"btn-secondary",disabled:n.page<=1,onClick:t[2]||(t[2]=o=>D(n.page-1))}," 上一页 ",8,Be),e("span",De," 第 "+u(n.page)+" 页，共 "+u(n.pages)+" 页 ",1),e("button",{class:"btn-secondary",disabled:n.page>=n.pages,onClick:t[3]||(t[3]=o=>D(n.page+1))}," 下一页 ",8,Me)])]),b.value?($(),j("div",{key:0,class:"modal-overlay",onClick:k},[e("div",{class:"modal-content",onClick:t[8]||(t[8]=W(()=>{},["stop"]))},[e("div",Se,[e("h3",null,u(p.value?"编辑标签":"新增标签"),1),e("button",{class:"modal-close",onClick:k},t[26]||(t[26]=[e("i",{class:"fas fa-times"},null,-1)]))]),e("div",Ae,[e("div",Ee,[t[27]||(t[27]=e("label",null,"标签名称",-1)),v(e("input",{"onUpdate:modelValue":t[4]||(t[4]=o=>l.name=o),type:"text",placeholder:"请输入标签名称",class:"form-input"},null,512),[[V,l.name]])]),e("div",Ue,[t[28]||(t[28]=e("label",null,"描述",-1)),v(e("textarea",{"onUpdate:modelValue":t[5]||(t[5]=o=>l.description=o),placeholder:"请输入标签描述",class:"form-textarea",rows:"3"},null,512),[[V,l.description]])]),e("div",Ie,[t[29]||(t[29]=e("label",null,"颜色",-1)),e("div",Le,[v(e("input",{"onUpdate:modelValue":t[6]||(t[6]=o=>l.color=o),type:"color",class:"color-input"},null,512),[[V,l.color]]),v(e("input",{"onUpdate:modelValue":t[7]||(t[7]=o=>l.color=o),type:"text",placeholder:"#000000",class:"form-input color-text-input"},null,512),[[V,l.color]])])]),e("div",Ne,[t[30]||(t[30]=e("label",null,"预览",-1)),e("span",{class:"tag-preview",style:R({backgroundColor:l.color,color:E(l.color)})},u(l.name||"标签预览"),5)])]),e("div",{class:"modal-footer"},[e("button",{class:"btn-secondary",onClick:k},"取消"),e("button",{class:"btn-primary",onClick:F},"保存")])])])):H("",!0)])}}}),Je=X(Oe,[["__scopeId","data-v-90f0549b"]]);export{Je as default};
