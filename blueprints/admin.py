#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Admin blueprint for tarot website.
This module contains routes for the admin interface, including article management.
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_from_directory, session
from flask_login import login_required, current_user
from extensions import db
from models import Article, Tag, Author, ArticleErrorReport, User, ReadingPreference, SystemSetting, Purchase, Order, Subscription, UserFinance, MediaFile, SupportTicket, SupportMessage
from utils.decorators import admin_required, staff_required, customer_service_required, finance_required
import logging
from datetime import date, datetime # 确保导入在不上他那个逼样。
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash
import re
import os
import uuid

# 创建蓝图实例 - 使用admin作为蓝图名称，与模板中的url_for一致
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')
logger = logging.getLogger(__name__)

def auto_detect_language(text):
    """自动检测文本语言"""
    if not text or not text.strip():
        return None
    
    # 计算中文字符比例
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    total_chars = len(re.sub(r'\s+', '', text))  # 去除空白字符
    
    if total_chars == 0:
        return None
    
    chinese_ratio = chinese_chars / total_chars
    
    # 如果中文字符超过30%，认为是中文
    if chinese_ratio > 0.3:
        return 'zh'
    
    # 检测是否包含常见英文单词模式
    english_words = len(re.findall(r'\b[a-zA-Z]+\b', text))
    if english_words > 5:  # 包含超过5个英文单词
        return 'en'
    
    # 默认返回中文（因为这是中文为主的塔罗网站）
    return 'zh'

def auto_detect_publish_date(text, title):
    """自动检测发布日期"""
    # 优先使用当前日期
    current_date = date.today()
    
    # 可选：从标题或内容中提取日期信息
    # 这里先简单返回当前日期，后续可以加入更复杂的日期提取逻辑
    return current_date

# Vue3管理后台路由 (暂时禁用，恢复原版Flask模板)
# @admin_bp.route('/')
# @admin_bp.route('/<path:path>')
# @login_required
# @staff_required
# def vue_admin(path=''):
#     """Vue3管理后台 - SPA路由处理"""
#     try:
#         # 服务Vue3构建的index.html
#         return send_from_directory('static/admin', 'index.html')
#     except Exception as e:
#         logger.error(f"加载Vue3管理后台失败: {str(e)}")
#         return f"管理后台加载失败: {str(e)}", 500

# 恢复原版Flask模板管理后台
@admin_bp.route('/')
@login_required
@staff_required
def dashboard():
    """管理后台首页 - 原版Flask模板"""
    try:
        # 获取统计数据
        from datetime import datetime, timedelta

        # 用户统计
        total_users = User.query.count()
        new_users_today = User.query.filter(
            User.created_at >= datetime.now().date()
        ).count()

        # 文章统计
        total_articles = Article.query.count()
        published_articles = Article.query.filter_by(status='published').count()

        # VIP用户统计
        vip_users = User.query.filter(User.membership_type != 'free').count()

        # 最近注册用户
        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()

        # 最近发布文章
        recent_articles = Article.query.filter_by(status='published').order_by(
            Article.created_at.desc()
        ).limit(5).all()

        stats = {
            'user_count': total_users,
            'new_users_today': new_users_today,
            'article_count': total_articles,
            'published_articles': published_articles,
            'vip_users': vip_users
        }

        return render_template('admin/dashboard.html',
                             stats=stats,
                             recent_users=recent_users,
                             recent_articles=recent_articles,
                             now=datetime.now())
    except Exception as e:
        logger.error(f"管理后台首页加载失败: {str(e)}")
        flash(f'加载失败: {str(e)}', 'error')
        return render_template('admin/dashboard.html',
                             stats={},
                             recent_users=[],
                             recent_articles=[],
                             now=datetime.now())

# ==================== Vue3管理后台独立路由 ====================

# 静态资源路由 - 必须在SPA路由之前
@admin_bp.route('/js/<path:filename>')
def vue_admin_js(filename):
    """Vue3管理后台 - JS文件服务"""
    try:
        return send_from_directory('static/admin/js', filename)
    except Exception as e:
        logger.error(f"加载JS文件失败: {str(e)}")
        return f"JS文件加载失败: {str(e)}", 404

@admin_bp.route('/css/<path:filename>')
def vue_admin_css(filename):
    """Vue3管理后台 - CSS文件服务"""
    try:
        return send_from_directory('static/admin/css', filename)
    except Exception as e:
        logger.error(f"加载CSS文件失败: {str(e)}")
        return f"CSS文件加载失败: {str(e)}", 404





# SPA路由 - 处理所有其他路径
@admin_bp.route('/vue')
@admin_bp.route('/vue/')
@admin_bp.route('/vue/<path:path>')
@login_required
@staff_required
def vue_admin(path=''):
    """Vue3管理后台 - SPA路由处理"""
    try:
        # 服务Vue3构建的index.html
        return send_from_directory('static/admin', 'index.html')
    except Exception as e:
        logger.error(f"加载Vue3管理后台失败: {str(e)}")
        return f"管理后台加载失败: {str(e)}", 500

# ==================== 原版Flask模板路由 ====================

# 文章管理路由
@admin_bp.route('/articles')
@login_required
@admin_required
def articles():
    """文章管理页面"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int) # 允许通过查询参数改变每页数量，默认为10
    try:
        articles_pagination = Article.query.order_by(Article.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )
        return render_template('admin/articles.html', 
                               articles=articles_pagination.items, 
                               pagination=articles_pagination,
                               search=request.args.get('search', ''),
                               status=request.args.get('status', ''))
    except Exception as e:
        logger.error(f"加载文章管理页面失败: {str(e)}")
        flash('加载文章列表失败，请稍后再试。', 'error')
        return render_template('admin/articles.html', articles=[], pagination=None, search='', status='') # 传递空列表和None

# 删除文章API路由
@admin_bp.route('/api/articles/backup_delete/<int:article_id>', methods=['POST', 'DELETE'])
@login_required
@admin_required
def backup_delete_article(article_id):
    logger.critical(f"CRITICAL_ADMIN_DEBUG: backup_delete_article function was called for article ID: {article_id}")
    """备份并删除文章API - 简化版实现"""
    logger.info(f"删除文章API被调用，文章ID: {article_id}")
    try:
        article = Article.query.get_or_404(article_id)
        # 这里可以添加备份逻辑
        db.session.delete(article)
        db.session.commit()
        return jsonify({"success": True, "message": f"文章 {article_id} 已成功删除"})
    except Exception as e:
        logger.error(f"删除文章失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500
    
# 标签管理
@admin_bp.route('/tags')
@login_required
@staff_required
def tags():
    """标签管理页面"""
    # 初始加载时，仍然可以通过这里传递数据，或者前端完全通过API获取
    all_tags = Tag.query.order_by(Tag.name).all()
    return render_template('admin/tags.html', tags=all_tags)

# 创建标签 (旧的表单处理，将被新的API替代，暂时保留或后续移除)
@admin_bp.route('/tag/new', methods=['POST'])
@login_required
@staff_required
def create_tag():
    """创建新标签"""
    name = request.form.get('name')
    if not name:
        flash('标签名不能为空', 'error')
        return redirect(url_for('admin.tags'))
    
    existing_tag = Tag.query.filter_by(name=name).first()
    if existing_tag:
        flash(f'标签 "{name}" 已存在', 'error')
        return redirect(url_for('admin.tags'))
    
    tag = Tag(name=name)
    db.session.add(tag)
    try:
        db.session.commit()
        flash('标签创建成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'标签创建失败: {str(e)}', 'error')
    
    return redirect(url_for('admin.tags'))

# 删除标签 (旧的表单处理，将被新的API替代，暂时保留或后续移除)
@admin_bp.route('/tag/<int:tag_id>/delete', methods=['POST'])
@login_required
@staff_required
def delete_tag(tag_id):
    """删除标签"""
    tag = Tag.query.get_or_404(tag_id)
    try:
        # 在删除标签前，需要处理与文章的关联
        # article_tags 是关联表，SQLAlchemy 会自动处理多对多关系中关联表的记录删除
        # 如果有其他直接关联或需要特殊处理的逻辑，在这里添加
        
        db.session.delete(tag)
        db.session.commit()
        flash('标签删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'标签删除失败: {str(e)}', 'error')
    
    return redirect(url_for('admin.tags'))

# --- 新增标签管理API端点 ---

@admin_bp.route('/api/readers', methods=['GET'])
@login_required
@staff_required
def api_get_readers():
    """获取塔罗师列表 (API) - 用于文章编辑页面"""
    try:
        # 查询所有作者（塔罗师）
        readers_query = Author.query.order_by(Author.name).all()

        # 构建返回数据
        readers_data = []
        for reader in readers_query:
            readers_data.append({
                'id': reader.id,
                'name': reader.name,
                'email': getattr(reader, 'email', ''),
                'bio': reader.description or '',
                'avatar': getattr(reader, 'avatar', ''),
                'status': 'active',  # 默认状态
                'article_count': len(reader.articles) if hasattr(reader, 'articles') else 0
            })

        return jsonify({"success": True, "data": readers_data})
    except Exception as e:
        logger.error(f"获取塔罗师列表API失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@admin_bp.route('/api/tags', methods=['GET'])
@login_required
@staff_required
def api_get_tags():
    """获取所有标签 (API)"""
    try:
        tags_query = Tag.query.order_by(Tag.name).all()
        # 对于每个标签，计算文章数量
        # 这种方式会在循环中对每个标签执行一次 len(tag.articles)，可能有效率问题
        # 更优的方式是使用 sqlalchemy.func.count 和 group_by
        # from sqlalchemy import func
        # tags_with_counts = db.session.query(
        # Tag, func.count(article_tags.c.article_id).label('articles_count')
        # ).outerjoin(article_tags, Tag.id == article_tags.c.tag_id)\
        # .group_by(Tag.id).order_by(Tag.name).all()
        #
        # results = []
        # for tag, count in tags_with_counts:
        #     tag_data = tag.to_dict()
        #     tag_data['articles_count'] = count
        #     results.append(tag_data)
        # return jsonify({"success": True, "tags": results})

        # 暂时使用简单方式，后续可优化
        return jsonify({"success": True, "data": [tag.to_dict(include_article_count=True) for tag in tags_query]})
    except Exception as e:
        logger.error(f"获取标签列表API失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@admin_bp.route('/api/tags/stats', methods=['GET'])
@login_required
@staff_required
def api_get_tags_stats():
    """获取标签统计信息API"""
    try:
        from sqlalchemy import func

        # 基础统计
        total_tags = Tag.query.count()

        # 获取有文章的标签数量
        used_tags = db.session.query(Tag.id).join(
            Article.tags
        ).distinct().count()

        unused_tags = total_tags - used_tags

        # 最常用标签
        most_used_tags = db.session.query(
            Tag.name,
            func.count(Article.id).label('article_count')
        ).join(Article.tags)\
         .group_by(Tag.id, Tag.name)\
         .order_by(func.count(Article.id).desc())\
         .limit(10).all()

        most_used_tags_data = [
            {'name': tag.name, 'article_count': tag.article_count}
            for tag in most_used_tags
        ]

        # 标签使用分布
        tag_usage_stats = db.session.query(
            func.count(Article.id).label('article_count'),
            func.count(Tag.id).label('tag_count')
        ).select_from(Tag)\
         .outerjoin(Article.tags)\
         .group_by(Tag.id).all()

        # 统计不同使用范围的标签数量
        usage_ranges = {'1-5篇': 0, '6-10篇': 0, '11+篇': 0, '未使用': 0}
        for stat in tag_usage_stats:
            count = stat.article_count or 0
            if count == 0:
                usage_ranges['未使用'] += 1
            elif count <= 5:
                usage_ranges['1-5篇'] += 1
            elif count <= 10:
                usage_ranges['6-10篇'] += 1
            else:
                usage_ranges['11+篇'] += 1

        tag_usage_distribution = [
            {'range': range_name, 'count': count}
            for range_name, count in usage_ranges.items()
        ]

        stats = {
            'total_tags': total_tags,
            'used_tags': used_tags,
            'unused_tags': unused_tags,
            'most_used_tags': most_used_tags_data,
            'tag_usage_distribution': tag_usage_distribution
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取标签统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/tags', methods=['POST'])
@login_required
@staff_required
def api_create_tag():
    """创建新标签 (API)"""
    data = request.get_json()
    if not data or 'name' not in data or not data['name'].strip():
        return jsonify({"success": False, "error": "标签名不能为空"}), 400
    
    name = data['name'].strip()
    existing_tag = Tag.query.filter_by(name=name).first()
    if existing_tag:
        return jsonify({"success": False, "error": f"标签 '{name}' 已存在"}), 409 # 409 Conflict
        
    tag = Tag(name=name)
    db.session.add(tag)
    try:
        db.session.commit()
        return jsonify({"success": True, "message": "标签创建成功", "tag": tag.to_dict(include_article_count=True)}), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建标签API失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@admin_bp.route('/api/tags/<int:tag_id>', methods=['PUT'])
@login_required
@staff_required
def api_update_tag(tag_id):
    """更新标签 (API)"""
    tag = Tag.query.get_or_404(tag_id)
    data = request.get_json()
    
    if not data or 'name' not in data or not data['name'].strip():
        return jsonify({"success": False, "error": "标签名不能为空"}), 400
        
    new_name = data['name'].strip()
    if new_name != tag.name:
        existing_tag = Tag.query.filter(Tag.id != tag_id, Tag.name == new_name).first()
        if existing_tag:
            return jsonify({"success": False, "error": f"标签 '{new_name}' 已存在"}), 409 # 409 Conflict
        tag.name = new_name
        
    try:
        db.session.commit()
        return jsonify({"success": True, "message": "标签更新成功", "tag": tag.to_dict(include_article_count=True)})
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新标签API失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@admin_bp.route('/api/tags/<int:tag_id>', methods=['DELETE'])
@login_required
@staff_required
def api_delete_tag(tag_id):
    """删除标签 (API)"""
    tag = Tag.query.get_or_404(tag_id)
    try:
        # SQLAlchemy 会自动处理 article_tags 关联表中的记录
        db.session.delete(tag)
        db.session.commit()
        return jsonify({"success": True, "message": "标签删除成功"})
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除标签API失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@admin_bp.route('/api/tags/batch_delete', methods=['POST'])
@login_required
@staff_required
def api_batch_delete_tags():
    """批量删除标签 (API)"""
    data = request.get_json()
    if not data or 'ids' not in data or not isinstance(data['ids'], list):
        return jsonify({"success": False, "error": "无效的请求数据，需要一个包含ID列表的 'ids' 键。"}), 400

    ids_to_delete = data['ids']
    if not ids_to_delete:
        return jsonify({"success": False, "error": "没有提供要删除的标签ID。"}), 400

    deleted_count = 0
    errors = []

    for tag_id in ids_to_delete:
        try:
            tag_id_int = int(tag_id) # 确保是整数
            tag = Tag.query.get(tag_id_int)
            if tag:
                db.session.delete(tag)
                deleted_count += 1
            else:
                errors.append(f"ID {tag_id_int} 未找到对应的标签。")
        except ValueError:
            errors.append(f"无效的标签ID格式: '{tag_id}'")
        except Exception as e:
            # 捕获删除单个标签时可能发生的其他DB相关错误
            errors.append(f"删除ID {tag_id} 时发生错误: {str(e)}")
            logger.error(f"批量删除标签中，删除ID {tag_id} 失败: {str(e)}")
    
    if errors and deleted_count > 0:
        # 如果有部分成功部分失败，需要决定是否回滚
        # 为了简单起见，我们先提交成功的，并报告错误
        # 更严格的事务可以回滚所有： db.session.rollback(); return jsonify(...)
        try:
            db.session.commit()
            message = f"成功删除了 {deleted_count} 个标签。"
            if errors:
                message += " 但以下ID处理失败： " + "; ".join(errors)
            return jsonify({"success": True, "message": message, "errors": errors if errors else None})
        except Exception as e:
            db.session.rollback()
            logger.error(f"批量删除标签提交事务时失败: {str(e)}")
            return jsonify({"success": False, "error": f"提交批量删除操作时发生严重错误: {str(e)}", "details": errors}), 500
    elif errors: # 全部失败
        db.session.rollback() # 确保没有任何更改被提交
        return jsonify({"success": False, "error": "所有选中的标签都删除失败。", "details": errors}), 400
    elif deleted_count > 0: # 全部成功
        try:
            db.session.commit()
            return jsonify({"success": True, "message": f"成功删除了 {deleted_count} 个标签。"})
        except Exception as e:
            db.session.rollback()
            logger.error(f"批量删除标签提交事务时失败: {str(e)}")
            return jsonify({"success": False, "error": f"提交批量删除操作时发生严重错误: {str(e)}"}), 500
    else: # ids_to_delete 为空或所有ID都无效但未引发异常（例如，全是未找到的ID）
        return jsonify({"success": False, "error": "没有有效的标签被删除。可能所有提供的ID都无效。"}), 400

# 添加占位 uploader_dashboard 路由
@admin_bp.route('/uploader')
@login_required
@staff_required
def uploader_dashboard():
    """上传管理面板 (占位)"""
    flash('上传管理功能正在建设中...', 'info')
    return redirect(url_for('admin.dashboard'))

# 添加占位 create_article 路由
@admin_bp.route('/articles/create', methods=['GET', 'POST'])
@login_required
@staff_required
def create_article():
    """创建文章页面 (占位)"""
    # flash('创建新文章功能正在建设中...', 'info') # 改为直接渲染模板
    all_readers = Author.query.all()
    all_tags = Tag.query.order_by(Tag.name).all() # 新增：获取所有标签
    return render_template('admin/article_upload.html', 
                           article=None, 
                           all_readers=all_readers, 
                           all_tags=all_tags) # 新增：传递all_tags到模板

# 添加占位 edit_article 路由
@admin_bp.route('/articles/<int:article_id>/edit', methods=['GET', 'POST'])
@login_required
@staff_required
def edit_article(article_id):
    """编辑文章页面"""
    article = Article.query.get_or_404(article_id)
    all_readers = Author.query.order_by(Author.name).all() # 按名称排序
    all_tags = Tag.query.order_by(Tag.name).all() # 按名称排序
    # # 简单的表单处理示例 (如果需要POST请求)
    # if request.method == 'POST':
    #     # 处理表单提交逻辑
    #     # article.title = request.form.get('title')
    #     # db.session.commit()
    #     # flash('文章更新成功!', 'success')
    #     # return redirect(url_for('admin.articles'))
    #     pass # 暂时不处理POST，只关注GET请求渲染

    return render_template('admin/article_upload.html', article=article, all_readers=all_readers, all_tags=all_tags)

# --- 新增API端点 ---

@admin_bp.route('/api/articles', methods=['GET'])
@login_required
@admin_required
def api_get_articles():
    """获取文章列表API (管理员) - 支持通过作者名称或ID、标签名称或ID进行查询"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')
        status_filter = request.args.get('status', '')
        author_filter = request.args.get('author', '')  # 支持作者名称或ID
        tag_filter = request.args.get('tag', '')        # 支持标签名称或ID
        
        query = Article.query.order_by(Article.created_at.desc())
        
        # 搜索标题
        if search:
            query = query.filter(Article.title.ilike(f'%{search}%'))
        
        # 状态过滤
        if status_filter:
            query = query.filter(Article.status == status_filter)
        
        # 作者过滤（支持名称或ID）
        if author_filter:
            if author_filter.isdigit():
                # 按ID过滤（reader_id）
                query = query.filter(Article.reader_id == int(author_filter))
            else:
                # 按名称过滤，需要join Author表
                query = query.join(Author, Article.reader_id == Author.id).filter(Author.name.ilike(f'%{author_filter}%'))
        
        # 标签过滤（支持名称或ID）
        if tag_filter:
            if tag_filter.isdigit():
                # 按标签ID过滤
                query = query.join(Article.tags).filter(Tag.id == int(tag_filter))
            else:
                # 按标签名称过滤
                query = query.join(Article.tags).filter(Tag.name.ilike(f'%{tag_filter}%'))

        pagination = query.paginate(page=page, per_page=per_page, error_out=False)
        articles_data = []
        
        for article in pagination.items:
            # 使用增强的to_dict方法，返回完整的作者和标签信息
            article_dict = article.to_dict(
                include_content=False,
                include_premium_content=False,
                include_author_details=True,
                include_tag_details=True
            )
            articles_data.append(article_dict)
        
        return jsonify({
            "success": True,
            "articles": articles_data,
            "pagination": {
                "total": pagination.total,
                "page": pagination.page,
                "per_page": pagination.per_page,
                "pages": pagination.pages,
                "has_next": pagination.has_next,
                "has_prev": pagination.has_prev
            },
            "filters": {
                "search": search,
                "status": status_filter,
                "author": author_filter,
                "tag": tag_filter
            }
        })
    except Exception as e:
        logger.error(f"获取管理员文章列表失败: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'获取文章列表失败: {str(e)}'}), 500

@admin_bp.route('/api/articles/stats', methods=['GET'])
@login_required
@admin_required
def api_get_articles_stats():
    """获取文章统计信息API"""
    try:
        from datetime import datetime, timedelta

        # 基础统计
        total_articles = Article.query.count()
        published_articles = Article.query.filter(Article.status == 'published').count()
        draft_articles = Article.query.filter(Article.status == 'draft').count()
        pending_articles = Article.query.filter(Article.status == 'pending').count()

        # 时间统计
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        today_published = Article.query.filter(
            Article.status == 'published',
            Article.created_at >= today
        ).count()

        week_published = Article.query.filter(
            Article.status == 'published',
            Article.created_at >= week_ago
        ).count()

        month_published = Article.query.filter(
            Article.status == 'published',
            Article.created_at >= month_ago
        ).count()

        # 状态分布
        status_distribution = {
            'published': published_articles,
            'draft': draft_articles,
            'pending': pending_articles
        }

        # 作者分布
        author_distribution = []
        try:
            from sqlalchemy import func
            author_stats = db.session.query(
                Author.name,
                func.count(Article.id).label('count')
            ).join(Article, Article.reader_id == Author.id)\
             .group_by(Author.id, Author.name)\
             .order_by(func.count(Article.id).desc())\
             .limit(10).all()

            author_distribution = [
                {'name': stat.name, 'count': stat.count}
                for stat in author_stats
            ]
        except Exception as e:
            logger.warning(f"获取作者分布失败: {str(e)}")

        stats = {
            'total_articles': total_articles,
            'published_articles': published_articles,
            'draft_articles': draft_articles,
            'pending_articles': pending_articles,
            'today_published': today_published,
            'this_week_published': week_published,
            'this_month_published': month_published,
            'status_distribution': status_distribution,
            'author_distribution': author_distribution
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取文章统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/articles', methods=['POST'])
@login_required
@staff_required
def api_create_article():
    data = request.get_json()
    if not data:
        return jsonify({"success": False, "error": "无效的请求数据"}), 400

    title = data.get('title')
    content = data.get('content')
    status = data.get('status', 'draft')
    reader_id_str = data.get('reader_id') 
    premium_content = data.get('premium_content')
    content_type = data.get('content_type', 'normal')
    price_str = data.get('price')
    premium_price_str = data.get('premium_price')
    cover_url = data.get('cover_url')

    # 获取其他可选字段
    publish_date_str = data.get('publish_date')
    language_code = data.get('language_code')
    tags_data = data.get('tags', []) # 可以是ID数组或名称数组
    author_id = data.get('author_id') # 新增：支持直接传递author_id
    premium_content_chinese = data.get('premium_content_chinese')  # 新增：高级翻译纯中文内容
    original_id = data.get('original_id')  # 新增：YouTube视频ID

    if not title:
        return jsonify({"success": False, "error": "文章标题不能为空"}), 400

    # 检查original_id是否重复
    if original_id:
        existing_article = Article.query.filter_by(original_id=original_id).first()
        if existing_article:
            return jsonify({"success": False, "error": f"Article with original_id '{original_id}' already exists (ID: {existing_article.id})"}), 409

    try:
        # 改进：支持塔罗师名称和ID
        reader_id = None
        if reader_id_str and str(reader_id_str).strip() not in ["", "None", "null"]:
            # 尝试作为ID处理
            if str(reader_id_str).isdigit():
                reader_id = int(reader_id_str)
            else:
                # 作为名称处理，查找对应的塔罗师
                author_by_name = Author.query.filter_by(name=str(reader_id_str).strip()).first()
                if author_by_name:
                    reader_id = author_by_name.id
                    logger.info(f"通过名称 '{reader_id_str}' 找到塔罗师ID: {reader_id}")
                else:
                    logger.warning(f"未找到名称为 '{reader_id_str}' 的塔罗师")
        
        # 改进：支持传递author_id，如果没有则使用当前用户
        final_author_id = author_id if author_id else current_user.id
        
        # 智能处理语言代码
        final_language_code = None
        if language_code and language_code.strip():
            final_language_code = language_code
            logger.info(f"使用用户提供的语言代码: {language_code}")
        else:
            # 自动检测语言
            detected_language = auto_detect_language(f"{title} {content}")
            if detected_language:
                final_language_code = detected_language
                logger.info(f"自动检测语言为: {detected_language}")
        
        new_article = Article(
            title=title,
            content=content,
            status=status,
            reader_id=None, # 先设为None，后续验证后再赋值
            author_id=final_author_id,
            premium_content=premium_content,
            premium_content_chinese=premium_content_chinese,  # 新增：高级翻译纯中文内容
            original_id=original_id,  # 新增：YouTube视频ID
            content_type=content_type,
            has_premium=bool(premium_content and premium_content.strip()),
            cover_url=cover_url,
            language_code=final_language_code
        )

        # 智能处理发布日期
        if publish_date_str and publish_date_str.strip():
            try:
                new_article.publish_date = date.fromisoformat(publish_date_str)
                logger.info(f"使用用户提供的发布日期: {publish_date_str}")
            except ValueError:
                logger.warning(f"创建文章时，提供的发布日期格式无效: '{publish_date_str}'，将使用自动检测")
                new_article.publish_date = auto_detect_publish_date(content, title)
        else:
            # 自动检测发布日期
            new_article.publish_date = auto_detect_publish_date(content, title)
            logger.info(f"自动设置发布日期为: {new_article.publish_date}")

        # 设置价格：如果提供了价格则使用，否则使用统一默认价格
        if price_str is not None:
            try:
                new_article.price = int(float(price_str) * 100)
            except ValueError:
                return jsonify({"success": False, "error": "基础内容价格格式无效"}), 400
        else:
            # 使用统一的默认价格：¥12.00
            new_article.price = 1200

        if premium_price_str is not None:
            try:
                new_article.premium_price = int(float(premium_price_str) * 100)
            except ValueError:
                return jsonify({"success": False, "error": "高级内容价格格式无效"}), 400
        else:
            # 使用统一的默认价格：¥20.00
            new_article.premium_price = 2000
        
        # 验证并设置塔罗师ID
        if reader_id:
            author_exists = Author.query.get(reader_id)
            if author_exists:
                new_article.reader_id = reader_id
            else:
                logger.warning(f"创建文章时，尝试关联无效的塔罗师ID: {reader_id} 到新文章 '{title}'")

        # 改进：处理标签关联 - 支持ID和名称混合
        if tags_data:
            for tag_item in tags_data:
                tag = None
                
                # 判断是ID还是名称
                if isinstance(tag_item, int) or (isinstance(tag_item, str) and tag_item.isdigit()):
                    # 作为ID处理
                    tag_id = int(tag_item)
                    tag = Tag.query.get(tag_id)
                    if not tag:
                        logger.warning(f"创建文章 '{title}' 时，标签ID {tag_id} 不存在")
                elif isinstance(tag_item, str):
                    # 作为名称处理
                    tag_name = tag_item.strip()
                    if tag_name:
                        tag = Tag.query.filter_by(name=tag_name).first()
                        if not tag:
                            # 自动创建新标签
                            tag = Tag(name=tag_name)
                            db.session.add(tag)
                            logger.info(f"自动创建新标签: '{tag_name}'")
                
                if tag and tag not in new_article.tags:
                    new_article.tags.append(tag)

        db.session.add(new_article)
        db.session.flush() # 获取 new_article.id 用于标签关联

        db.session.commit()
        return jsonify({"success": True, "message": "文章创建成功", "article": new_article.to_dict(include_content=False, include_premium_content=True, include_author_details=False, include_tag_details=True)}), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建文章API失败: {str(e)}")
        return jsonify({"success": False, "error": f"创建文章失败: {str(e)}"}), 500

@admin_bp.route('/api/articles/<int:article_id>', methods=['PUT'])
@login_required
@staff_required
def api_update_article(article_id):
    article = Article.query.get_or_404(article_id)
    data = request.get_json()

    if not data:
        return jsonify({"success": False, "error": "无效的请求数据"}), 400

    # 根据前端JS的逻辑，它可能会为不同表单的保存发送不同的payload
    # 例如，一个payload只包含title和content，另一个只包含status和reader_id
    
    if 'title' in data:
        article.title = data.get('title', article.title)
    if 'content' in data:
        article.content = data.get('content', article.content)

    if 'premium_content' in data:
        article.premium_content = data.get('premium_content', article.premium_content)
        article.has_premium = bool(article.premium_content and article.premium_content.strip())

    if 'status' in data:
        article.status = data.get('status', article.status)
    
    # 改进：支持塔罗师名称和ID
    if 'reader_id' in data:
        reader_id_str = data.get('reader_id')
        if reader_id_str is None or reader_id_str == "" or str(reader_id_str).lower() == 'none':
            article.reader_id = None
        else:
            reader_id_val = None
            
            # 判断是ID还是名称
            if str(reader_id_str).isdigit():
                # 作为ID处理
                reader_id_val = int(reader_id_str)
                if reader_id_val == 0:
                     article.reader_id = None
                else:
                    author_exists = Author.query.get(reader_id_val)
                    if author_exists:
                        article.reader_id = reader_id_val
                    else:
                        logger.warning(f"更新文章 {article_id} 时，塔罗师ID {reader_id_val} 不存在")
                        article.reader_id = None
            else:
                # 作为名称处理
                author_by_name = Author.query.filter_by(name=str(reader_id_str).strip()).first()
                if author_by_name:
                    article.reader_id = author_by_name.id
                    logger.info(f"更新文章 {article_id} 时，通过名称 '{reader_id_str}' 找到塔罗师ID: {author_by_name.id}")
                else:
                    logger.warning(f"更新文章 {article_id} 时，未找到名称为 '{reader_id_str}' 的塔罗师")
                    article.reader_id = None
    
    if 'content_type' in data:
        article.content_type = data.get('content_type', article.content_type)

    if 'price' in data:
        price_str = data.get('price')
        if price_str is not None:
            try:
                 article.price = int(float(price_str) * 100)
            except ValueError:
                return jsonify({"success": False, "error": "基础内容价格格式无效"}), 400
    
    if 'premium_price' in data:
        premium_price_str = data.get('premium_price')
        if premium_price_str is not None:
            try:
                 article.premium_price = int(float(premium_price_str) * 100)
            except ValueError:
                return jsonify({"success": False, "error": "高级内容价格格式无效"}), 400
            
    if 'cover_url' in data:
        article.cover_url = data.get('cover_url', article.cover_url)
        # 如果URL被清空，也清空本地上传的封面记录（如果模型中有相关字段）
        # if not article.cover_url or not article.cover_url.strip():
        #     article.cover_image = None 
            
    # 智能处理发布日期
    if 'publish_date' in data:
        publish_date_str = data.get('publish_date')
        if publish_date_str and publish_date_str.strip():
            try:
                article.publish_date = date.fromisoformat(publish_date_str)
                logger.info(f"更新文章 {article_id}，使用用户提供的发布日期: {publish_date_str}")
            except ValueError:
                logger.warning(f"更新文章 {article_id} 时，提供的发布日期格式无效: '{publish_date_str}'，将使用自动检测")
                article.publish_date = auto_detect_publish_date(data.get('content', article.content), data.get('title', article.title))
        else:
            # 如果传来空字符串，进行自动检测而不是清空
            if publish_date_str == "":
                article.publish_date = auto_detect_publish_date(data.get('content', article.content), data.get('title', article.title))
                logger.info(f"更新文章 {article_id}，自动设置发布日期为: {article.publish_date}")
            else:
                article.publish_date = None # 只有明确传null才清空

    # 智能处理语言代码
    if 'language_code' in data:
        language_code = data.get('language_code')
        if language_code and language_code.strip():
            article.language_code = language_code
            logger.info(f"更新文章 {article_id}，使用用户提供的语言代码: {language_code}")
        else:
            # 如果没有提供语言代码，自动检测
            content_for_detection = data.get('content', article.content)
            title_for_detection = data.get('title', article.title)
            detected_language = auto_detect_language(f"{title_for_detection} {content_for_detection}")
            if detected_language:
                article.language_code = detected_language
                logger.info(f"更新文章 {article_id}，自动检测语言为: {detected_language}")
            else:
                article.language_code = None

    # 改进：处理标签 - 支持ID和名称混合
    if 'tags' in data:
        tags_data = data.get('tags', [])
        article.tags.clear() # 先清除旧的标签关联
        if tags_data:
            for tag_item in tags_data:
                tag = None
                
                # 判断是ID还是名称
                if isinstance(tag_item, int) or (isinstance(tag_item, str) and tag_item.isdigit()):
                    # 作为ID处理
                    tag_id = int(tag_item)
                    tag = Tag.query.get(tag_id)
                    if not tag:
                        logger.warning(f"更新文章 {article_id} 时，标签ID {tag_id} 不存在")
                elif isinstance(tag_item, str):
                    # 作为名称处理
                    tag_name = tag_item.strip()
                    if tag_name:
                        tag = Tag.query.filter_by(name=tag_name).first()
                        if not tag:
                            # 自动创建新标签
                            tag = Tag(name=tag_name)
                            db.session.add(tag)
                            logger.info(f"更新文章 {article_id} 时，自动创建新标签: '{tag_name}'")
                
                if tag and tag not in article.tags:
                        article.tags.append(tag)
            
    try:
        db.session.commit()
        return jsonify({"success": True, "message": "文章更新成功", "article": article.to_dict(include_content=True, include_premium_content=True, include_author_details=True, include_tag_details=True)})
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新文章 {article_id} API失败: {str(e)}")
        return jsonify({"success": False, "error": f"更新文章失败: {str(e)}"}), 500

@admin_bp.route('/api/articles/<int:article_id>/premium', methods=['PUT'])
@login_required
@staff_required
def update_article_premium_content(article_id):
    """更新文章高级内容"""
    logger.info(f"收到更新文章高级内容请求，文章ID: {article_id}")
    article = Article.query.get_or_404(article_id)
    data = request.get_json()
    logger.info(f"请求数据: {data}")

    if not data:
        logger.warning(f"更新文章 {article_id} 高级内容失败: 无效的请求数据")
        return jsonify({"success": False, "error": "无效的请求数据"}), 400

    # 更新高级内容
    premium_content = data.get('premium_content')
    logger.info(f"更新文章 {article_id} 高级内容，内容长度: {len(str(premium_content)) if premium_content else 0}")
    
    # 保存原始值用于对比
    original_premium_content = article.premium_content
    original_has_premium = article.has_premium
    
    article.premium_content = premium_content
    article.has_premium = bool(premium_content and premium_content.strip())
    
    # 记录变化
    logger.info(f"高级内容变化: '{original_premium_content or 'None'}' -> '{premium_content or 'None'}'")
    logger.info(f"has_premium变化: {original_has_premium} -> {article.has_premium}")

    try:
        db.session.commit()
        logger.info(f"更新文章 {article_id} 高级内容成功")
        result = article.to_dict(include_content=True, include_premium_content=True, include_author_details=True, include_tag_details=True)
        logger.info(f"返回数据: has_premium={result.get('has_premium')}, premium_content长度={len(result.get('premium_content', '')) if result.get('premium_content') else 0}")
        return jsonify({"success": True, "message": "高级内容更新成功", "article": result})
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新文章 {article_id} 高级内容失败: {str(e)}")
        return jsonify({"success": False, "error": f"更新高级内容失败: {str(e)}"}), 500

# --- 结束高级内容API ---

@admin_bp.route('/api/articles/<int:article_id>/price', methods=['PUT'])
@login_required
@finance_required
def update_article_price(article_id):
    """更新文章价格设置"""
    article = Article.query.get_or_404(article_id)
    data = request.get_json()

    if not data:
        return jsonify({"success": False, "error": "无效的请求数据"}), 400

    if 'price' in data:
        try:
            article.price = int(data['price'])
        except (ValueError, TypeError):
            return jsonify({"success": False, "error": "基础内容价格格式无效"}), 400

    if 'premium_price' in data:
        try:
            article.premium_price = int(data['premium_price'])
        except (ValueError, TypeError):
            return jsonify({"success": False, "error": "高级内容价格格式无效"}), 400

    try:
        db.session.commit()
        return jsonify({"success": True, "message": "价格设置更新成功", "article": article.to_dict(include_author_details=True, include_tag_details=True)})
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新文章 {article_id} 价格失败: {str(e)}")
        return jsonify({"success": False, "error": f"更新价格失败: {str(e)}"}), 500

@admin_bp.route('/api/articles/<int:article_id>/cover-url', methods=['PUT'])
@login_required
@staff_required
def update_article_cover_url(article_id):
    """更新文章封面URL"""
    article = Article.query.get_or_404(article_id)
    data = request.get_json()

    if not data or 'cover_url' not in data:
        return jsonify({"success": False, "error": "无效的请求数据，需要提供cover_url"}), 400

    article.cover_url = data['cover_url']

    try:
        db.session.commit()
        return jsonify({"success": True, "message": "封面URL更新成功", "article": article.to_dict(include_author_details=True, include_tag_details=True)})
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新文章 {article_id} 封面URL失败: {str(e)}")
        return jsonify({"success": False, "error": f"更新封面URL失败: {str(e)}"}), 500

@admin_bp.route('/api/articles/<int:article_id>/cover', methods=['DELETE'])
@login_required
@staff_required
def delete_article_cover(article_id):
    """删除文章封面"""
    article = Article.query.get_or_404(article_id)

    # 清空封面相关字段
    article.cover_url = None
    # 如果有本地存储的封面图片，这里也需要清空
    # article.cover_image = None

    try:
        db.session.commit()
        return jsonify({"success": True, "message": "封面已移除"})
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除文章 {article_id} 封面失败: {str(e)}")
        return jsonify({"success": False, "error": f"移除封面失败: {str(e)}"}), 500

# --- 塔罗师管理 (Authors) ---
@admin_bp.route('/authors')
@login_required
@admin_required
def authors():
    """塔罗师管理页面"""
    return render_template('admin/authors.html')

@admin_bp.route('/api/authors', methods=['GET'])
@login_required
@admin_required
def api_get_authors():
    """获取塔罗师列表API - 支持分页和搜索"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')

        query = Author.query

        # 搜索过滤
        if search:
            query = query.filter(Author.name.ilike(f'%{search}%'))

        # 分页查询
        pagination = query.order_by(Author.name).paginate(
            page=page, per_page=per_page, error_out=False
        )

        authors_data = []
        for author in pagination.items:
            author_dict = author.to_dict(include_article_count=True)
            authors_data.append(author_dict)

        return jsonify({
            "success": True,
            "data": {
                "data": authors_data,
                "pagination": {
                    "page": pagination.page,
                    "pages": pagination.pages,
                    "per_page": pagination.per_page,
                    "total": pagination.total,
                    "has_prev": pagination.has_prev,
                    "has_next": pagination.has_next
                }
            }
        })
    except Exception as e:
        logger.error(f"获取塔罗师列表API失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@admin_bp.route('/api/authors', methods=['POST'])
@login_required
@admin_required
def api_create_author():
    """创建塔罗师API"""
    try:
        data = request.get_json()
        if not data or 'name' not in data:
            return jsonify({'success': False, 'error': '缺少必要参数name'}), 400

        name = data['name'].strip()
        if not name:
            return jsonify({'success': False, 'error': '塔罗师名称不能为空'}), 400

        # 检查是否已存在同名塔罗师
        existing_author = Author.query.filter_by(name=name).first()
        if existing_author:
            return jsonify({'success': False, 'error': f'塔罗师 "{name}" 已存在'}), 409

        # 创建新塔罗师
        author = Author(
            name=name,
            description=data.get('description', ''),
            specialty=data.get('specialty', '塔罗占卜'),
            experience_years=data.get('experience_years', 0),
            contact_info=data.get('contact_info', ''),
            is_active=data.get('is_active', True)
        )

        db.session.add(author)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '塔罗师创建成功',
            'data': author.to_dict(include_article_count=True)
        }), 201

    except Exception as e:
        db.session.rollback()
        logger.error(f"创建塔罗师API失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/authors/<int:author_id>', methods=['GET'])
@login_required
@admin_required
def api_get_author(author_id):
    """获取塔罗师详情API"""
    try:
        author = Author.query.get_or_404(author_id)
        return jsonify({
            'success': True,
            'data': author.to_dict(include_article_count=True)
        })
    except Exception as e:
        logger.error(f"获取塔罗师详情API失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/authors/<int:author_id>', methods=['PUT'])
@login_required
@admin_required
def api_update_author(author_id):
    """更新塔罗师API"""
    try:
        author = Author.query.get_or_404(author_id)
        data = request.get_json()

        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 更新名称
        if 'name' in data:
            new_name = data['name'].strip()
            if not new_name:
                return jsonify({'success': False, 'error': '塔罗师名称不能为空'}), 400

            # 检查名称是否与其他塔罗师冲突
            if new_name != author.name:
                existing_author = Author.query.filter(
                    Author.id != author_id,
                    Author.name == new_name
                ).first()
                if existing_author:
                    return jsonify({'success': False, 'error': f'塔罗师 "{new_name}" 已存在'}), 409

            author.name = new_name

        # 更新其他字段
        if 'description' in data:
            author.description = data['description']
        if 'specialty' in data:
            author.specialty = data['specialty']
        if 'experience_years' in data:
            author.experience_years = data['experience_years']
        if 'contact_info' in data:
            author.contact_info = data['contact_info']
        if 'is_active' in data:
            author.is_active = data['is_active']

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '塔罗师更新成功',
            'data': author.to_dict(include_article_count=True)
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"更新塔罗师API失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/authors/<int:author_id>', methods=['DELETE'])
@login_required
@admin_required
def api_delete_author(author_id):
    """删除塔罗师API"""
    try:
        author = Author.query.get_or_404(author_id)

        # 检查是否有关联的文章
        if hasattr(author, 'articles') and author.articles:
            return jsonify({
                'success': False,
                'error': f'无法删除塔罗师 "{author.name}"，因为还有 {len(author.articles)} 篇关联文章'
            }), 400

        db.session.delete(author)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '塔罗师删除成功'
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"删除塔罗师API失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/authors/batch_delete', methods=['POST'])
@login_required
@admin_required
def api_batch_delete_authors():
    """批量删除塔罗师API"""
    try:
        data = request.get_json()
        if not data or 'ids' not in data or not isinstance(data['ids'], list):
            return jsonify({'success': False, 'error': '无效的请求数据，需要一个包含ID列表的 "ids" 键'}), 400

        ids_to_delete = data['ids']
        if not ids_to_delete:
            return jsonify({'success': False, 'error': '没有提供要删除的塔罗师ID'}), 400

        # 查找要删除的塔罗师
        authors_to_delete = Author.query.filter(Author.id.in_(ids_to_delete)).all()

        if not authors_to_delete:
            return jsonify({'success': False, 'error': '没有找到要删除的塔罗师'}), 404

        # 检查是否有关联的文章
        authors_with_articles = []
        for author in authors_to_delete:
            if hasattr(author, 'articles') and author.articles:
                authors_with_articles.append(f'{author.name}({len(author.articles)}篇文章)')

        if authors_with_articles:
            return jsonify({
                'success': False,
                'error': f'以下塔罗师还有关联文章，无法删除：{", ".join(authors_with_articles)}'
            }), 400

        # 执行删除
        for author in authors_to_delete:
            db.session.delete(author)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功删除 {len(authors_to_delete)} 个塔罗师'
        })

    except Exception as e:
        db.session.rollback()
        logger.error(f"批量删除塔罗师API失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/authors/stats', methods=['GET'])
@login_required
@admin_required
def api_get_authors_stats():
    """获取塔罗师统计信息API"""
    try:
        from sqlalchemy import func

        # 基础统计
        total_authors = Author.query.count()
        featured_authors = Author.query.filter(Author.featured == True).count()

        # 有文章的塔罗师数量
        active_authors = db.session.query(Author.id).join(
            Article, Article.reader_id == Author.id
        ).distinct().count()

        # 总文章数
        total_articles_by_authors = Article.query.filter(
            Article.reader_id.isnot(None)
        ).count()

        # 平均每个塔罗师的文章数
        avg_articles_per_author = (
            total_articles_by_authors / total_authors
            if total_authors > 0 else 0
        )

        # 顶级塔罗师（按文章数排序）
        top_authors = db.session.query(
            Author.name,
            func.count(Article.id).label('article_count'),
            func.sum(Article.view_count).label('total_views')
        ).join(Article, Article.reader_id == Author.id)\
         .group_by(Author.id, Author.name)\
         .order_by(func.count(Article.id).desc())\
         .limit(10).all()

        top_authors_data = []
        for author in top_authors:
            top_authors_data.append({
                'name': author.name,
                'article_count': author.article_count,
                'total_views': author.total_views or 0
            })

        # 塔罗师表现数据
        author_performance = []
        for author in top_authors:
            # 计算平均评分（这里使用模拟数据，实际应该从评分表获取）
            rating = 4.5 + (hash(author.name) % 6) / 10  # 模拟评分 4.5-5.0
            author_performance.append({
                'name': author.name,
                'articles': author.article_count,
                'views': author.total_views or 0,
                'rating': round(rating, 1)
            })

        stats = {
            'total_authors': total_authors,
            'active_authors': active_authors,
            'featured_authors': featured_authors,
            'total_articles_by_authors': total_articles_by_authors,
            'avg_articles_per_author': round(avg_articles_per_author, 1),
            'top_authors': top_authors_data,
            'author_performance': author_performance
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取塔罗师统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500







@admin_bp.route('/api/article-buyers/<int:article_id>', methods=['GET'])
@login_required
@admin_required
def get_article_buyers(article_id):
    """获取文章购买者列表API"""
    try:
        # 获取文章信息
        article = Article.query.get_or_404(article_id)

        # 获取购买记录 - 只查询已完成的购买
        purchases = Purchase.query.filter_by(
            article_id=article_id,
            status='completed'
        ).all()

        # 如果没有购买记录，返回空列表
        if not purchases:
            return jsonify({
                'success': True,
                'article': {
                    'id': article.id,
                    'title': article.title,
                    'status': article.status,
                    'created_at': article.created_at.isoformat() if article.created_at else None
                },
                'buyers': [],
                'total_count': 0
            })

        # 整理购买者信息 - 基于优化后的Purchase表
        buyers = []
        for purchase in purchases:
            # 获取用户信息
            user = User.query.get(purchase.user_id) if purchase.user_id else None

            # 获取支付摘要（兼容当前数据库结构）
            try:
                payment_summary = purchase.get_payment_summary()
            except:
                # 如果新字段不存在，使用默认值
                payment_summary = {
                    'composition': 'unknown',
                    'cash': {'amount': 0, 'currency': 'CNY', 'percentage': 0},
                    'points': {'amount': 0, 'value': 0, 'rate': 0, 'percentage': 0},
                    'quota': {'amount': 0, 'type': '', 'value': 0, 'percentage': 0},
                    'total': {'value': 0, 'paid': 0, 'saved': 0}
                }

            # 查找积分使用记录（用于获取余额变化信息）
            points_record = None
            points_balance_before = None
            points_balance_after = None

            if purchase.user_id and purchase.article_id and purchase.points_used > 0:
                points_record = PointsRecord.query.filter_by(
                    user_id=purchase.user_id,
                    type='used',
                    order_id=f'ART{purchase.article_id}'
                ).first()

                if points_record and hasattr(points_record, 'balance_before'):
                    points_balance_before = points_record.balance_before
                    points_balance_after = points_record.balance_after

            # 获取用户当前财务状态
            user_finance = UserFinance.query.get(purchase.user_id) if purchase.user_id else None

            # 安全获取字段值（兼容当前数据库结构）
            def safe_get_attr(obj, attr, default=0):
                try:
                    return getattr(obj, attr, default)
                except:
                    return default

            # 计算基本信息
            cash_amount = safe_get_attr(purchase, 'cash_amount', purchase.amount or 0)
            points_used = safe_get_attr(purchase, 'points_used', 0)
            points_value = safe_get_attr(purchase, 'points_value', 0)
            quota_used = safe_get_attr(purchase, 'quota_used', 0)
            quota_type = safe_get_attr(purchase, 'quota_type', None)
            if not quota_type:
                quota_type = purchase.content_type
            quota_value = safe_get_attr(purchase, 'quota_value', 0)
            total_value = safe_get_attr(purchase, 'total_value', cash_amount + points_value + quota_value)
            total_amount = safe_get_attr(purchase, 'total_amount', cash_amount)
            payment_composition = safe_get_attr(purchase, 'payment_composition', 'unknown')

            # 如果没有配额价值但有配额使用，计算配额价值（以分为单位）
            if quota_value == 0 and quota_used > 0:
                if purchase.content_type == 'premium':
                    quota_value = quota_used * 2999  # 高级内容29.99元 = 2999分
                else:
                    quota_value = quota_used * 999   # 基础内容9.99元 = 999分
                total_value = cash_amount + points_value + quota_value

            # 如果没有任何消费但是基础内容，设置默认价值
            if total_value == 0 and purchase.content_type == 'basic':
                total_value = 999  # 基础内容默认9.99元
            elif total_value == 0 and purchase.content_type == 'premium':
                total_value = 2999  # 高级内容默认29.99元

            # 确定支付构成和用户状态
            if payment_composition == 'unknown':
                if quota_used > 0 and cash_amount == 0 and points_used == 0:
                    # 纯配额支付
                    if purchase.content_type == 'premium':
                        payment_composition = '高级配额'
                    else:
                        payment_composition = '基础配额'
                elif points_used > 0 and quota_used == 0 and cash_amount == 0:
                    payment_composition = '积分支付'
                elif cash_amount > 0 and quota_used == 0 and points_used == 0:
                    payment_composition = '现金支付'
                elif quota_used > 0 and (cash_amount > 0 or points_used > 0):
                    payment_composition = '混合支付'
                elif purchase.content_type == 'basic' and quota_used == 0 and cash_amount == 0 and points_used == 0:
                    payment_composition = '自动赠送'
                else:
                    payment_composition = '未知'

            # 确定获得方式
            acquisition_method = '购买获得'
            if quota_used > 0 and cash_amount == 0 and points_used == 0:
                acquisition_method = '配额消耗'
            elif user and hasattr(user, 'is_admin') and user.is_admin:
                acquisition_method = '管理员权限'

            # 计算节省金额（仅在有实际支付时显示）
            total_saved = points_value + quota_value
            savings_percentage = round(total_saved / total_value * 100, 2) if total_value > 0 else 0

            # 获取用户VIP等级
            user_vip_level = 0
            if user_finance:
                user_vip_level = getattr(user_finance, 'vip_level', 0)

            buyer = {
                # 基础信息
                'purchase_id': purchase.id,
                'user_id': purchase.user_id,
                'username': user.username if user else '未知用户',
                'email': user.email if user and user.email else '未提供',
                'content_type': purchase.content_type,
                'purchase_time': purchase.purchase_date.isoformat() if purchase.purchase_date else None,

                # 支付信息（简化显示）
                'amount_paid': cash_amount / 100 if cash_amount else 0,  # 转换为元
                'payment_method': purchase.payment_method or '未知',
                'transaction_id': purchase.transaction_id or '',

                # 积分消费详情（仅在有积分消费时显示）
                'points_used': points_used if points_used > 0 else 0,
                'points_balance_before': points_balance_before,
                'points_balance_after': points_balance_after,
                'points_record_id': points_record.id if points_record else None,

                # 配额消费详情（仅在有配额消费时显示）
                'quota_used': quota_used if quota_used > 0 else 0,
                'quota_type': quota_type if quota_used > 0 else '',
                'exchange_used': quota_used > 0,
                'quota_record_id': None,

                # 购买时用户状态
                'vip_level_at_purchase': user_vip_level,

                # 消费方式分析（简化）
                'cost_type': payment_composition,
                'cost_amount': total_value / 100 if total_value else 0,  # 转换为元
                'consumption_record_id': purchase.id,

                # 支付状态
                'is_free_purchase': total_amount == 0 and (points_used > 0 or quota_used > 0),
                'is_mixed_payment': payment_composition == 'mixed',

                # 用户状态
                'vip_level_at_purchase': user_finance.vip_level if user_finance else 0,

                # 向后兼容字段
                'amount_paid': total_amount / 100.0,  # 转换为元
                'price': total_amount / 100.0,  # 前端期望的字段
                'cost_type': payment_composition,
                'cost_amount': total_saved if total_amount == 0 else total_amount,

                # 完整的支付摘要
                'payment_summary': payment_summary,

                # 调试信息
                'debug_info': {
                    'purchase_amount': purchase.amount,
                    'calculated_total_value': total_value,
                    'calculated_total_amount': total_amount,
                    'purchase_status': purchase.status,
                    'has_points_record': points_record is not None,
                    'user_finance_exists': user_finance is not None,
                    'has_new_fields': hasattr(purchase, 'cash_amount')
                }
            }
            buyers.append(buyer)

        return jsonify({
            'success': True,
            'article': {
                'id': article.id,
                'title': article.title,
                'status': article.status,
                'created_at': article.created_at.isoformat() if article.created_at else None
            },
            'buyers': buyers,
            'total_count': len(buyers)
        })

    except Exception as e:
        logger.error(f"获取文章购买者列表失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'获取文章购买者列表失败: {str(e)}'
        }), 500

@admin_bp.route('/api/debug-article-records/<int:article_id>', methods=['GET'])
@login_required
@admin_required
def debug_article_records(article_id):
    """调试API - 查看文章相关的所有记录"""
    try:
        # 获取所有购买记录
        purchases = Purchase.query.filter_by(article_id=article_id).all()

        debug_data = {
            'article_id': article_id,
            'purchases': [],
            'all_points_records': [],
            'all_quota_records': [],
            'all_consumption_records': []
        }

        # 购买记录
        for purchase in purchases:
            debug_data['purchases'].append({
                'id': purchase.id,
                'user_id': purchase.user_id,
                'amount': purchase.amount,
                'content_type': purchase.content_type,
                'purchase_date': purchase.purchase_date.isoformat() if purchase.purchase_date else None,
                'status': purchase.status,
                'quota_used': getattr(purchase, 'quota_used', 'N/A'),
                'quota_type': getattr(purchase, 'quota_type', 'N/A')
            })

        # 获取所有用户的积分记录（包含这篇文章）
        user_ids = [p.user_id for p in purchases]
        if user_ids:
            points_records = PointsRecord.query.filter(
                PointsRecord.user_id.in_(user_ids),
                PointsRecord.order_id.like(f'%{article_id}%')
            ).all()

            for record in points_records:
                debug_data['all_points_records'].append({
                    'id': record.id,
                    'user_id': record.user_id,
                    'amount': record.amount,
                    'type': record.type,
                    'order_id': record.order_id,
                    'created_at': record.created_at.isoformat() if record.created_at else None
                })

        # 获取所有配额记录
        if user_ids:
            quota_records = QuotaUsageRecord.query.filter(
                QuotaUsageRecord.user_id.in_(user_ids),
                QuotaUsageRecord.article_id == article_id
            ).all()

            for record in quota_records:
                debug_data['all_quota_records'].append({
                    'id': record.id,
                    'user_id': record.user_id,
                    'article_id': record.article_id,
                    'quota_type': record.quota_type,
                    'amount_used': record.amount_used,
                    'exchange_used': record.exchange_used,
                    'vip_level_at_time': record.vip_level_at_time,
                    'created_at': record.created_at.isoformat() if record.created_at else None
                })

        # 获取所有消费记录
        if user_ids:
            consumption_records = UserConsumptionRecord.query.filter(
                UserConsumptionRecord.user_id.in_(user_ids),
                UserConsumptionRecord.article_id == article_id
            ).all()

            for record in consumption_records:
                debug_data['all_consumption_records'].append({
                    'id': record.id,
                    'user_id': record.user_id,
                    'article_id': record.article_id,
                    'content_type': record.content_type,
                    'cost_type': record.cost_type,
                    'cost_amount': record.cost_amount,
                    'created_at': record.created_at.isoformat() if record.created_at else None
                })

        return jsonify({
            'success': True,
            'debug_data': debug_data
        })

    except Exception as e:
        logger.error(f"调试API失败: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'调试API失败: {str(e)}'
        }), 500

@admin_bp.route('/api/articles/batch_delete', methods=['POST'])
@login_required
@admin_required
def api_batch_delete_articles():
    """批量删除文章 (API)"""
    data = request.get_json()
    if not data or 'ids' not in data or not isinstance(data['ids'], list):
        return jsonify({"success": False, "error": "无效的请求数据，需要一个包含ID列表的 'ids' 键。"}), 400

    ids_to_delete = data['ids']
    if not ids_to_delete:
        return jsonify({"success": False, "error": "没有提供要删除的文章ID。"}), 400

    deleted_count = 0
    errors = []

    for article_id_str in ids_to_delete:
        try:
            article_id = int(article_id_str) # 确保是整数
            article = Article.query.get(article_id)
            if article:
                # 这里可以集成更复杂的删除逻辑，比如调用一个负责删除文章及其关联数据（如图片）的函数
                # 目前，我们直接删除文章对象，与 backup_delete_article 的核心逻辑一致
                db.session.delete(article)
                deleted_count += 1
                logger.info(f"批量删除：文章 ID {article_id} 已标记为删除。")
            else:
                errors.append(f"ID {article_id} 未找到对应的文章。")
                logger.warning(f"批量删除：未找到文章 ID {article_id}。")
        except ValueError:
            errors.append(f"无效的文章ID格式: '{article_id_str}'")
            logger.warning(f"批量删除：无效的文章ID格式 '{article_id_str}'")
        except Exception as e:
            # 捕获删除单个文章时可能发生的其他DB相关错误
            db.session.rollback() # 单个错误时，回滚当前更改，但继续处理下一个
            errors.append(f"删除ID {article_id_str} 时发生数据库错误: {str(e)}")
            logger.error(f"批量删除文章中，删除ID {article_id_str} 失败: {str(e)}")
    
    if not errors and deleted_count > 0:
        # 全部成功
        try:
            db.session.commit()
            logger.info(f"批量删除：成功删除 {deleted_count} 篇文章并已提交事务。")
            return jsonify({"success": True, "message": f"成功删除了 {deleted_count} 篇文章。"})
        except Exception as e:
            db.session.rollback()
            logger.error(f"批量删除文章提交事务时失败: {str(e)}")
            return jsonify({"success": False, "error": f"提交批量删除操作时发生严重错误: {str(e)}"}), 500
    elif errors:
        # 如果有任何错误，我们选择回滚所有本次批量操作中已标记删除的内容，
        # 以保证原子性，或者至少不提交部分成功的删除。
        # 注意：上面循环中对单个错误的 rollback() 可能已经回滚了部分。
        # 这里确保最终的 commit 不会发生，或者如果需要，显式地回滚整个事务。
        # 根据具体需求，也可以选择提交已成功的，并报告失败的。
        # 当前策略：如果任何一个失败，则不提交任何删除，并报告所有错误。
        db.session.rollback() # 确保整体回滚
        message = f"批量删除操作中有 {len(errors)} 个错误，所有更改已回滚。"
        logger.warning(f"批量删除文章操作失败，错误数量: {len(errors)}. 详情: {'; '.join(errors)}")
        return jsonify({"success": False, "message": message, "error": "部分或全部文章删除失败。", "details": errors}), 400 # 或 207 Multi-Status
    elif deleted_count == 0 and not errors:
         # 没有选中任何有效的、存在的文章
        logger.info("批量删除：没有有效的文章被删除（可能ID列表为空或所有ID都无效）。")
        return jsonify({"success": False, "message": "没有有效的文章被删除。"}), 400
    else: # deleted_count > 0 and not errors (这种情况被第一个if分支处理)
        # 逻辑上这个else分支不应该被达到，但作为保险
        db.session.rollback()
        logger.error("批量删除文章时出现未预期的逻辑分支。")
        return jsonify({"success": False, "error": "批量删除过程中发生未知错误。"}), 500



# 管理员阅读模式路由
@admin_bp.route('/reading/<int:reading_id>')
@login_required
@admin_required
def admin_reading(reading_id):
    """管理员阅读模式视图 - 用于测试和管理阅读模式功能"""
    try:
        # 获取文章内容，与普通阅读视图逻辑类似
        from models import Article, ReadingPreference, Author
        from flask_login import current_user

        reading = Article.query.get_or_404(reading_id)

        # 获取作者信息
        author_name = "未知"
        reader = None
        if reading.reader_id:
            reader = Author.query.get(reading.reader_id)
            if reader:
                author_name = reader.name

        # 获取用户阅读偏好
        reading_preference = None
        if current_user.is_authenticated:
            reading_preference = ReadingPreference.query.filter_by(user_id=current_user.id).first()

        # 返回模板，传递完整的变量（参考reading.py的实现）
        return render_template('article_reading.html',
                            article=reading,
                            reading=reading,
                            author_name=author_name,
                            reader=reader,
                            reading_preference=reading_preference,
                            reading_preferences=reading_preference,  # 兼容性
                            is_admin=True,
                            # 管理员有完整访问权限
                            has_basic_access=True,
                            has_premium_access=True,
                            can_access_basic=True,
                            can_access_premium=True,
                            # 其他必要变量
                            basic_message="",
                            premium_message="",
                            basic_price=0,
                            premium_price=0,
                            is_bookmarked=False)
    
    except Exception as e:
        logger.error(f"管理员阅读模式页面错误: {str(e)}")
        return render_template('error.html', error=f"加载管理员阅读模式时出错，请稍后再试。错误: {str(e)}")

@admin_bp.route('/settings')
@login_required
@admin_required
def system_settings():
    """显示系统设置页面"""
    try:
        # 获取所有设置
        settings = SystemSetting.query.all()
        settings_dict = {s.key: s.value for s in settings}
        
        return render_template('admin/settings.html', settings=settings_dict)
    except Exception as e:
        logger.error(f"加载系统设置页面失败: {str(e)}")
        flash('加载系统设置时出错', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/settings', methods=['GET'])
@login_required
@admin_required
def get_settings():
    """获取系统设置"""
    try:
        settings = SystemSetting.query.all()
        settings_dict = {s.key: s.value for s in settings}
        
        return jsonify({
            'success': True,
            'settings': settings_dict
        })
    except Exception as e:
        logger.error(f"获取系统设置API失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/settings', methods=['POST'])
@login_required
@admin_required
def update_settings():
    """更新系统设置"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400
        
        # 遍历所有设置键值对进行更新或创建
        for key, value in data.items():
            # 查找现有设置
            setting = SystemSetting.query.filter_by(key=key).first()
            
            if setting:
                # 更新现有设置
                setting.value = value
            else:
                # 创建新设置
                new_setting = SystemSetting(key=key, value=value)
                db.session.add(new_setting)
        
        db.session.commit()
        return jsonify({'success': True, 'message': '系统设置已更新'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新系统设置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# --- 工单管理 ---
@admin_bp.route('/tickets')
@login_required
@staff_required
def tickets():
    """工单管理页面"""
    try:
        from models import SupportTicket
        from datetime import datetime

        page = request.args.get('page', 1, type=int)
        per_page = 20
        query = SupportTicket.query.order_by(SupportTicket.created_at.desc())

        # 获取筛选参数
        status = request.args.get('status', '')
        priority = request.args.get('priority', '')
        category = request.args.get('category', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')

        # 状态筛选
        if status:
            query = query.filter(SupportTicket.status == status)

        # 优先级筛选
        if priority:
            query = query.filter(SupportTicket.priority == priority)

        # 分类筛选
        if category:
            query = query.filter(SupportTicket.category == category)

        # 时间筛选
        if start_date:
            try:
                start_dt = datetime.strptime(start_date, '%Y-%m-%d')
                query = query.filter(SupportTicket.created_at >= start_dt)
            except ValueError:
                flash('开始日期格式错误', 'error')

        if end_date:
            try:
                end_dt = datetime.strptime(end_date, '%Y-%m-%d')
                # 结束日期包含当天的所有时间
                end_dt = end_dt.replace(hour=23, minute=59, second=59)
                query = query.filter(SupportTicket.created_at <= end_dt)
            except ValueError:
                flash('结束日期格式错误', 'error')

        pagination = query.paginate(page=page, per_page=per_page)
        tickets = pagination.items

        return render_template('admin/ticket_list.html',
                              tickets=tickets,
                              pagination=pagination,
                              status=status,
                              priority=priority,
                              category=category,
                              start_date=start_date,
                              end_date=end_date)
    except Exception as e:
        logger.error(f"加载工单管理页面失败: {str(e)}")
        flash('加载工单管理页面时出错', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/tickets/<int:ticket_id>')
@login_required
@staff_required
def ticket_detail(ticket_id):
    """工单详情页面"""
    try:
        from models import SupportTicket, TicketLog
        ticket = SupportTicket.query.get_or_404(ticket_id)
        logs = TicketLog.query.filter_by(ticket_id=ticket_id).order_by(TicketLog.created_at.asc()).all()

        return render_template('admin/ticket_detail.html', ticket=ticket, logs=logs)
    except Exception as e:
        logger.error(f"加载工单详情页面失败: {str(e)}")
        flash('加载工单详情时出错', 'error')
        return redirect(url_for('admin.tickets'))

@admin_bp.route('/api/tickets', methods=['GET'])
@login_required
@staff_required
def api_get_tickets():
    """获取工单列表API"""
    try:
        from models import SupportTicket

        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        status = request.args.get('status', 'all')
        category = request.args.get('category', 'all')
        priority = request.args.get('priority', 'all')

        query = SupportTicket.query

        if status != 'all':
            query = query.filter_by(status=status)

        if category != 'all':
            query = query.filter_by(category_primary=category)

        if priority != 'all':
            query = query.filter_by(priority=priority)

        tickets = query.order_by(SupportTicket.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        ticket_list = []
        for ticket in tickets.items:
            # 获取用户VIP信息 - 仅管理员可见
            vip_level = "普通用户"
            if current_user.can_access_finance() and ticket.user.finance and ticket.user.finance.vip_level > 0:
                vip_levels = {1: "初级VIP", 2: "高级VIP"}
                vip_level = vip_levels.get(ticket.user.finance.vip_level, "VIP用户")

            ticket_data = {
                'id': ticket.id,
                'ticket_number': ticket.get_ticket_number(),
                'subject': ticket.subject,
                'content': ticket.content,
                'category_primary': ticket.category_primary,
                'category_secondary': ticket.category_secondary,
                'status': ticket.status,
                'priority': ticket.priority,
                'created_at': ticket.created_at.isoformat(),
                'updated_at': ticket.updated_at.isoformat(),
                'resolved_at': ticket.resolved_at.isoformat() if ticket.resolved_at else None,
                'admin_reply': ticket.admin_reply,
                'user': {
                    'id': ticket.user.id,
                    'username': ticket.user.username,
                    'email': ticket.user.email,
                    'vip_level': vip_level
                },
                'admin': {
                    'id': ticket.admin.id,
                    'username': ticket.admin.username
                } if ticket.admin else None,
                'staff': {
                    'id': ticket.staff.id,
                    'username': ticket.staff.username
                } if ticket.staff else None
            }
            ticket_list.append(ticket_data)

        return jsonify({
            'success': True,
            'tickets': ticket_list,
            'pagination': {
                'page': tickets.page,
                'pages': tickets.pages,
                'per_page': tickets.per_page,
                'total': tickets.total,
                'has_next': tickets.has_next,
                'has_prev': tickets.has_prev
            }
        })

    except Exception as e:
        logger.error(f"获取工单列表API失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/tickets/stats', methods=['GET'])
@login_required
@staff_required
def api_get_tickets_stats():
    """获取工单统计信息API"""
    try:
        from models import SupportTicket
        from datetime import datetime, timedelta
        from sqlalchemy import func

        # 基础统计
        total_tickets = SupportTicket.query.count()
        open_tickets = SupportTicket.query.filter(SupportTicket.status == 'open').count()
        closed_tickets = SupportTicket.query.filter(SupportTicket.status == 'closed').count()

        # 时间统计
        today = datetime.now().date()
        today_created = SupportTicket.query.filter(
            func.date(SupportTicket.created_at) == today
        ).count()

        today_closed = SupportTicket.query.filter(
            SupportTicket.status == 'closed',
            func.date(SupportTicket.updated_at) == today
        ).count()

        # 状态分布
        status_stats = db.session.query(
            SupportTicket.status,
            func.count(SupportTicket.id).label('count')
        ).group_by(SupportTicket.status).all()

        status_distribution = {stat.status: stat.count for stat in status_stats}

        # 优先级分布
        priority_stats = db.session.query(
            SupportTicket.priority,
            func.count(SupportTicket.id).label('count')
        ).group_by(SupportTicket.priority).all()

        priority_distribution = {stat.priority: stat.count for stat in priority_stats}

        # 分类分布
        category_stats = db.session.query(
            SupportTicket.category,
            func.count(SupportTicket.id).label('count')
        ).group_by(SupportTicket.category).all()

        category_distribution = {stat.category: stat.count for stat in category_stats}

        # 员工表现（模拟数据）
        staff_performance = [
            {'staff': '客服A', 'assigned': 23, 'resolved': 20, 'avg_time': 18.5},
            {'staff': '客服B', 'assigned': 19, 'resolved': 17, 'avg_time': 22.3}
        ]

        stats = {
            'total_tickets': total_tickets,
            'open_tickets': open_tickets,
            'closed_tickets': closed_tickets,
            'today_created': today_created,
            'today_closed': today_closed,
            'avg_response_time': 2.5,  # 模拟数据
            'avg_resolution_time': 24.5,  # 模拟数据
            'status_distribution': status_distribution,
            'priority_distribution': priority_distribution,
            'category_distribution': category_distribution,
            'staff_performance': staff_performance
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取工单统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/tickets/<int:ticket_id>/reply', methods=['POST'])
@login_required
@staff_required
def api_reply_ticket(ticket_id):
    """回复工单API"""
    try:
        from models import SupportTicket, TicketLog
        from utils.customer_service import send_ticket_notification, update_ticket_txt_log, create_ticket_log

        ticket = SupportTicket.query.get_or_404(ticket_id)
        data = request.get_json()

        if not data.get('reply'):
            return jsonify({'success': False, 'error': '回复内容不能为空'}), 400

        # 更新工单
        ticket.admin_reply = data.get('reply')
        ticket.admin_id = current_user.id
        ticket.status = data.get('status', 'processing')
        ticket.updated_at = datetime.utcnow()

        if data.get('status') == 'resolved':
            ticket.resolved_at = datetime.utcnow()

        # 记录操作日志
        create_ticket_log(
            ticket_id=ticket.id,
            action_type='replied',
            action_by=current_user.id,
            action_details=f"回复: {data.get('reply')[:50]}..."
        )

        db.session.commit()

        # 发送邮件通知用户
        try:
            send_ticket_notification(ticket, 'replied')
        except Exception as e:
            logger.error(f"发送回复邮件通知失败: {e}")

        # 更新TXT日志
        try:
            update_ticket_txt_log(ticket, '客服回复', data.get('reply'))
        except Exception as e:
            logger.error(f"更新TXT日志失败: {e}")

        return jsonify({'success': True, 'message': '回复成功'})

    except Exception as e:
        db.session.rollback()
        logger.error(f"回复工单失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

def get_vip_status_indicator(user_id):
    """获取VIP状态指示器"""
    try:
        from vip_status_indicator import VIPStatusIndicator
        return VIPStatusIndicator.check_vip_status(user_id)
    except Exception as e:
        return {
            'status': 'red',
            'message': '检查失败',
            'details': str(e)
        }

def get_vip_status_summary():
    """获取VIP状态汇总"""
    try:
        from vip_status_indicator import VIPStatusIndicator
        return VIPStatusIndicator.get_status_summary()
    except Exception as e:
        return {
            'total_vip_users': 0,
            'green': 0,
            'yellow': 0,
            'red': 0,
            'health_rate': 0
        }

@admin_bp.route('/users')
@login_required
@staff_required
def users_dashboard():
    """显示用户管理页面 - 简化版本"""
    try:
        # 获取基本用户列表
        page = request.args.get('page', 1, type=int)
        per_page = 20

        # 简单查询，避免复杂的join操作
        users_query = User.query.order_by(User.created_at.desc())

        # 基本搜索
        search = request.args.get('search', '')
        if search:
            users_query = users_query.filter(User.username.contains(search))

        pagination = users_query.paginate(page=page, per_page=per_page, error_out=False)
        users = pagination.items

        # 使用财务计算系统获取准确数据
        from services.finance_calculator import FinanceCalculator

        # 构建用户行HTML
        user_rows = []
        for user in users:
            role_map = {'admin': '管理员', 'staff': '员工', 'customer_service': '客服', 'user': '普通用户'}
            role_text = role_map.get(user.role, user.role)

            if user.role == 'admin':
                role_class = 'bg-red-900 text-red-300'
            elif user.role == 'staff':
                role_class = 'bg-blue-900 text-blue-300'
            else:
                role_class = 'bg-gray-900 text-gray-300'

            created_time = user.created_at.strftime('%Y-%m-%d') if user.created_at else '未知'

            # 使用财务计算系统获取准确的财务信息
            if user.role in ['admin', 'superadmin']:
                # 管理员显示无限权限
                finance_info = f'''
                <div class="bg-gray-800 p-3 rounded text-xs">
                    <div class="mb-2"><span class="px-2 py-1 rounded bg-red-900 text-red-300 font-bold">管理员</span></div>
                    <div class="grid grid-cols-2 gap-1">
                        <div>余额: <span class="text-green-400 font-bold">∞</span></div>
                        <div>积分: <span class="text-blue-400 font-bold">∞</span></div>
                        <div>基础: <span class="text-yellow-400 font-bold">∞</span></div>
                        <div>高级: <span class="text-purple-400 font-bold">∞</span></div>
                    </div>
                </div>
                '''
            else:
                # 普通用户使用财务计算系统
                finance_summary = FinanceCalculator.get_user_finance_summary(user.id)
                if finance_summary:
                    vip_level = finance_summary['vip_level']
                    vip_text = f"VIP{vip_level}" if vip_level > 0 else "免费"
                    vip_class = "bg-yellow-900 text-yellow-300" if vip_level > 0 else "bg-gray-700 text-gray-300"

                    # VIP状态指示器（专门用于检查购买记录是否正常）
                    vip_status = get_vip_status_indicator(user.id) if vip_level > 0 else {'status': 'gray', 'message': '非VIP'}
                    if vip_level > 0:
                        status_light = "🟢" if vip_status['status'] == 'green' else "🟡" if vip_status['status'] == 'yellow' else "🔴"
                    else:
                        status_light = ""  # 非VIP用户不显示状态灯

                    # VIP到期时间
                    vip_expire = ""
                    if finance_summary['vip_expire_at']:
                        expire_date = finance_summary['vip_expire_at'].strftime('%m-%d')
                        vip_expire = f"<br><small class='text-gray-400'>到期:{expire_date}</small>"

                    # 财务数据
                    balance = finance_summary['balance_yuan']
                    points = finance_summary['points_balance']
                    quota = finance_summary['quota_info']

                    # 数据一致性状态（保留原有的）
                    integrity = finance_summary['data_integrity']
                    integrity_icon = "🔴" if integrity['status'] == 'inconsistent' else "🟢"

                    # 构建状态指示器说明
                    status_indicators = ""
                    if vip_level > 0:
                        # 只显示VIP购买记录状态指示器，移除重复的指示器
                        status_indicators = f'<span class="ml-1" title="VIP购买记录状态：{vip_status["message"]}">{status_light}</span>'

                    finance_info = f'''
                    <div class="bg-gray-800 p-3 rounded text-xs">
                        <div class="mb-2">
                            <span class="px-2 py-1 rounded {vip_class} font-bold">{vip_text}</span>
                            {status_indicators}
                            {vip_expire}
                        </div>
                        <div class="grid grid-cols-2 gap-1">
                            <div>余额: <span class="text-green-400 font-bold">¥{balance:.2f}</span></div>
                            <div>积分: <span class="text-blue-400 font-bold">{points}</span></div>
                            <div>基础: <span class="text-yellow-400 font-bold">{quota['basic_used']}/{'∞' if quota['basic_limit'] >= 999999 else quota['basic_limit']}</span></div>
                            <div>高级: <span class="text-purple-400 font-bold">{quota['premium_used']}/{'∞' if quota['premium_limit'] >= 999999 else quota['premium_limit']}</span></div>
                        </div>
                        {f'<div class="mt-1 text-xs text-gray-400">🚦 {vip_status["message"]}</div>' if vip_status['status'] != 'green' and vip_level > 0 else ''}
                    </div>
                    '''
                else:
                    finance_info = '<div class="text-xs text-red-500 bg-gray-800 p-2 rounded">❌ 财务数据错误</div>'

            user_row = f'''
            <tr class="border-b border-gray-800 hover:bg-gray-800">
                <td class="py-3 px-2 text-center font-mono">{user.id}</td>
                <td class="py-3 px-3">
                    <div class="font-medium text-white">{user.username}</div>
                    <div class="text-xs text-gray-400">{user.email or "未设置邮箱"}</div>
                </td>
                <td class="py-3 px-2 text-center">
                    <span class="px-2 py-1 rounded text-xs {role_class} font-bold">
                        {role_text}
                    </span>
                </td>
                <td class="py-3 px-3">{finance_info}</td>
                <td class="py-3 px-2 text-center text-xs text-gray-400">{created_time}</td>
                <td class="py-3 px-2 text-center">
                    <a href="/admin/users/{user.id}/finance"
                       class="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 font-bold">
                        详情
                    </a>
                </td>
            </tr>
            '''
            user_rows.append(user_row)

        # 获取VIP状态汇总
        vip_summary = get_vip_status_summary()

        # 分页链接
        prev_link = f'<a href="?page={pagination.prev_num}&search={search}" class="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700">上一页</a>' if pagination.has_prev else '<span class="px-3 py-1 bg-gray-800 text-gray-500 rounded">上一页</span>'
        next_link = f'<a href="?page={pagination.next_num}&search={search}" class="px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700">下一页</a>' if pagination.has_next else '<span class="px-3 py-1 bg-gray-800 text-gray-500 rounded">下一页</span>'

        # 返回简化的HTML页面
        return f'''
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 塔罗解读</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {{ background-color: #121212; color: #e0e0e0; }}
        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-yellow-400">👥 用户管理</h1>
            <a href="/admin/" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                ← 返回管理后台
            </a>
        </div>

        <!-- VIP状态汇总面板 -->
        <div class="card rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold text-yellow-400 mb-3">🚦 VIP购买记录状态监控</h3>
            <div class="flex space-x-6 mb-3">
                <div class="flex items-center">
                    <span class="text-lg mr-2">🟢</span>
                    <span>正常: {vip_summary['green']}</span>
                </div>
                <div class="flex items-center">
                    <span class="text-lg mr-2">🟡</span>
                    <span>警告: {vip_summary['yellow']}</span>
                </div>
                <div class="flex items-center">
                    <span class="text-lg mr-2">🔴</span>
                    <span>异常: {vip_summary['red']}</span>
                </div>
                <div class="ml-auto">
                    <span class="text-sm text-gray-400">VIP健康率: {vip_summary['health_rate']:.1f}%</span>
                </div>
            </div>
            <div class="text-xs text-gray-400">
                <strong>红绿灯说明：</strong>
                🟢 VIP用户且有对应购买记录 |
                🟡 VIP用户但数据不完整 |
                🔴 VIP用户但完全没有购买记录（需要处理）
            </div>
        </div>

        <div class="card rounded-lg p-6">
            <div class="mb-4">
                <form method="GET" class="flex gap-4">
                    <input type="text" name="search" value="{search}"
                           placeholder="搜索用户名..."
                           class="px-3 py-2 bg-gray-700 text-white rounded border border-gray-600">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        搜索
                    </button>
                </form>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full table-fixed">
                    <thead>
                        <tr class="border-b border-gray-700 bg-gray-900">
                            <th class="w-16 text-center py-3 px-2 text-yellow-400 font-bold">ID</th>
                            <th class="w-48 text-left py-3 px-3 text-yellow-400 font-bold">用户信息</th>
                            <th class="w-20 text-center py-3 px-2 text-yellow-400 font-bold">角色</th>
                            <th class="w-64 text-left py-3 px-3 text-yellow-400 font-bold">💰 财务状况</th>
                            <th class="w-24 text-center py-3 px-2 text-yellow-400 font-bold">注册</th>
                            <th class="w-20 text-center py-3 px-2 text-yellow-400 font-bold">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {"".join(user_rows)}
                    </tbody>
                </table>
            </div>

            <div class="mt-6 flex justify-between items-center">
                <div class="text-gray-400">
                    共 {pagination.total} 个用户，第 {pagination.page} / {pagination.pages} 页
                </div>
                <div class="flex gap-2">
                    {prev_link}
                    {next_link}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        '''

    except Exception as e:
        logger.error(f"加载用户管理页面失败: {str(e)}")
        import traceback
        return f'''
<!DOCTYPE html>
<html>
<head><title>用户管理错误</title></head>
<body style="background: #121212; color: white; padding: 20px;">
    <h1>用户管理页面错误</h1>
    <p>错误信息: {str(e)}</p>
    <pre>{traceback.format_exc()}</pre>
    <a href="/admin/" style="color: #60a5fa;">返回管理后台</a>
</body>
</html>
        '''

@admin_bp.route('/users/<int:user_id>/finance')
@login_required
@staff_required
def user_finance_detail(user_id):
    """用户财务详情页面 - 显示所有原始单据和余额计算过程"""
    try:
        from sqlalchemy.orm import joinedload
        from models import Purchase, Order, UserFinance, UserConsumptionRecord, Article
        from collections import defaultdict
        from datetime import datetime

        # 获取用户信息
        user = User.query.options(joinedload(User.finance)).get_or_404(user_id)

        # 获取所有购买记录（原始单据）- 只查询存在的字段
        purchases = db.session.query(Purchase.id, Purchase.user_id, Purchase.article_id,
                                    Purchase.amount, Purchase.content_type, Purchase.purchase_date,
                                    Purchase.payment_method, Purchase.transaction_id)\
                             .filter_by(user_id=user_id)\
                             .order_by(Purchase.purchase_date.desc()).all()

        # 获取所有订单记录
        orders = Order.query.filter_by(user_id=user_id)\
                           .order_by(Order.created_at.desc()).all()

        # 获取所有消费记录
        consumption_records = UserConsumptionRecord.query.filter_by(user_id=user_id)\
                                                         .order_by(UserConsumptionRecord.created_at.desc()).all()

        # 构建详细的财务流水记录
        finance_transactions = []

        # 添加购买记录（收入）
        for purchase in purchases:
            article = Article.query.get(purchase.article_id) if purchase.article_id else None
            if purchase.article_id:
                description = f'购买文章: {article.title if article else "已删除文章"}'
            else:
                # VIP购买或其他类型
                if purchase.amount == 12800:
                    description = '购买VIP1个月'
                elif purchase.amount == 140800:
                    description = '购买VIP1年'
                else:
                    description = f'购买VIP ¥{purchase.amount/100:.2f}'

            finance_transactions.append({
                'type': 'purchase',
                'date': purchase.purchase_date,
                'description': description,
                'content_type': purchase.content_type,
                'payment_method': purchase.payment_method,
                'amount': purchase.amount,
                'transaction_id': purchase.transaction_id,
                'status': 'completed'
            })

        # 添加消费记录（支出）
        for record in consumption_records:
            article = Article.query.get(record.article_id) if record.article_id else None
            finance_transactions.append({
                'type': 'consumption',
                'date': record.created_at,
                'description': f'消费: {article.title if article else "系统消费"}',
                'content_type': record.content_type,
                'cost_type': record.cost_type,
                'amount': -record.cost_amount,  # 负数表示支出
                'status': 'completed'
            })

        # 按时间排序
        finance_transactions.sort(key=lambda x: x['date'] if x['date'] else datetime.min, reverse=True)

        # 计算余额变化过程
        running_balance = 0
        if user.finance:
            running_balance = user.finance.balance or 0

        # 从最新记录开始，反向计算每笔交易后的余额
        for i, transaction in enumerate(finance_transactions):
            transaction['balance_after'] = running_balance
            running_balance -= transaction['amount']
            transaction['balance_before'] = running_balance

        # 反转列表，使其按时间正序显示
        finance_transactions.reverse()

        # 计算统计数据
        finance_stats = {
            'total_purchases': len(purchases),
            'total_spent': sum(p.amount for p in purchases),
            'basic_purchases': len([p for p in purchases if p.content_type == 'basic']),
            'premium_purchases': len([p for p in purchases if p.content_type == 'premium']),
            'cash_payments': len([p for p in purchases if p.payment_method in ['alipay', 'wechat', 'mock']]),
            'quota_payments': len([p for p in purchases if p.payment_method == 'quota']),
            'points_payments': len([p for p in purchases if p.payment_method == 'points']),
            'total_consumption': sum(r.cost_amount for r in consumption_records),
            'current_balance': user.finance.balance if user.finance else 0
        }

        # 按月统计
        monthly_stats = defaultdict(lambda: {'purchases': 0, 'consumption': 0, 'net': 0})

        for transaction in finance_transactions:
            if transaction['date']:
                month_key = transaction['date'].strftime('%Y-%m')
                if transaction['type'] == 'purchase':
                    monthly_stats[month_key]['purchases'] += transaction['amount']
                else:
                    monthly_stats[month_key]['consumption'] += abs(transaction['amount'])
                monthly_stats[month_key]['net'] = monthly_stats[month_key]['purchases'] - monthly_stats[month_key]['consumption']

        # 转换为列表
        monthly_data = []
        for month, stats in sorted(monthly_stats.items(), reverse=True):
            monthly_data.append({
                'month': month,
                'purchases': stats['purchases'],
                'consumption': stats['consumption'],
                'net': stats['net']
            })

        # 构建财务交易记录HTML
        transaction_rows = []
        for transaction in finance_transactions:
            transaction_date = transaction['date'].strftime('%Y-%m-%d %H:%M') if transaction['date'] else '未知'
            amount_class = 'text-green-400' if transaction['amount'] > 0 else 'text-red-400'
            # 正确转换金额：分 -> 元
            amount_yuan = transaction['amount'] / 100.0
            amount_text = f"+¥{amount_yuan:.2f}" if amount_yuan > 0 else f"-¥{abs(amount_yuan):.2f}"

            transaction_row = f'''
            <tr class="border-b border-gray-800 hover:bg-gray-800">
                <td class="py-3 px-4">{transaction_date}</td>
                <td class="py-3 px-4">{transaction['type']}</td>
                <td class="py-3 px-4">{transaction['description']}</td>
                <td class="py-3 px-4">{transaction.get('content_type', '-')}</td>
                <td class="py-3 px-4">{transaction.get('payment_method', transaction.get('cost_type', '-'))}</td>
                <td class="py-3 px-4 {amount_class}">{amount_text}</td>
                <td class="py-3 px-4">¥{transaction.get('balance_before', 0)/100:.2f}</td>
                <td class="py-3 px-4">¥{transaction.get('balance_after', 0)/100:.2f}</td>
                <td class="py-3 px-4">{transaction.get('transaction_id', '-')}</td>
            </tr>
            '''
            transaction_rows.append(transaction_row)

        # 构建月度统计HTML
        monthly_rows = []
        for month_data in monthly_data:
            net_class = 'text-green-400' if month_data['net'] >= 0 else 'text-red-400'
            monthly_row = f'''
            <tr class="border-b border-gray-800">
                <td class="py-2 px-4">{month_data['month']}</td>
                <td class="py-2 px-4 text-green-400">¥{month_data['purchases']/100:.2f}</td>
                <td class="py-2 px-4 text-red-400">¥{month_data['consumption']/100:.2f}</td>
                <td class="py-2 px-4 {net_class}">¥{month_data['net']/100:.2f}</td>
            </tr>
            '''
            monthly_rows.append(monthly_row)

        # 构建UserFinance信息HTML
        finance_info_html = ""
        if user.finance:
            vip_level = user.finance.vip_level or 0
            vip_expire = user.finance.vip_expire_at or "N/A"
            balance = (user.finance.balance or 0) / 100
            total_spent = (user.finance.total_spent or 0) / 100
            points_balance = user.finance.points_balance or 0
            basic_quota_used = user.finance.basic_quota_used or 0
            premium_quota_used = user.finance.premium_quota_used or 0
            quota_reset_date = user.finance.quota_reset_date or "N/A"

            finance_info_html = f'''
                <p><strong>VIP等级:</strong> {vip_level}</p>
                <p><strong>VIP到期时间:</strong> {vip_expire}</p>
                <p><strong>余额:</strong> ¥{balance:.2f}</p>
                <p><strong>总消费金额:</strong> ¥{total_spent:.2f}</p>
                <p><strong>积分余额:</strong> {points_balance}</p>
                <p><strong>基础配额已用:</strong> {basic_quota_used}</p>
                <p><strong>高级配额已用:</strong> {premium_quota_used}</p>
                <p><strong>配额重置日期:</strong> {quota_reset_date}</p>
            '''

            # 添加状态分析
            if vip_level > 0 and total_spent > 0:
                finance_info_html += f'''
                    <div class="mt-2 p-2 bg-green-800 rounded">
                        <p class="text-green-300">✅ <strong>发现购买痕迹！</strong></p>
                        <p class="text-green-300">用户是VIP且total_spent > 0，说明确实有消费记录</p>
                        <p class="text-green-300">应该为这¥{total_spent:.2f}的消费创建Purchase记录</p>
                    </div>
                '''
            elif vip_level > 0 and total_spent == 0:
                finance_info_html += '''
                    <div class="mt-2 p-2 bg-red-800 rounded">
                        <p class="text-red-300">🚨 <strong>异常：VIP但total_spent=0</strong></p>
                        <p class="text-red-300">可能是管理员手动设置的VIP或数据异常</p>
                    </div>
                '''
        else:
            finance_info_html = '<p class="text-red-300">❌ 该用户没有UserFinance记录</p>'

        # 获取Order数量
        order_count = Order.query.filter_by(user_id=user.id).count()

        # 返回简化的HTML页面
        return f'''
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户财务详情 - {user.username}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {{ background-color: #121212; color: #e0e0e0; }}
        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-yellow-400">💰 {user.username} 的财务详情</h1>
            <a href="/admin/users" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                ← 返回用户管理
            </a>
        </div>

        <!-- UserFinance详细信息 -->
        <div class="card p-4 rounded-lg mb-6 bg-blue-900 border border-blue-700">
            <h3 class="text-lg font-semibold text-blue-400 mb-2">🔍 UserFinance表详细信息</h3>
            <div class="text-sm">
                {finance_info_html}
                <hr class="my-2 border-blue-600">
                <p><strong>Purchase记录数:</strong> {len(purchases)}</p>
                <p><strong>Order记录数:</strong> {order_count}</p>
            </div>
        </div>



        <!-- 财务统计概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div class="card p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-yellow-400 mb-2">总购买次数</h3>
                <p class="text-2xl font-bold">{finance_stats['total_purchases']}</p>
            </div>
            <div class="card p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-green-400 mb-2">总购买金额</h3>
                <p class="text-2xl font-bold text-green-400">¥{finance_stats['total_spent']/100:.2f}</p>
            </div>
            <div class="card p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-red-400 mb-2">总消费金额</h3>
                <p class="text-2xl font-bold text-red-400">¥{finance_stats['total_consumption']/100:.2f}</p>
            </div>
            <div class="card p-4 rounded-lg">
                <h3 class="text-lg font-semibold text-blue-400 mb-2">当前余额</h3>
                <p class="text-2xl font-bold text-blue-400">¥{finance_stats['current_balance']/100:.2f}</p>
            </div>
        </div>

        <!-- 支付方式统计 -->
        <div class="card p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold text-yellow-400 mb-4">支付方式统计</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                    <p class="text-lg font-semibold">现金支付</p>
                    <p class="text-2xl font-bold text-green-400">{finance_stats['cash_payments']}</p>
                </div>
                <div class="text-center">
                    <p class="text-lg font-semibold">配额支付</p>
                    <p class="text-2xl font-bold text-blue-400">{finance_stats['quota_payments']}</p>
                </div>
                <div class="text-center">
                    <p class="text-lg font-semibold">积分支付</p>
                    <p class="text-2xl font-bold text-purple-400">{finance_stats['points_payments']}</p>
                </div>
            </div>
        </div>

        <!-- 月度统计 -->
        <div class="card p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold text-yellow-400 mb-4">月度财务统计</h2>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-700">
                            <th class="text-left py-2 px-4">月份</th>
                            <th class="text-left py-2 px-4">购买金额</th>
                            <th class="text-left py-2 px-4">消费金额</th>
                            <th class="text-left py-2 px-4">净收支</th>
                        </tr>
                    </thead>
                    <tbody>
                        {"".join(monthly_rows)}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 详细财务流水 -->
        <div class="card p-6 rounded-lg">
            <h2 class="text-xl font-semibold text-yellow-400 mb-4">详细财务流水（原始单据）</h2>
            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead>
                        <tr class="border-b border-gray-700">
                            <th class="text-left py-2 px-4">时间</th>
                            <th class="text-left py-2 px-4">类型</th>
                            <th class="text-left py-2 px-4">描述</th>
                            <th class="text-left py-2 px-4">内容类型</th>
                            <th class="text-left py-2 px-4">支付方式</th>
                            <th class="text-left py-2 px-4">金额</th>
                            <th class="text-left py-2 px-4">交易前余额</th>
                            <th class="text-left py-2 px-4">交易后余额</th>
                            <th class="text-left py-2 px-4">交易ID</th>
                        </tr>
                    </thead>
                    <tbody>
                        {"".join(transaction_rows)}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</body>
</html>
        '''

    except Exception as e:
        logger.error(f"加载用户财务详情失败: {str(e)}")
        import traceback
        return f'''
<!DOCTYPE html>
<html>
<head><title>财务详情错误</title></head>
<body style="background: #121212; color: white; padding: 20px;">
    <h1>财务详情页面错误</h1>
    <p>用户ID: {user_id}</p>
    <p>错误信息: {str(e)}</p>
    <pre>{traceback.format_exc()}</pre>
    <a href="/admin/users" style="color: #60a5fa;">返回用户管理</a>
</body>
</html>
        '''

@admin_bp.route('/diagnostic')
def link_diagnostic():
    """链接跳转诊断页面"""
    return render_template('admin/link_diagnostic.html')

@admin_bp.route('/users/<int:user_id>/fix-finance', methods=['POST'])
@login_required
@staff_required
def fix_user_finance(user_id):
    """修复用户财务数据"""
    try:
        from services.finance_calculator import AdminFinanceTools
        success, message = AdminFinanceTools.fix_user_finance_data(user_id)

        if success:
            return f'''
            <div style="background: #121212; color: white; padding: 20px; font-family: Arial;">
                <h2 style="color: #4ade80;">✅ 财务数据修复成功</h2>
                <p>用户ID: {user_id}</p>
                <p>修复结果: {message}</p>
                <a href="/admin/users" style="color: #60a5fa; text-decoration: none;">← 返回用户管理</a>
            </div>
            '''
        else:
            return f'''
            <div style="background: #121212; color: white; padding: 20px; font-family: Arial;">
                <h2 style="color: #ef4444;">❌ 财务数据修复失败</h2>
                <p>用户ID: {user_id}</p>
                <p>错误信息: {message}</p>
                <a href="/admin/users" style="color: #60a5fa; text-decoration: none;">← 返回用户管理</a>
            </div>
            '''
    except Exception as e:
        return f'''
        <div style="background: #121212; color: white; padding: 20px; font-family: Arial;">
            <h2 style="color: #ef4444;">❌ 修复过程出错</h2>
            <p>用户ID: {user_id}</p>
            <p>错误信息: {str(e)}</p>
            <a href="/admin/users" style="color: #60a5fa; text-decoration: none;">← 返回用户管理</a>
        </div>
        '''

@admin_bp.route('/data-repair')
@login_required
@admin_required
def data_repair_dashboard():
    """数据修复控制台"""
    try:
        from services.data_repair import DataValidator

        # 获取数据验证结果
        validation_result = DataValidator.validate_all_users()

        return f'''
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据修复控制台</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {{ background-color: #121212; color: #e0e0e0; }}
        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-red-400">🔧 数据修复控制台</h1>
            <a href="/admin/" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                ← 返回管理后台
            </a>
        </div>

        <!-- 数据验证概览 -->
        <div class="card p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold text-yellow-400 mb-4">📊 数据完整性概览</h2>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <p class="text-lg font-semibold">总用户数</p>
                    <p class="text-3xl font-bold text-blue-400">{validation_result['total_users'] if validation_result else 0}</p>
                </div>
                <div class="text-center">
                    <p class="text-lg font-semibold">数据正常</p>
                    <p class="text-3xl font-bold text-green-400">{validation_result['valid_users'] if validation_result else 0}</p>
                </div>
                <div class="text-center">
                    <p class="text-lg font-semibold">数据异常</p>
                    <p class="text-3xl font-bold text-red-400">{validation_result['invalid_users'] if validation_result else 0}</p>
                </div>
                <div class="text-center">
                    <p class="text-lg font-semibold">完整性率</p>
                    <p class="text-3xl font-bold text-purple-400">{validation_result['validation_rate']*100:.1f}%</p>
                </div>
            </div>
        </div>

        <!-- 修复操作 -->
        <div class="card p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold text-yellow-400 mb-4">🛠️ 数据修复操作</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button onclick="runRepair('purchase_fields')"
                        class="px-4 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 font-bold">
                    修复Purchase字段缺失
                </button>
                <button onclick="runRepair('consumption_records')"
                        class="px-4 py-3 bg-green-600 text-white rounded hover:bg-green-700 font-bold">
                    补充消费记录
                </button>
                <button onclick="runRepair('quota_records')"
                        class="px-4 py-3 bg-purple-600 text-white rounded hover:bg-purple-700 font-bold">
                    创建配额使用记录
                </button>
                <button onclick="runRepair('points_records')"
                        class="px-4 py-3 bg-yellow-600 text-white rounded hover:bg-yellow-700 font-bold">
                    修复积分记录
                </button>
            </div>
            <div class="mt-4">
                <button onclick="runRepair('full_repair')"
                        class="w-full px-4 py-3 bg-red-600 text-white rounded hover:bg-red-700 font-bold">
                    🚨 运行完整修复（谨慎操作）
                </button>
            </div>
        </div>

        <!-- 修复结果 -->
        <div id="repair-result" class="card p-6 rounded-lg" style="display: none;">
            <h2 class="text-xl font-semibold text-yellow-400 mb-4">修复结果</h2>
            <div id="repair-content" class="text-sm"></div>
        </div>
    </div>

    <script>
        function runRepair(repairType) {{
            const resultDiv = document.getElementById('repair-result');
            const contentDiv = document.getElementById('repair-content');

            resultDiv.style.display = 'block';
            contentDiv.innerHTML = '<p class="text-blue-400">正在执行修复操作，请稍候...</p>';

            fetch('/admin/data-repair/run', {{
                method: 'POST',
                headers: {{
                    'Content-Type': 'application/json',
                }},
                body: JSON.stringify({{repair_type: repairType}})
            }})
            .then(response => response.json())
            .then(data => {{
                if (data.success) {{
                    contentDiv.innerHTML = '<div class="text-green-400"><h3>✅ 修复成功</h3><pre>' +
                                         JSON.stringify(data.results, null, 2) + '</pre></div>';
                }} else {{
                    contentDiv.innerHTML = '<div class="text-red-400"><h3>❌ 修复失败</h3><p>' +
                                         data.error + '</p></div>';
                }}
            }})
            .catch(error => {{
                contentDiv.innerHTML = '<div class="text-red-400"><h3>❌ 请求失败</h3><p>' +
                                     error.message + '</p></div>';
            }});
        }}
    </script>
</body>
</html>
        '''

    except Exception as e:
        return f'''
        <div style="background: #121212; color: white; padding: 20px;">
            <h1>数据修复控制台错误</h1>
            <p>错误信息: {str(e)}</p>
            <a href="/admin/" style="color: #60a5fa;">返回管理后台</a>
        </div>
        '''

@admin_bp.route('/data-repair/run', methods=['POST'])
@login_required
@admin_required
def run_data_repair():
    """执行数据修复操作"""
    try:
        from services.data_repair import DataRepairTool

        data = request.get_json()
        repair_type = data.get('repair_type')

        if repair_type == 'purchase_fields':
            success, message = DataRepairTool.repair_purchase_missing_fields()
            return jsonify({'success': success, 'results': [message]})

        elif repair_type == 'consumption_records':
            success, message = DataRepairTool.repair_missing_consumption_records()
            return jsonify({'success': success, 'results': [message]})

        elif repair_type == 'quota_records':
            success, message = DataRepairTool.create_missing_quota_records()
            return jsonify({'success': success, 'results': [message]})

        elif repair_type == 'points_records':
            success, message = DataRepairTool.repair_points_records()
            return jsonify({'success': success, 'results': [message]})

        elif repair_type == 'full_repair':
            results = DataRepairTool.run_full_repair()
            return jsonify({'success': True, 'results': results})

        else:
            return jsonify({'success': False, 'error': '未知的修复类型'})

    except Exception as e:
        logger.error(f"执行数据修复失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)})



@admin_bp.route('/vip-status')
@login_required
@staff_required
def vip_status_page():
    """VIP状态监控页面"""
    try:
        # 获取VIP状态指示器
        vip_status_data = get_vip_status_summary()
        all_vip_status = []

        # 获取所有VIP用户
        vip_user_ids = db.session.query(UserFinance.user_id).filter(UserFinance.vip_level > 0).all()
        vip_user_ids = [uid[0] for uid in vip_user_ids]

        for user_id in vip_user_ids:
            user = User.query.get(user_id)
            if user:
                status = get_vip_status_indicator(user_id)
                all_vip_status.append({
                    'user': user,
                    'status': status
                })

        # 生成HTML
        user_rows = []
        for item in all_vip_status:
            user = item['user']
            status = item['status']

            # 状态指示灯
            if status['status'] == 'green':
                light = '🟢'
                status_class = 'text-green-400'
            elif status['status'] == 'yellow':
                light = '🟡'
                status_class = 'text-yellow-400'
            else:
                light = '🔴'
                status_class = 'text-red-400'

            # 获取财务信息
            finance = UserFinance.query.filter_by(user_id=user.id).first()
            if finance:
                vip_info = f"VIP{finance.vip_level}"
                balance = finance.balance / 100 if finance.balance else 0
                total_spent = finance.total_spent / 100 if finance.total_spent else 0
                finance_text = f"余额: ¥{balance:.2f} | 消费: ¥{total_spent:.2f}"
            else:
                vip_info = "无财务记录"
                finance_text = "N/A"

            user_row = f'''
            <tr class="border-b border-gray-700 hover:bg-gray-800">
                <td class="py-3 px-4 text-center">{light}</td>
                <td class="py-3 px-4">
                    <div class="font-medium">{user.username}</div>
                    <div class="text-xs text-gray-400">{user.email or "无邮箱"}</div>
                </td>
                <td class="py-3 px-4 text-center">
                    <span class="px-2 py-1 bg-yellow-800 text-yellow-300 rounded text-xs">{vip_info}</span>
                </td>
                <td class="py-3 px-4 text-xs">{finance_text}</td>
                <td class="py-3 px-4 {status_class} text-sm">{status['message']}</td>
                <td class="py-3 px-4 text-xs text-gray-400">{status.get('details', '')}</td>
            </tr>
            '''
            user_rows.append(user_row)

        return f'''
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIP状态监控</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {{ background-color: #121212; color: #e0e0e0; }}
        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-yellow-400">🚦 VIP状态监控</h1>
            <a href="/admin/" class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                ← 返回管理后台
            </a>
        </div>

        <!-- VIP状态汇总 -->
        <div class="card rounded-lg p-4 mb-6">
            <h3 class="text-lg font-semibold text-yellow-400 mb-3">📊 VIP状态概览</h3>
            <div class="flex space-x-6">
                <div class="flex items-center">
                    <span class="text-lg mr-2">🟢</span>
                    <span>正常: {vip_status_data.get('green', 0)}</span>
                </div>
                <div class="flex items-center">
                    <span class="text-lg mr-2">🟡</span>
                    <span>警告: {vip_status_data.get('yellow', 0)}</span>
                </div>
                <div class="flex items-center">
                    <span class="text-lg mr-2">🔴</span>
                    <span>异常: {vip_status_data.get('red', 0)}</span>
                </div>
                <div class="ml-auto">
                    <span class="text-sm text-gray-400">健康率: {vip_status_data.get('health_rate', 0):.1f}%</span>
                </div>
            </div>
            <div class="mt-2 text-sm text-gray-400">
                说明: 🟢正常 🟡需注意 🔴需处理
            </div>
        </div>

        <!-- VIP用户详情 -->
        <div class="card rounded-lg p-6">
            <h3 class="text-lg font-semibold text-yellow-400 mb-4">VIP用户详情</h3>
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr class="border-b border-gray-700 bg-gray-900">
                            <th class="text-center py-3 px-4 text-yellow-400">状态</th>
                            <th class="text-left py-3 px-4 text-yellow-400">用户</th>
                            <th class="text-center py-3 px-4 text-yellow-400">VIP等级</th>
                            <th class="text-left py-3 px-4 text-yellow-400">财务信息</th>
                            <th class="text-left py-3 px-4 text-yellow-400">状态说明</th>
                            <th class="text-left py-3 px-4 text-yellow-400">详情</th>
                        </tr>
                    </thead>
                    <tbody>
                        {"".join(user_rows)}
                    </tbody>
                </table>
            </div>

            <div class="mt-4 text-center text-gray-400">
                总VIP用户: {len(all_vip_status)} | 刷新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            </div>
        </div>
    </div>
</body>
</html>
        '''

    except Exception as e:
        return f'''
        <div style="background: #121212; color: white; padding: 20px;">
            <h1>VIP状态监控错误</h1>
            <p>错误信息: {str(e)}</p>
            <a href="/admin/" style="color: #60a5fa;">返回管理后台</a>
        </div>
        '''

@admin_bp.route('/test-clean')
@login_required
@staff_required
def test_clean_page():
    """完全干净的测试页面"""
    return '''
<!DOCTYPE html>
<html>
<head>
    <title>干净测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }
        .test-link {
            display: inline-block;
            padding: 15px 30px;
            margin: 10px;
            background: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 16px;
        }
        .test-link:hover { background: #45a049; }
        .log { background: #333; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🧪 干净的链接测试页面</h1>
    <p>这个页面没有任何复杂的JavaScript或CSS，用于测试基本的链接跳转。</p>

    <div>
        <h2>测试链接：</h2>
        <a href="/admin/users" class="test-link">👥 用户管理 (普通链接)</a>
        <a href="/admin/" class="test-link">🏠 管理后台首页</a>
        <a href="/admin/articles" class="test-link">📄 文章管理</a>
    </div>

    <div>
        <h2>强制跳转测试：</h2>
        <button class="test-link" onclick="window.location.href='/admin/users'">🚀 强制跳转到用户管理</button>
        <button class="test-link" onclick="window.open('/admin/users', '_blank')">🪟 新窗口打开用户管理</button>
    </div>

    <div id="log" class="log">
        <h3>测试日志：</h3>
        <div id="log-content">页面加载完成，等待测试...</div>
    </div>

    <script>
        function log(message) {
            const logContent = document.getElementById('log-content');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += '<br>[' + timestamp + '] ' + message;
        }

        // 监听所有点击事件
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'A') {
                log('点击链接: ' + e.target.href);
                log('链接文本: ' + e.target.textContent);

                // 检查是否被阻止
                setTimeout(function() {
                    if (window.location.href === e.target.href) {
                        log('✅ 跳转成功');
                    } else {
                        log('❌ 跳转失败，当前URL: ' + window.location.href);
                    }
                }, 100);
            } else if (e.target.tagName === 'BUTTON') {
                log('点击按钮: ' + e.target.textContent);
            }
        });

        // 监听页面跳转
        window.addEventListener('beforeunload', function() {
            log('页面即将跳转...');
        });

        log('JavaScript加载完成');
    </script>
</body>
</html>
    '''

@admin_bp.route('/api/users', methods=['GET'])
@login_required
@admin_required
def get_users():
    """获取用户列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # 支持按角色和状态过滤
        role = request.args.get('role')
        status = request.args.get('status')
        
        users_query = User.query
        
        if role:
            users_query = users_query.filter(User.role == role)
            
        if status:
            users_query = users_query.filter(User.status == status)
            
        # 按创建时间倒序排列
        users_query = users_query.order_by(User.created_at.desc())
        
        # 执行分页查询
        pagination = users_query.paginate(page=page, per_page=per_page)
        
        # 格式化结果 - 包含完整的用户数据管理信息
        users_data = []
        for user in pagination.items:
            # 获取用户财务信息
            finance_info = {
                'balance': 0,
                'total_spent': 0,
                'vip_level': 0,
                'vip_expire_at': None,
                'basic_quota_used': 0,
                'premium_quota_used': 0,
                'quota_reset_date': None,
                'remaining_quotas': {'basic_remaining': 0, 'premium_remaining': 0, 'can_exchange': False, 'exchange_available': 0}
            }

            # 仅管理员可以看到财务信息
            if user.finance and current_user.can_access_finance():
                finance_info.update({
                    'balance': user.finance.balance,
                    'total_spent': user.finance.total_spent,
                    'vip_level': user.finance.vip_level,
                    'vip_expire_at': user.finance.vip_expire_at.strftime('%Y-%m-%d %H:%M:%S') if user.finance.vip_expire_at else None,
                    'basic_quota_used': user.finance.basic_quota_used,
                    'premium_quota_used': user.finance.premium_quota_used,
                    'quota_reset_date': user.finance.quota_reset_date.strftime('%Y-%m-%d') if user.finance.quota_reset_date else None,
                    'remaining_quotas': user.finance.get_remaining_quotas()
                })

            # 统计用户行为数据 - 使用安全的查询方式
            try:
                articles_count = user.articles.count() if hasattr(user, 'articles') and user.articles else 0
            except:
                articles_count = 0

            try:
                bookmarks_count = user.bookmarks.count() if hasattr(user, 'bookmarks') and user.bookmarks else 0
            except:
                bookmarks_count = 0

            try:
                favorites_count = user.favorites.count() if hasattr(user, 'favorites') and user.favorites else 0
            except:
                favorites_count = 0

            try:
                support_tickets_count = user.support_tickets.count() if hasattr(user, 'support_tickets') and user.support_tickets else 0
            except:
                support_tickets_count = 0

            try:
                # 使用数据库查询而不是关系属性
                from models import ParagraphBookmark
                paragraph_bookmarks_count = ParagraphBookmark.query.filter_by(user_id=user.id).count()
            except:
                paragraph_bookmarks_count = 0

            user_stats = {
                'articles_count': articles_count,
                'bookmarks_count': bookmarks_count,
                'favorites_count': favorites_count,
                'support_tickets_count': support_tickets_count,
                'paragraph_bookmarks_count': paragraph_bookmarks_count
            }

            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'role': user.role,
                'email_verified': user.email_verified,
                'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else None,
                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else None,
                'finance': finance_info,
                'stats': user_stats
            })
        
        return jsonify({
            'success': True,
            'users': users_data,
            'total': pagination.total,
            'pages': pagination.pages,
            'current_page': pagination.page
        })
    except Exception as e:
        logger.error(f"获取用户列表API失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users/stats', methods=['GET'])
@login_required
@admin_required
def api_get_users_stats():
    """获取用户统计信息API"""
    try:
        from datetime import datetime, timedelta
        from sqlalchemy import func

        # 基础统计
        total_users = User.query.count()
        active_users = User.query.filter(User.last_login.isnot(None)).count()
        vip_users = User.query.filter(User.is_vip == True).count()

        # 时间统计
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)

        today_registered = User.query.filter(User.created_at >= today).count()
        week_registered = User.query.filter(User.created_at >= week_ago).count()
        month_registered = User.query.filter(User.created_at >= month_ago).count()

        # 角色分布
        role_stats = db.session.query(
            User.role,
            func.count(User.id).label('count')
        ).group_by(User.role).all()

        role_distribution = {stat.role: stat.count for stat in role_stats}

        # 会员类型分布
        membership_stats = db.session.query(
            User.membership_type,
            func.count(User.id).label('count')
        ).group_by(User.membership_type).all()

        membership_distribution = {
            stat.membership_type or 'free': stat.count
            for stat in membership_stats
        }

        # 注册趋势（最近7天）
        registration_trend = []
        for i in range(7):
            date = today - timedelta(days=i)
            count = User.query.filter(
                func.date(User.created_at) == date
            ).count()
            registration_trend.append({
                'date': date.strftime('%Y-%m-%d'),
                'count': count
            })
        registration_trend.reverse()

        stats = {
            'total_users': total_users,
            'active_users': active_users,
            'vip_users': vip_users,
            'today_registered': today_registered,
            'this_week_registered': week_registered,
            'this_month_registered': month_registered,
            'role_distribution': role_distribution,
            'membership_distribution': membership_distribution,
            'registration_trend': registration_trend
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取用户统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>', methods=['GET'])
@login_required
@admin_required
def get_user(user_id):
    """获取单个用户详细信息"""
    try:
        from sqlalchemy.orm import joinedload
        user = User.query.options(joinedload(User.finance)).filter_by(id=user_id).first()

        if not user:
            return jsonify({'success': False, 'error': '用户不存在'}), 404

        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'role': user.role,
            'email_verified': user.email_verified,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'finance': None
        }

        # 添加财务信息 - 仅管理员可见
        if user.finance and current_user.can_access_finance():
            user_data['finance'] = {
                'vip_level': user.finance.vip_level,
                'vip_expire_at': user.finance.vip_expire_at.isoformat() if user.finance.vip_expire_at else None,
                'basic_quota': user.finance.basic_quota,
                'basic_quota_used': user.finance.basic_quota_used,
                'premium_quota': user.finance.premium_quota,
                'premium_quota_used': user.finance.premium_quota_used,
                'balance': float(user.finance.balance) if user.finance.balance else 0.0
            }

        return jsonify({'success': True, 'user': user_data})

    except Exception as e:
        logger.error(f"获取用户信息失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>/details', methods=['GET'])
@login_required
@admin_required
def get_user_details(user_id):
    """获取用户详细信息，包括消费记录、推广信息等"""
    try:
        from sqlalchemy.orm import joinedload
        from models import UserConsumptionRecord, UserReferralRecord, UserPointsRecord, Article

        # 获取用户基本信息和财务信息
        user = User.query.options(joinedload(User.finance)).filter_by(id=user_id).first()

        if not user:
            return jsonify({'success': False, 'error': '用户不存在'}), 404

        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'role': user.role,
            'email_verified': user.email_verified,
            'created_at': user.created_at.isoformat() if user.created_at else None,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'finance': None,
            'consumption_records': [],
            'referral_rewards': [],
            'referred_records': []
        }

        # 添加财务信息 - 仅管理员可见
        if user.finance and current_user.can_access_finance():
            user_data['finance'] = {
                'vip_level': user.finance.vip_level,
                'vip_expire_at': user.finance.vip_expire_at.isoformat() if user.finance.vip_expire_at else None,
                'basic_quota': user.finance.basic_quota,
                'basic_quota_used': user.finance.basic_quota_used,
                'premium_quota': user.finance.premium_quota,
                'premium_quota_used': user.finance.premium_quota_used,
                'balance': float(user.finance.balance) if user.finance.balance else 0.0,
                'points_balance': user.finance.points_balance,
                'referral_code': user.finance.referral_code,
                'referred_by': user.finance.referred_by,
                'last_quota_reset': user.finance.last_quota_reset.isoformat() if user.finance.last_quota_reset else None
            }

        # 获取购买记录统计（Purchase表）
        from models import Purchase
        purchases = Purchase.query.filter_by(user_id=user_id).all()

        # 购买统计
        purchase_stats = {
            'total_purchases': len(purchases),
            'basic_purchases': len([p for p in purchases if p.content_type == 'basic']),
            'premium_purchases': len([p for p in purchases if p.content_type == 'premium']),
            'total_spent': sum(p.amount for p in purchases),
            'cash_purchases': len([p for p in purchases if p.payment_method in ['alipay', 'wechat', 'mock']]),
            'quota_purchases': len([p for p in purchases if p.payment_method == 'quota']),
            'points_purchases': len([p for p in purchases if p.payment_method == 'points'])
        }
        user_data['purchase_stats'] = purchase_stats

        # 最近购买记录（最多10条）
        recent_purchases = Purchase.query.filter_by(user_id=user_id)\
                                        .order_by(Purchase.created_at.desc())\
                                        .limit(10).all()

        user_data['recent_purchases'] = []
        for purchase in recent_purchases:
            article = Article.query.get(purchase.article_id)
            user_data['recent_purchases'].append({
                'id': purchase.id,
                'article_title': article.title if article else '文章已删除',
                'content_type': purchase.content_type,
                'amount': purchase.amount,
                'payment_method': purchase.payment_method,
                'transaction_id': purchase.transaction_id,
                'created_at': purchase.created_at.isoformat() if purchase.created_at else None
            })

        # 获取最近的消费记录（最多10条）
        consumption_records = UserConsumptionRecord.query.filter_by(user_id=user_id)\
            .join(Article, UserConsumptionRecord.article_id == Article.id)\
            .order_by(UserConsumptionRecord.created_at.desc())\
            .limit(10).all()

        for record in consumption_records:
            user_data['consumption_records'].append({
                'id': record.id,
                'article_id': record.article_id,
                'article_title': record.article.title if record.article else None,
                'content_type': record.content_type,
                'cost_type': record.cost_type,
                'cost_amount': record.cost_amount,
                'created_at': record.created_at.isoformat()
            })

        # 获取推广奖励记录
        referral_rewards = UserReferralRecord.query.filter_by(referrer_id=user_id)\
            .order_by(UserReferralRecord.created_at.desc()).all()

        for reward in referral_rewards:
            user_data['referral_rewards'].append({
                'id': reward.id,
                'referred_id': reward.referred_id,
                'reward_type': reward.reward_type,
                'reward_amount': reward.reward_amount,
                'trigger_event': reward.trigger_event,
                'created_at': reward.created_at.isoformat()
            })

        # 获取被推广记录
        referred_records = UserReferralRecord.query.filter_by(referred_id=user_id)\
            .order_by(UserReferralRecord.created_at.desc()).all()

        for record in referred_records:
            user_data['referred_records'].append({
                'id': record.id,
                'referrer_id': record.referrer_id,
                'reward_type': record.reward_type,
                'reward_amount': record.reward_amount,
                'trigger_event': record.trigger_event,
                'created_at': record.created_at.isoformat()
            })

        return jsonify({'success': True, 'user': user_data})

    except Exception as e:
        logger.error(f"获取用户详细信息失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>', methods=['PUT'])
@login_required
@admin_required
def update_user(user_id):
    """更新用户信息"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()
        
        # 不允许修改超级管理员状态
        if user.role == 'superadmin' and current_user.role != 'superadmin':
            return jsonify({'success': False, 'error': '您没有权限修改超级管理员用户'}), 403
        
        # 不允许用户修改自己的角色（防止权限降级）
        if user.id == current_user.id and 'role' in data and data['role'] != user.role:
            return jsonify({'success': False, 'error': '不能修改自己的角色'}), 403
        
        if 'username' in data:
            # 检查用户名是否已被使用
            if data['username'] != user.username:
                existing_user = User.query.filter_by(username=data['username']).first()
                if existing_user:
                    return jsonify({'success': False, 'error': '此用户名已被使用'}), 400
            user.username = data['username']
            
        if 'email' in data:
            # 检查邮箱是否已被使用
            if data['email'] != user.email:
                existing_user = User.query.filter_by(email=data['email']).first()
                if existing_user:
                    return jsonify({'success': False, 'error': '此邮箱已被使用'}), 400
            user.email = data['email']
            
        if 'role' in data and current_user.role == 'superadmin':
            user.role = data['role']
            
        if 'status' in data:
            user.status = data['status']
            
        db.session.commit()
        return jsonify({'success': True, 'message': '用户信息已更新'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新用户 {user_id} 失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>/reset_password', methods=['POST'])
@login_required
@admin_required
def reset_user_password(user_id):
    """重置用户密码"""
    try:
        user = User.query.get_or_404(user_id)
        data = request.get_json()
        
        # 超级管理员密码只能由超级管理员重置
        if user.role == 'superadmin' and current_user.role != 'superadmin':
            return jsonify({'success': False, 'error': '您没有权限重置超级管理员密码'}), 403
        
        new_password = data.get('new_password')
        if not new_password or len(new_password) < 8:
            return jsonify({'success': False, 'error': '新密码长度必须至少为8个字符'}), 400
            
        # 设置新密码
        user.password = generate_password_hash(new_password)
        
        db.session.commit()
        return jsonify({'success': True, 'message': '用户密码已重置'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"重置用户 {user_id} 密码失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users', methods=['POST'])
@login_required
@admin_required
def create_user():
    """创建新用户"""
    try:
        data = request.get_json()
        
        username = data.get('username')
        email = data.get('email')
        password = data.get('password')
        role = data.get('role', 'user')
        
        # 验证必填字段
        if not username or not email or not password:
            return jsonify({'success': False, 'error': '用户名、邮箱和密码都是必需的'}), 400
            
        # 验证密码长度
        if len(password) < 8:
            return jsonify({'success': False, 'error': '密码长度必须至少为8个字符'}), 400
            
        # 检查用户名是否已被使用
        existing_user = User.query.filter_by(username=username).first()
        if existing_user:
            return jsonify({'success': False, 'error': '此用户名已被使用'}), 400
            
        # 检查邮箱是否已被使用
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            return jsonify({'success': False, 'error': '此邮箱已被使用'}), 400
            
        # 创建新用户
        new_user = User(
            username=username,
            email=email,
            password=generate_password_hash(password),
            role=role,
            status='active'
        )
        
        db.session.add(new_user)
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': '用户创建成功',
            'user': {
                'id': new_user.id,
                'username': new_user.username,
                'email': new_user.email,
                'role': new_user.role,
                'status': new_user.status
            }
        }), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"创建用户失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
@admin_required
def delete_user(user_id):
    """删除用户"""
    try:
        user = User.query.get_or_404(user_id)
        
        # 不允许删除超级管理员
        if user.role == 'superadmin':
            return jsonify({'success': False, 'error': '不能删除超级管理员用户'}), 403
            
        # 不允许删除自己
        if user.id == current_user.id:
            return jsonify({'success': False, 'error': '不能删除当前登录的用户账户'}), 403
            
        db.session.delete(user)
        db.session.commit()
        return jsonify({'success': True, 'message': '用户已删除'})
    except Exception as e:
        db.session.rollback()
        logger.error(f"删除用户 {user_id} 失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users/<int:user_id>/vip', methods=['PUT'])
@login_required
@finance_required
def update_user_vip(user_id):
    """更新用户VIP状态和配额"""
    try:
        from models import UserFinance
        from datetime import datetime, timedelta

        user = User.query.get_or_404(user_id)
        data = request.get_json()

        # 确保用户有财务记录
        if not user.finance:
            user_finance = UserFinance(user_id=user.id)
            db.session.add(user_finance)
            db.session.flush()  # 获取ID但不提交
        else:
            user_finance = user.finance

        # 更新VIP等级
        if 'vip_level' in data:
            vip_level = int(data['vip_level'])
            if vip_level not in [0, 1, 2]:
                return jsonify({'success': False, 'error': 'VIP等级必须是0、1或2'}), 400
            user_finance.vip_level = vip_level

            # 如果设置为VIP，自动设置过期时间（如果没有提供）
            if vip_level > 0 and 'vip_expire_at' not in data:
                # 默认设置为1个月后过期
                user_finance.vip_expire_at = datetime.utcnow() + timedelta(days=30)

        # 更新VIP过期时间
        if 'vip_expire_at' in data and data['vip_expire_at']:
            try:
                user_finance.vip_expire_at = datetime.strptime(data['vip_expire_at'], '%Y-%m-%d %H:%M:%S')
            except ValueError:
                try:
                    user_finance.vip_expire_at = datetime.strptime(data['vip_expire_at'], '%Y-%m-%d')
                except ValueError:
                    return jsonify({'success': False, 'error': 'VIP过期时间格式错误，请使用YYYY-MM-DD或YYYY-MM-DD HH:MM:SS'}), 400

        # 重置配额
        if data.get('reset_quota', False):
            user_finance.basic_quota_used = 0
            user_finance.premium_quota_used = 0
            user_finance.quota_reset_date = datetime.utcnow()

        # 更新配额总量
        if 'basic_quota' in data:
            user_finance.basic_quota = max(0, int(data['basic_quota']))

        if 'premium_quota' in data:
            user_finance.premium_quota = max(0, int(data['premium_quota']))

        # 手动调整配额使用量
        if 'basic_quota_used' in data:
            user_finance.basic_quota_used = max(0, int(data['basic_quota_used']))

        if 'premium_quota_used' in data:
            user_finance.premium_quota_used = max(0, int(data['premium_quota_used']))

        # 更新余额
        if 'balance' in data:
            user_finance.balance = float(data['balance'])

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '用户VIP状态已更新',
            'finance': {
                'vip_level': user_finance.vip_level,
                'vip_expire_at': user_finance.vip_expire_at.strftime('%Y-%m-%d %H:%M:%S') if user_finance.vip_expire_at else None,
                'basic_quota': user_finance.basic_quota,
                'basic_quota_used': user_finance.basic_quota_used,
                'premium_quota': user_finance.premium_quota,
                'premium_quota_used': user_finance.premium_quota_used,
                'balance': float(user_finance.balance) if user_finance.balance else 0.0
            }
        })
    except Exception as e:
        db.session.rollback()
        logger.error(f"更新用户 {user_id} VIP状态失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# --- 公告管理 ---
@admin_bp.route('/announcements')
@login_required
@admin_required
def announcement_list():
    """公告管理页面"""
    try:
        return render_template('admin/announcement_list.html')
    except Exception as e:
        logger.error(f"加载公告管理页面失败: {str(e)}")
        flash('加载公告管理页面失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/announcements', methods=['GET'])
@login_required
@admin_required
def api_get_announcements():
    """获取公告列表API"""
    try:
        from models import Announcement
        announcements = Announcement.query.order_by(Announcement.created_at.desc()).all()

        result = []
        for announcement in announcements:
            result.append({
                'id': announcement.id,
                'title': announcement.title,
                'content': announcement.content,
                'priority': announcement.priority,
                'is_active': announcement.is_active,
                'start_date': announcement.start_date.isoformat() if announcement.start_date else None,
                'end_date': announcement.end_date.isoformat() if announcement.end_date else None,
                'created_at': announcement.created_at.isoformat() if announcement.created_at else None
            })

        return jsonify({'success': True, 'data': result})
    except Exception as e:
        logger.error(f"获取公告列表失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/announcements/stats', methods=['GET'])
@login_required
@admin_required
def api_get_announcements_stats():
    """获取公告统计信息API"""
    try:
        from models import Announcement
        from datetime import datetime, timedelta
        from sqlalchemy import func

        # 基础统计
        total_announcements = Announcement.query.count()

        # 活跃公告（未过期的）
        now = datetime.now()
        active_announcements = Announcement.query.filter(
            Announcement.is_active == True,
            Announcement.end_date >= now
        ).count()

        # 过期公告
        expired_announcements = Announcement.query.filter(
            Announcement.end_date < now
        ).count()

        # 时间统计
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)

        today_created = Announcement.query.filter(
            func.date(Announcement.created_at) == today
        ).count()

        week_created = Announcement.query.filter(
            Announcement.created_at >= week_ago
        ).count()

        # 公告类型分布（这里使用模拟数据，实际应该从数据库字段获取）
        announcement_types = {
            'system': total_announcements // 3,
            'promotion': total_announcements // 3,
            'maintenance': total_announcements - (total_announcements // 3) * 2
        }

        # 浏览统计（模拟数据，实际应该从浏览记录表获取）
        total_views = total_announcements * 1028  # 模拟总浏览量
        avg_views_per_announcement = 1028 if total_announcements > 0 else 0

        stats = {
            'total_announcements': total_announcements,
            'active_announcements': active_announcements,
            'expired_announcements': expired_announcements,
            'today_created': today_created,
            'this_week_created': week_created,
            'announcement_types': announcement_types,
            'view_stats': {
                'total_views': total_views,
                'avg_views_per_announcement': avg_views_per_announcement
            }
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取公告统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/announcements', methods=['POST'])
@login_required
@admin_required
def api_create_announcement():
    """创建公告API"""
    try:
        from models import Announcement
        data = request.get_json()

        announcement = Announcement(
            title=data.get('title'),
            content=data.get('content'),
            priority=data.get('priority', 'normal'),
            is_active=data.get('is_active', True),
            start_date=datetime.strptime(data.get('start_date'), '%Y-%m-%dT%H:%M') if data.get('start_date') else None,
            end_date=datetime.strptime(data.get('end_date'), '%Y-%m-%dT%H:%M') if data.get('end_date') else None
        )

        db.session.add(announcement)
        db.session.commit()

        return jsonify({'success': True, 'message': '公告创建成功'})
    except Exception as e:
        logger.error(f"创建公告失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['GET'])
@login_required
@admin_required
def api_get_announcement(announcement_id):
    """获取单个公告API"""
    try:
        from models import Announcement
        announcement = Announcement.query.get_or_404(announcement_id)

        result = {
            'id': announcement.id,
            'title': announcement.title,
            'content': announcement.content,
            'priority': announcement.priority,
            'is_active': announcement.is_active,
            'start_date': announcement.start_date.isoformat() if announcement.start_date else None,
            'end_date': announcement.end_date.isoformat() if announcement.end_date else None,
            'created_at': announcement.created_at.isoformat() if announcement.created_at else None
        }

        return jsonify({'success': True, 'data': result})
    except Exception as e:
        logger.error(f"获取公告失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['PUT'])
@login_required
@admin_required
def api_update_announcement(announcement_id):
    """更新公告API"""
    try:
        from models import Announcement
        announcement = Announcement.query.get_or_404(announcement_id)
        data = request.get_json()

        announcement.title = data.get('title', announcement.title)
        announcement.content = data.get('content', announcement.content)
        announcement.priority = data.get('priority', announcement.priority)
        announcement.is_active = data.get('is_active', announcement.is_active)

        if data.get('start_date'):
            announcement.start_date = datetime.strptime(data.get('start_date'), '%Y-%m-%dT%H:%M')
        if data.get('end_date'):
            announcement.end_date = datetime.strptime(data.get('end_date'), '%Y-%m-%dT%H:%M')

        db.session.commit()

        return jsonify({'success': True, 'message': '公告更新成功'})
    except Exception as e:
        logger.error(f"更新公告失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['DELETE'])
@login_required
@admin_required
def api_delete_announcement(announcement_id):
    """删除公告API"""
    try:
        from models import Announcement
        announcement = Announcement.query.get_or_404(announcement_id)

        db.session.delete(announcement)
        db.session.commit()

        return jsonify({'success': True, 'message': '公告删除成功'})
    except Exception as e:
        logger.error(f"删除公告失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

# --- 积分管理 ---
@admin_bp.route('/points')
@login_required
@admin_required
def points_management():
    """积分管理页面"""
    try:
        return render_template('admin/points_management.html')
    except Exception as e:
        logger.error(f"加载积分管理页面失败: {str(e)}")
        flash('加载积分管理页面失败', 'error')
        return redirect(url_for('admin.dashboard'))

@admin_bp.route('/api/points/transactions', methods=['GET'])
@login_required
@admin_required
def api_get_point_transactions():
    """获取积分交易记录API"""
    try:
        from models import PointTransaction
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        transactions = PointTransaction.query.order_by(PointTransaction.created_at.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        result = []
        for transaction in transactions.items:
            result.append({
                'id': transaction.id,
                'user_id': transaction.user_id,
                'username': transaction.user.username if transaction.user else '未知用户',
                'points': transaction.points,
                'transaction_type': transaction.transaction_type,
                'description': transaction.description,
                'created_at': transaction.created_at.isoformat() if transaction.created_at else None
            })

        return jsonify({
            'success': True,
            'data': result,
            'pagination': {
                'page': transactions.page,
                'pages': transactions.pages,
                'per_page': transactions.per_page,
                'total': transactions.total
            }
        })
    except Exception as e:
        logger.error(f"获取积分交易记录失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/points/adjust', methods=['POST'])
@login_required
@admin_required
def api_adjust_user_points():
    """调整用户积分API"""
    try:
        from models import PointTransaction
        data = request.get_json()

        user_id = data.get('user_id')
        points = data.get('points')
        description = data.get('description', '管理员调整')

        user = User.query.get_or_404(user_id)

        # 创建积分交易记录
        transaction = PointTransaction(
            user_id=user_id,
            points=points,
            transaction_type='admin_adjust',
            description=description
        )

        # 更新用户积分
        user.points = (user.points or 0) + points

        db.session.add(transaction)
        db.session.commit()

        return jsonify({'success': True, 'message': '积分调整成功'})
    except Exception as e:
        logger.error(f"调整用户积分失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/points/statistics', methods=['GET'])
@login_required
@admin_required
def api_get_points_statistics():
    """获取积分统计信息API"""
    try:
        from models import PointTransaction
        from sqlalchemy import func
        from datetime import date

        # 总积分发放（正数）
        total_issued = db.session.query(func.sum(PointTransaction.points)).filter(
            PointTransaction.points > 0
        ).scalar() or 0

        # 总积分消费（负数的绝对值）
        total_spent = db.session.query(func.sum(PointTransaction.points)).filter(
            PointTransaction.points < 0
        ).scalar() or 0
        total_spent = abs(total_spent)

        # 有积分的用户数
        active_users = db.session.query(func.count(func.distinct(User.id))).filter(
            User.points > 0
        ).scalar() or 0

        # 今日交易数
        today = date.today()
        today_transactions = PointTransaction.query.filter(
            func.date(PointTransaction.created_at) == today
        ).count()

        return jsonify({
            'success': True,
            'data': {
                'total_issued': total_issued,
                'total_spent': total_spent,
                'active_users': active_users,
                'today_transactions': today_transactions
            }
        })
    except Exception as e:
        logger.error(f"获取积分统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/users/search', methods=['GET'])
@login_required
@admin_required
def api_search_users():
    """搜索用户API"""
    try:
        query = request.args.get('q', '').strip()
        if len(query) < 2:
            return jsonify({'success': True, 'data': []})

        # 搜索用户名或邮箱
        users = User.query.filter(
            db.or_(
                User.username.ilike(f'%{query}%'),
                User.email.ilike(f'%{query}%')
            )
        ).limit(10).all()

        result = []
        for user in users:
            result.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'points': user.points or 0
            })

        return jsonify({'success': True, 'data': result})
    except Exception as e:
        logger.error(f"搜索用户失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# --- 文章媒体文件管理 ---
@admin_bp.route('/api/articles/<int:article_id>', methods=['GET'])
@login_required
@admin_required
def api_get_article_info(article_id):
    """获取文章完整信息API - 用于Vue3管理后台编辑页面"""
    try:
        article = Article.query.get_or_404(article_id)

        return jsonify({
            'success': True,
            'article': {
                'id': article.id,
                'title': article.title,
                'content': article.content,
                'premium_content': article.premium_content,
                'status': article.status,
                'reader_id': article.reader_id,
                'author_id': article.author_id,
                'tags': [{'id': tag.id, 'name': tag.name} for tag in article.tags] if article.tags else [],
                'publish_date': article.publish_date.isoformat() if article.publish_date else None,
                'language_code': article.language_code,
                'content_type': article.content_type,
                'price': article.price,
                'premium_price': article.premium_price,
                'cover_image': article.cover_image,
                'cover_url': article.cover_url,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'updated_at': article.updated_at.isoformat() if article.updated_at else None,
                'views': article.views,
                'original_id': article.original_id,
                'author': article.author,
                'original_audio_url': article.original_audio_url,
                'content_images': article.content_images,
                'audio_sync_data': article.audio_sync_data
            }
        })
    except Exception as e:
        logger.error(f"获取文章信息失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/articles/<int:article_id>/media', methods=['GET'])
@login_required
@staff_required
def api_get_article_media(article_id):
    """获取文章关联的媒体文件API"""
    try:
        from models import MediaFile
        article = Article.query.get_or_404(article_id)

        # 获取与文章关联的媒体文件
        media_files = MediaFile.query.filter_by(article_id=article_id).order_by(MediaFile.created_at.desc()).all()

        # 按文件类型分组
        audio_files = []
        subtitle_files = []
        image_files = []

        for file in media_files:
            file_dict = file.to_dict()
            if file.file_type == 'audio':
                audio_files.append(file_dict)
            elif file.file_type == 'subtitle':
                subtitle_files.append(file_dict)
            elif file.file_type == 'image':
                image_files.append(file_dict)

        # 如果没有字幕文件记录，尝试从audio_sync_data中提取
        if not subtitle_files and article.audio_sync_data:
            try:
                import json
                sync_data = json.loads(article.audio_sync_data)
                subtitle_url = sync_data.get('subtitle_url')
                if subtitle_url and article.original_id:
                    # 构建虚拟字幕文件记录
                    subtitle_files.append({
                        'id': f'virtual_{article.original_id}_subtitle',
                        'filename': f'{article.original_id}_subtitle.srt',
                        'original_filename': f'{article.original_id}_subtitle.srt',
                        'file_type': 'subtitle',
                        'file_category': 'subtitle',
                        'url': subtitle_url,
                        'upload_status': 'uploaded',
                        'created_at': article.created_at.isoformat() if article.created_at else None
                    })
                    logger.info(f"从audio_sync_data中提取到字幕文件: {subtitle_url}")
            except (json.JSONDecodeError, Exception) as e:
                logger.warning(f"解析audio_sync_data失败: {str(e)}")

        # 如果没有图片文件记录，尝试从content_images中提取
        if not image_files and article.content_images:
            try:
                import json
                image_urls = json.loads(article.content_images)
                for i, image_url in enumerate(image_urls, 1):
                    if image_url and image_url.strip():
                        image_files.append({
                            'id': f'virtual_{article.original_id}_image_{i:02d}',
                            'filename': f'{article.original_id}_image_{i:02d}.jpg',
                            'original_filename': f'{article.original_id}_image_{i:02d}.jpg',
                            'file_type': 'image',
                            'file_category': 'content',
                            'url': image_url,
                            'upload_status': 'uploaded',
                            'created_at': article.created_at.isoformat() if article.created_at else None
                        })
                logger.info(f"从content_images中提取到{len(image_files)}个图片文件")
            except (json.JSONDecodeError, Exception) as e:
                logger.warning(f"解析content_images失败: {str(e)}")

        # 如果没有音频文件记录，尝试从original_audio_url中提取
        if not audio_files and article.original_audio_url and article.original_id:
            audio_files.append({
                'id': f'virtual_{article.original_id}_audio',
                'filename': f'{article.original_id}_audio.mp3',
                'original_filename': f'{article.original_id}_audio.mp3',
                'file_type': 'audio',
                'file_category': 'original_audio',
                'url': article.original_audio_url,
                'upload_status': 'uploaded',
                'created_at': article.created_at.isoformat() if article.created_at else None
            })
            logger.info(f"从original_audio_url中提取到音频文件: {article.original_audio_url}")

        # 如果没有封面文件记录，尝试从cover_url中提取
        cover_files = []
        cover_media_files = [f for f in media_files if f.file_category == 'cover']
        if not cover_media_files and article.cover_url and article.original_id:
            cover_files.append({
                'id': f'virtual_{article.original_id}_cover',
                'filename': f'{article.original_id}_cover.jpg',
                'original_filename': f'{article.original_id}_cover.jpg',
                'file_type': 'image',
                'file_category': 'cover',
                'url': article.cover_url,
                'upload_status': 'uploaded',
                'created_at': article.created_at.isoformat() if article.created_at else None
            })
            logger.info(f"从cover_url中提取到封面文件: {article.cover_url}")
        else:
            # 如果有MediaFile记录的封面，使用它们
            for file in cover_media_files:
                cover_files.append(file.to_dict())

        return jsonify({
            'success': True,
            'data': {
                'audio': audio_files,
                'subtitle': subtitle_files,
                'image': image_files,
                'cover': cover_files
            },
            'article': {
                'id': article.id,
                'title': article.title,
                'original_id': article.original_id
            }
        })
    except Exception as e:
        logger.error(f"获取文章媒体文件失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/articles/<int:article_id>/media/upload', methods=['POST'])
@login_required
@staff_required
def api_upload_article_media(article_id):
    """为文章上传媒体文件API"""
    try:
        from models import MediaFile
        article = Article.query.get_or_404(article_id)

        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        # 这里应该调用R2上传功能
        # 暂时创建媒体文件记录
        media_file = MediaFile(
            article_id=article_id,
            original_id=article.original_id,
            filename=file.filename,
            original_filename=file.filename,
            file_type='image',  # 需要根据文件扩展名判断
            url='https://example.com/placeholder.jpg',  # 临时URL
            r2_key='placeholder'
        )

        db.session.add(media_file)
        db.session.commit()

        return jsonify({'success': True, 'message': '文件上传成功', 'data': media_file.to_dict()})
    except Exception as e:
        logger.error(f"上传文章媒体文件失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/media/<int:file_id>', methods=['DELETE'])
@login_required
@staff_required
def api_delete_media_file(file_id):
    """删除媒体文件API"""
    try:
        from models import MediaFile
        file = MediaFile.query.get_or_404(file_id)

        # 这里应该同时删除R2上的文件
        db.session.delete(file)
        db.session.commit()

        return jsonify({'success': True, 'message': '文件删除成功'})
    except Exception as e:
        logger.error(f"删除媒体文件失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500


# --- 音频同步功能管理 ---
@admin_bp.route('/audio-sync-test')
@login_required
@admin_required
def audio_sync_test():
    """音频同步测试页面"""
    # 查找测试文章
    test_article = Article.query.filter_by(title="【测试】惊天预兆！即将发生的事！").first()

    return render_template('audio_sync_test.html', article=test_article)

@admin_bp.route('/api/media/match-by-original-id', methods=['POST'])
@login_required
@admin_required
def api_match_media_by_original_id():
    """根据原始ID匹配媒体文件到文章API"""
    try:
        from models import MediaFile
        data = request.get_json()
        original_id = data.get('original_id')

        if not original_id:
            return jsonify({'success': False, 'error': '缺少原始ID'}), 400

        # 查找具有该原始ID的文章
        article = Article.query.filter_by(original_id=original_id).first()
        if not article:
            return jsonify({'success': False, 'error': f'未找到原始ID为 {original_id} 的文章'}), 404

        # 查找未关联的媒体文件（通过文件名中的OID匹配）
        unmatched_files = MediaFile.query.filter(
            MediaFile.article_id.is_(None),
            MediaFile.original_filename.like(f'%OID{original_id}%')
        ).all()

        matched_count = 0
        for file in unmatched_files:
            file.article_id = article.id
            file.original_id = original_id
            matched_count += 1

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'成功匹配 {matched_count} 个媒体文件到文章 "{article.title}"',
            'matched_count': matched_count
        })
    except Exception as e:
        logger.error(f"匹配媒体文件失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/tickets/export')
@login_required
@staff_required
def export_tickets():
    """导出工单API"""
    try:
        from models import SupportTicket, User
        from datetime import datetime
        from flask import make_response
        import csv
        import io

        # 获取参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        status = request.args.get('status')
        format_type = request.args.get('format', 'txt')

        if not start_date or not end_date:
            return jsonify({'success': False, 'error': '请提供开始和结束日期'}), 400

        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
            # 结束日期包含当天的所有时间
            end_dt = end_dt.replace(hour=23, minute=59, second=59)
        except ValueError:
            return jsonify({'success': False, 'error': '日期格式错误，请使用YYYY-MM-DD格式'}), 400

        # 构建查询 - 明确指定join条件
        query = SupportTicket.query.join(User, SupportTicket.user_id == User.id).filter(
            SupportTicket.created_at >= start_dt,
            SupportTicket.created_at <= end_dt
        )

        if status:
            query = query.filter(SupportTicket.status == status)

        tickets = query.order_by(SupportTicket.created_at.asc()).all()

        if format_type == 'txt':
            # TXT格式 - 适合AI处理
            output = io.StringIO()

            # 写入AI指令模板
            ai_template = """请根据以下工单信息生成专业的客服回复，严格按照以下要求：

【格式要求】
1. 每行一个回复，格式为：工单ID|回复内容
2. 工单ID必须与下方工单信息中的ID完全一致
3. 回复内容要专业、友好、有针对性
4. 不要包含标题行、说明文字或其他格式
5. 直接输出可导入系统的格式

【网站介绍】
本站提供基础翻译和高级翻译服务。基础翻译包含常规文章内容，所有用户都可以阅读。高级翻译包含更详细的内容、原文对照、音频朗读等功能，需要VIP权限才能访问。VIP用户每月获得一定的阅读配额。

【回复原则】
- 充值/支付问题：请等待客服上线处理，我们会尽快为您解决充值相关问题

- VIP会员问题：VIP权限激活需要1-3分钟，建议重新登录或清除浏览器缓存，如仍有问题请等待客服处理

- 配额问题：基础翻译免费阅读，高级翻译需要消耗VIP配额。VIP用户每月有固定配额，可在个人中心查看剩余额度

- 文章阅读问题：先记录问题，如果着急可以查看相关视频或原文（高级内容中有原文对照）

- 技术故障：我们已收集您的反馈并转交技术团队，正在积极解决中，请耐心等待

- 其他问题：请等待客服上线为您详细解答，或查看网站帮助文档

【示例格式】
123|您好，我们已经查看了您的账户，余额显示异常是由于系统同步延迟造成的。您的充值已经到账，请刷新页面查看。
456|您好，感谢您购买VIP会员。请先退出账户重新登录，然后清除浏览器缓存。VIP权限需要几分钟时间同步。

【工单信息】
格式：工单ID|用户名|主题|内容|状态|创建时间

"""
            output.write(ai_template)

            # 写入工单数据
            for ticket in tickets:
                # 格式：工单ID|用户名|主题|内容|状态|创建时间
                content = ticket.content.replace('\n', ' ').replace('\r', ' ')
                subject = ticket.subject.replace('\n', ' ').replace('\r', ' ')
                line = f"{ticket.id}|{ticket.user.username}|{subject}|{content}|{ticket.status}|{ticket.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
                output.write(line)

            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/plain; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename=tickets_{start_date}_to_{end_date}.txt'

        else:  # CSV格式
            output = io.StringIO()
            writer = csv.writer(output)

            # 写入标题行
            writer.writerow(['工单ID', '用户名', '主题', '内容', '分类', '状态', '优先级', '创建时间', '最后更新'])

            # 写入数据行
            for ticket in tickets:
                writer.writerow([
                    ticket.id,
                    ticket.user.username,
                    ticket.subject,
                    ticket.content,
                    ticket.category or '',
                    ticket.status,
                    ticket.priority,
                    ticket.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    ticket.updated_at.strftime('%Y-%m-%d %H:%M:%S')
                ])

            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename=tickets_{start_date}_to_{end_date}.csv'

        return response

    except Exception as e:
        logger.error(f"导出工单失败: {str(e)}")
        return jsonify({'success': False, 'error': f'导出失败: {str(e)}'}), 500

@admin_bp.route('/api/tickets/import-replies', methods=['POST'])
@login_required
@staff_required
def import_ticket_replies():
    """导入工单回复API"""
    try:
        from models import SupportTicket, SupportMessage
        from datetime import datetime

        # 检查文件
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400

        if not file.filename.endswith('.txt'):
            return jsonify({'success': False, 'error': '只支持TXT格式文件'}), 400

        auto_close = request.form.get('auto_close') == 'true'

        # 读取文件内容
        try:
            content = file.read().decode('utf-8')
        except UnicodeDecodeError:
            try:
                content = file.read().decode('gbk')
            except UnicodeDecodeError:
                return jsonify({'success': False, 'error': '文件编码不支持，请使用UTF-8或GBK编码'}), 400

        lines = content.strip().split('\n')
        processed = 0
        success_count = 0
        error_count = 0
        errors = []

        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue

            # 跳过AI指令模板和说明文字
            if (line.startswith('请根据以下工单信息') or
                line.startswith('【') or
                line.startswith('-') or
                line.startswith('格式：') or
                line.startswith('1.') or
                line.startswith('2.') or
                line.startswith('3.') or
                line.startswith('4.') or
                line.startswith('5.') or
                '格式要求' in line or
                '回复原则' in line or
                '示例格式' in line or
                '工单信息' in line or
                not line or
                line.isspace()):
                continue

            # 跳过原始工单数据行（包含多个|分隔符且格式为：ID|用户名|主题|内容|状态|时间）
            parts_check = line.split('|')
            if len(parts_check) >= 6:  # 原始工单数据有6个字段
                # 检查最后一个字段是否像时间格式
                last_field = parts_check[-1].strip()
                if ('-' in last_field and ':' in last_field and len(last_field) >= 10):
                    continue  # 跳过原始工单数据行

            processed += 1

            # 解析格式：工单ID|回复内容
            parts = line.split('|', 1)
            if len(parts) != 2:
                # 检查是否是纯数字开头的有效回复行
                if not (parts[0].strip().isdigit() if parts else False):
                    # 不是有效的回复行，跳过但不计入错误
                    processed -= 1
                    continue
                error_count += 1
                errors.append(f"第{line_num}行格式错误：{line}")
                continue

            try:
                ticket_id = int(parts[0])
                reply_content = parts[1].strip()

                if not reply_content:
                    error_count += 1
                    errors.append(f"第{line_num}行回复内容为空")
                    continue

                # 查找工单
                ticket = SupportTicket.query.get(ticket_id)
                if not ticket:
                    error_count += 1
                    errors.append(f"第{line_num}行工单ID {ticket_id} 不存在")
                    continue

                # 创建回复消息
                message = SupportMessage(
                    ticket_id=ticket.id,
                    message=reply_content,
                    is_staff_reply=True,
                    staff_id=current_user.id,
                    created_at=datetime.utcnow()
                )
                db.session.add(message)

                # 更新工单状态
                ticket.admin_reply = reply_content
                ticket.admin_id = current_user.id
                ticket.updated_at = datetime.utcnow()

                if auto_close:
                    ticket.status = 'closed'
                    ticket.resolved_at = datetime.utcnow()
                else:
                    ticket.status = 'processing'

                success_count += 1

            except ValueError:
                error_count += 1
                errors.append(f"第{line_num}行工单ID格式错误：{parts[0]}")
                continue
            except Exception as e:
                error_count += 1
                errors.append(f"第{line_num}行处理失败：{str(e)}")
                continue

        # 提交数据库更改
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'error': f'数据库保存失败: {str(e)}'}), 500

        result = {
            'success': True,
            'processed': processed,
            'success_count': success_count,
            'error_count': error_count
        }

        if errors:
            result['errors'] = errors[:10]  # 只返回前10个错误

        return jsonify(result)

    except Exception as e:
        logger.error(f"导入工单回复失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': f'导入失败: {str(e)}'}), 500

# ==================== 媒体文件管理 API ====================

@admin_bp.route('/api/articles/<int:article_id>/media', methods=['GET'])
@login_required
def get_article_media(article_id):
    """获取文章的媒体文件列表"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': '权限不足'}), 403

    try:
        # 验证文章存在
        article = Article.query.get_or_404(article_id)

        # 获取关联的媒体文件
        media_files = MediaFile.query.filter_by(article_id=article_id).all()

        # 按类型分组
        audio_files = []
        image_files = []

        for media in media_files:
            file_data = {
                'id': media.id,
                'filename': media.filename,
                'original_filename': media.original_filename,
                'file_type': media.file_type,
                'file_category': media.file_category,
                'file_size': media.file_size,
                'url': media.url,
                'created_at': media.created_at.isoformat() if media.created_at else None
            }

            if media.file_type == 'audio':
                audio_files.append(file_data)
            elif media.file_type == 'image':
                image_files.append(file_data)

        return jsonify({
            'success': True,
            'audio_files': audio_files,
            'image_files': image_files
        })

    except Exception as e:
        logger.error(f"获取文章媒体文件失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取失败: {str(e)}'}), 500

@admin_bp.route('/api/articles/<int:article_id>/media', methods=['POST'])
@login_required
def upload_article_media(article_id):
    """上传文章媒体文件"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': '权限不足'}), 403

    try:
        # 验证文章存在
        article = Article.query.get_or_404(article_id)

        # 检查文件
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400

        file_type = request.form.get('file_type', 'image')
        file_category = request.form.get('file_category', 'content')

        # 验证文件类型
        if file_type == 'audio':
            allowed_extensions = {'.mp3', '.wav', '.m4a', '.aac', '.ogg'}
        elif file_type == 'image':
            allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
        else:
            return jsonify({'success': False, 'error': '不支持的文件类型'}), 400

        file_ext = os.path.splitext(file.filename)[1].lower()
        if file_ext not in allowed_extensions:
            return jsonify({'success': False, 'error': f'不支持的文件格式: {file_ext}'}), 400

        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4()}{file_ext}"

        # 这里应该上传到R2存储，暂时返回模拟数据
        # TODO: 实现实际的R2上传逻辑
        file_url = f"https://your-r2-domain.com/{unique_filename}"

        # 创建媒体文件记录
        media_file = MediaFile(
            article_id=article_id,
            filename=unique_filename,
            original_filename=file.filename,
            file_type=file_type,
            file_category=file_category,
            file_size=len(file.read()),
            url=file_url,
            r2_key=unique_filename,
            created_at=datetime.utcnow()
        )

        db.session.add(media_file)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文件上传成功',
            'file': {
                'id': media_file.id,
                'filename': media_file.filename,
                'original_filename': media_file.original_filename,
                'file_type': media_file.file_type,
                'file_size': media_file.file_size,
                'url': media_file.url
            }
        })

    except Exception as e:
        logger.error(f"上传媒体文件失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500

@admin_bp.route('/api/media/<int:media_id>', methods=['DELETE'])
@login_required
def delete_media_file(media_id):
    """删除媒体文件"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': '权限不足'}), 403

    try:
        media_file = MediaFile.query.get_or_404(media_id)

        # TODO: 从R2存储中删除实际文件
        # delete_from_r2(media_file.r2_key)

        db.session.delete(media_file)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文件删除成功'
        })

    except Exception as e:
        logger.error(f"删除媒体文件失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': f'删除失败: {str(e)}'}), 500

@admin_bp.route('/api/articles/<int:article_id>/audio-sync', methods=['PUT'])
@login_required
def save_audio_sync_config(article_id):
    """保存音频同步配置"""
    if not current_user.is_admin():
        return jsonify({'success': False, 'error': '权限不足'}), 403

    try:
        article = Article.query.get_or_404(article_id)
        data = request.get_json()

        audio_sync_data = data.get('audio_sync_data')
        has_audio_sync = data.get('has_audio_sync', False)

        # 验证JSON格式
        if audio_sync_data:
            try:
                import json
                json.loads(audio_sync_data)
            except json.JSONDecodeError:
                return jsonify({'success': False, 'error': '音频同步数据格式错误'}), 400

        # 更新文章
        article.audio_sync_data = audio_sync_data
        article.has_audio_sync = has_audio_sync
        article.updated_at = datetime.utcnow()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '音频同步配置保存成功'
        })

    except Exception as e:
        logger.error(f"保存音频同步配置失败: {str(e)}")
        db.session.rollback()
        return jsonify({'success': False, 'error': f'保存失败: {str(e)}'}), 500


@admin_bp.route('/api/proxy-subtitle', methods=['GET'])
def proxy_subtitle():
    """代理获取字幕文件，解决跨域问题"""
    try:
        import requests
        from flask import Response

        # 获取要代理的字幕URL
        subtitle_url = request.args.get('url')
        if not subtitle_url:
            return jsonify({'error': '缺少url参数'}), 400

        logger.info(f"代理获取字幕文件: {subtitle_url}")

        # 发起请求获取字幕文件
        try:
            response = requests.get(subtitle_url, timeout=10)

            # 检查请求是否成功
            if response.status_code != 200:
                logger.warning(f"无法获取字幕文件，状态码: {response.status_code}, URL: {subtitle_url}")
                return jsonify({'error': f'字幕文件不存在 (状态码: {response.status_code})'}), response.status_code

            # 获取字幕内容
            subtitle_content = response.text
            logger.info(f"成功获取字幕文件，大小: {len(subtitle_content)} 字符")

            # 创建响应并设置正确的内容类型
            proxy_response = Response(subtitle_content, content_type='text/plain; charset=utf-8')

            return proxy_response

        except requests.Timeout:
            logger.warning(f"获取字幕文件超时: {subtitle_url}")
            return jsonify({'error': '字幕文件获取超时'}), 408

        except requests.RequestException as e:
            logger.error(f"获取字幕文件失败: {str(e)}")
            return jsonify({'error': f'字幕文件获取失败: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"代理字幕文件失败: {str(e)}")
        return jsonify({'error': f'服务器错误: {str(e)}'}), 500




# ==================== Vue3 管理后台认证API ====================

@admin_bp.route('/api/auth/login', methods=['POST'])
def api_auth_login():
    """Vue3管理后台登录API"""
    try:
        from flask_login import login_user
        from werkzeug.security import check_password_hash

        data = request.get_json()
        if not data or not data.get('username') or not data.get('password'):
            return jsonify({'success': False, 'error': '用户名和密码不能为空'}), 400

        username = data['username']
        password = data['password']

        # 查找用户
        user = User.query.filter_by(username=username).first()
        if not user:
            return jsonify({'success': False, 'error': '用户名或密码错误'}), 401

        # 验证密码
        if not check_password_hash(user.password_hash, password):
            return jsonify({'success': False, 'error': '用户名或密码错误'}), 401

        # 检查用户权限
        if not user.can_access_admin():
            return jsonify({'success': False, 'error': '权限不足，无法访问管理后台'}), 403

        # 登录用户
        login_user(user, remember=True)

        # 更新最后登录时间
        user.last_login = datetime.now()
        db.session.commit()

        # 返回用户信息
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'is_admin': user.is_admin(),
            'is_staff': user.can_access_admin(),
            'role': user.role,
            'membership_type': user.membership_type,
            'created_at': user.created_at.isoformat() if user.created_at else None
        }

        return jsonify({
            'success': True,
            'data': {
                'token': 'flask-session',  # 使用Flask会话
                'user': user_data
            },
            'message': '登录成功'
        })

    except Exception as e:
        logger.error(f"Vue3登录失败: {str(e)}")
        return jsonify({'success': False, 'error': '登录失败，请稍后重试'}), 500

@admin_bp.route('/api/auth/logout', methods=['POST'])
@login_required
def api_auth_logout():
    """Vue3管理后台登出API"""
    try:
        from flask_login import logout_user
        logout_user()
        return jsonify({'success': True, 'message': '登出成功'})
    except Exception as e:
        logger.error(f"Vue3登出失败: {str(e)}")
        return jsonify({'success': False, 'error': '登出失败'}), 500

@admin_bp.route('/api/auth/me')
@login_required
@staff_required
def api_auth_me():
    """获取当前用户信息 - 用于Vue3应用"""
    try:
        user_data = {
            'id': current_user.id,
            'username': current_user.username,
            'email': current_user.email,
            'is_admin': current_user.is_admin(),  # 调用方法而不是属性
            'is_staff': current_user.can_access_admin(),  # 使用can_access_admin方法
            'role': current_user.role,
            'membership_type': current_user.membership_type,
            'created_at': current_user.created_at.isoformat() if current_user.created_at else None
        }

        return jsonify({
            'success': True,
            'data': user_data
        })
    except Exception as e:
        logger.error(f"获取当前用户信息失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取用户信息失败: {str(e)}'}), 500

# ==================== Vue3 管理后台 Dashboard API 路由 ====================

@admin_bp.route('/api/admin/dashboard/stats')
@login_required
@staff_required
def api_dashboard_stats():
    """获取仪表盘统计数据"""
    try:
        from datetime import datetime, timedelta

        # 用户统计
        total_users = User.query.count()
        new_users_today = User.query.filter(
            User.created_at >= datetime.now().date()
        ).count()

        # 文章统计
        total_articles = Article.query.count()
        published_articles = Article.query.filter_by(status='published').count()

        # 订单统计
        total_purchases = Purchase.query.count() if Purchase else 0

        # VIP用户统计
        vip_users = User.query.filter(User.membership_type != 'free').count()

        stats = {
            'user_count': total_users,
            'new_users_today': new_users_today,
            'article_count': total_articles,
            'published_articles': published_articles,
            'purchase_count': total_purchases,
            'vip_users': vip_users
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取仪表盘统计数据失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取统计数据失败: {str(e)}'}), 500

@admin_bp.route('/api/admin/dashboard/recent-users')
@login_required
@staff_required
def api_dashboard_recent_users():
    """获取最近注册用户"""
    try:
        limit = request.args.get('limit', 5, type=int)
        recent_users = User.query.order_by(User.created_at.desc()).limit(limit).all()

        users_data = []
        for user in recent_users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'is_active': getattr(user, 'is_active', True),
                'membership_type': getattr(user, 'membership_type', 'free')
            })

        return jsonify({
            'success': True,
            'data': users_data
        })
    except Exception as e:
        logger.error(f"获取最近用户失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取最近用户失败: {str(e)}'}), 500

@admin_bp.route('/api/admin/dashboard/recent-articles')
@login_required
@staff_required
def api_dashboard_recent_articles():
    """获取最近发布文章"""
    try:
        limit = request.args.get('limit', 5, type=int)
        recent_articles = Article.query.filter_by(status='published').order_by(
            Article.created_at.desc()
        ).limit(limit).all()

        articles_data = []
        for article in recent_articles:
            articles_data.append({
                'id': article.id,
                'title': article.title,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'author_name': article.author.username if article.author else '未知',
                'view_count': getattr(article, 'view_count', 0),
                'status': article.status
            })

        return jsonify({
            'success': True,
            'data': articles_data
        })
    except Exception as e:
        logger.error(f"获取最近文章失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取最近文章失败: {str(e)}'}), 500

@admin_bp.route('/api/admin/dashboard')
@login_required
@staff_required
def api_dashboard_all():
    """获取完整仪表盘数据"""
    try:
        from datetime import datetime, timedelta

        # 获取统计数据
        total_users = User.query.count()
        new_users_today = User.query.filter(
            User.created_at >= datetime.now().date()
        ).count()

        total_articles = Article.query.count()
        published_articles = Article.query.filter_by(status='published').count()
        total_purchases = Purchase.query.count() if Purchase else 0
        vip_users = User.query.filter(User.membership_type != 'free').count()

        # 获取最近用户
        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
        users_data = []
        for user in recent_users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'created_at': user.created_at.isoformat() if user.created_at else None,
                'is_active': getattr(user, 'is_active', True),
                'membership_type': getattr(user, 'membership_type', 'free')
            })

        # 获取最近文章
        recent_articles = Article.query.filter_by(status='published').order_by(
            Article.created_at.desc()
        ).limit(5).all()
        articles_data = []
        for article in recent_articles:
            articles_data.append({
                'id': article.id,
                'title': article.title,
                'created_at': article.created_at.isoformat() if article.created_at else None,
                'author_name': article.author.username if article.author else '未知',
                'view_count': getattr(article, 'view_count', 0),
                'status': article.status
            })

        return jsonify({
            'success': True,
            'data': {
                'stats': {
                    'user_count': total_users,
                    'new_users_today': new_users_today,
                    'article_count': total_articles,
                    'published_articles': published_articles,
                    'purchase_count': total_purchases,
                    'vip_users': vip_users
                },
                'recent_users': users_data,
                'recent_articles': articles_data
            }
        })
    except Exception as e:
        logger.error(f"获取完整仪表盘数据失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取仪表盘数据失败: {str(e)}'}), 500

# ==================== Vue3 管理后台财务API ====================

@admin_bp.route('/api/finance/stats')
@login_required
@staff_required
def api_finance_stats():
    """获取财务统计数据"""
    try:
        from datetime import datetime, timedelta

        # 计算总收入
        total_revenue = Purchase.query.with_entities(
            func.sum(Purchase.amount)
        ).scalar() or 0

        # 计算今日收入
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_revenue = Purchase.query.filter(
            Purchase.purchase_date >= today_start
        ).with_entities(func.sum(Purchase.amount)).scalar() or 0

        # VIP用户数
        vip_users = UserFinance.query.filter(
            UserFinance.vip_level > 0,
            UserFinance.vip_expire_at > datetime.now()
        ).count()

        # 今日订单数
        today_orders = Purchase.query.filter(
            Purchase.purchase_date >= today_start
        ).count()

        # 待处理异常数（模拟数据）
        pending_anomalies = 3

        stats = {
            'total_revenue': total_revenue,
            'today_revenue': today_revenue,
            'vip_users': vip_users,
            'today_orders': today_orders,
            'pending_anomalies': pending_anomalies
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取财务统计数据失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取财务统计失败: {str(e)}'}), 500

@admin_bp.route('/api/finance/orders')
@login_required
@staff_required
def api_finance_orders():
    """获取订单列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        search = request.args.get('search', '')

        query = Purchase.query.join(User)

        if search:
            query = query.filter(
                or_(
                    Purchase.id.like(f'%{search}%'),
                    User.username.like(f'%{search}%')
                )
            )

        pagination = query.order_by(Purchase.purchase_date.desc()).paginate(
            page=page, per_page=per_page, error_out=False
        )

        orders_data = []
        for purchase in pagination.items:
            orders_data.append({
                'id': purchase.id,
                'user_id': purchase.user_id,
                'username': purchase.user.username if purchase.user else '未知',
                'product_type': purchase.product_type or 'VIP',
                'amount': purchase.amount,
                'payment_method': purchase.payment_method,
                'status': 'success',  # Purchase表中的都是成功的
                'created_at': purchase.purchase_date.isoformat() if purchase.purchase_date else None
            })

        return jsonify({
            'success': True,
            'data': {
                'data': orders_data,
                'pagination': {
                    'page': pagination.page,
                    'pages': pagination.pages,
                    'per_page': pagination.per_page,
                    'total': pagination.total,
                    'has_prev': pagination.has_prev,
                    'has_next': pagination.has_next
                }
            }
        })
    except Exception as e:
        logger.error(f"获取订单列表失败: {str(e)}")
        return jsonify({'success': False, 'error': f'获取订单列表失败: {str(e)}'}), 500

# ==================== 系统设置API ====================

@admin_bp.route('/api/system/settings', methods=['GET'])
@login_required
@admin_required
def api_get_system_settings():
    """获取系统设置API"""
    try:
        # 从数据库或配置文件获取系统设置
        settings = {
            # 基础设置
            'site_name': app.config.get('SITE_NAME', '塔罗嗅嗅'),
            'site_description': app.config.get('SITE_DESCRIPTION', '专业的塔罗牌占卜平台'),
            'admin_email': app.config.get('ADMIN_EMAIL', '<EMAIL>'),
            'contact_phone': app.config.get('CONTACT_PHONE', '************'),

            # 用户设置
            'allow_registration': app.config.get('ALLOW_REGISTRATION', True),
            'require_email_verification': app.config.get('REQUIRE_EMAIL_VERIFICATION', True),
            'default_user_quota': app.config.get('DEFAULT_USER_QUOTA', 100),
            'max_login_attempts': app.config.get('MAX_LOGIN_ATTEMPTS', 5),

            # 内容设置
            'articles_per_page': app.config.get('ARTICLES_PER_PAGE', 20),
            'auto_approve_articles': app.config.get('AUTO_APPROVE_ARTICLES', False),
            'max_upload_size': app.config.get('MAX_UPLOAD_SIZE', 10),
            'allowed_file_types': app.config.get('ALLOWED_FILE_TYPES', 'jpg,png,gif,pdf,doc,docx'),

            # 安全设置
            'enable_csrf_protection': app.config.get('WTF_CSRF_ENABLED', True),
            'session_timeout': app.config.get('PERMANENT_SESSION_LIFETIME', 30),
            'enable_rate_limiting': app.config.get('RATELIMIT_ENABLED', True),
            'backup_frequency': app.config.get('BACKUP_FREQUENCY', 'daily'),

            # 邮件设置
            'smtp_server': app.config.get('MAIL_SERVER', 'smtp.gmail.com'),
            'smtp_port': app.config.get('MAIL_PORT', 587),
            'smtp_username': app.config.get('MAIL_USERNAME', ''),
            'smtp_use_tls': app.config.get('MAIL_USE_TLS', True)
        }

        return jsonify({
            'success': True,
            'data': settings
        })
    except Exception as e:
        logger.error(f"获取系统设置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/system/settings', methods=['PUT'])
@login_required
@admin_required
def api_update_system_settings():
    """更新系统设置API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 这里应该将设置保存到数据库或配置文件
        # 为了演示，我们只是记录日志
        logger.info(f"管理员 {current_user.username} 更新了系统设置")

        # 可以在这里添加设置验证逻辑
        # 例如验证邮箱格式、端口号范围等

        return jsonify({
            'success': True,
            'message': '系统设置更新成功'
        })
    except Exception as e:
        logger.error(f"更新系统设置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== 价格设置API ====================

@admin_bp.route('/api/system/prices', methods=['GET'])
@login_required
@admin_required
def api_get_price_settings():
    """获取价格设置API"""
    try:
        # 从数据库或配置获取价格设置
        prices = {
            # 内容价格
            'basic_content_price': 9.90,
            'premium_content_price': 19.90,
            'vip_content_price': 39.90,
            'consultation_price': 99.00,

            # 会员价格
            'monthly_vip_price': 29.90,
            'quarterly_vip_price': 79.90,
            'yearly_vip_price': 299.90,

            # 积分价格
            'points_package_small': 9.90,
            'points_small_amount': 100,
            'points_package_large': 49.90,
            'points_large_amount': 600,

            # 折扣设置
            'vip_discount': 0.85,
            'bulk_discount': 0.90
        }

        return jsonify({
            'success': True,
            'data': prices
        })
    except Exception as e:
        logger.error(f"获取价格设置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/system/prices', methods=['PUT'])
@login_required
@admin_required
def api_update_price_settings():
    """更新价格设置API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 验证价格数据
        for key, value in data.items():
            if 'price' in key or 'discount' in key:
                try:
                    float(value)
                except (ValueError, TypeError):
                    return jsonify({'success': False, 'error': f'无效的价格值: {key}'}), 400

        logger.info(f"管理员 {current_user.username} 更新了价格设置")

        return jsonify({
            'success': True,
            'message': '价格设置更新成功'
        })
    except Exception as e:
        logger.error(f"更新价格设置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== 上传配置API ====================

@admin_bp.route('/api/uploader/config', methods=['GET'])
@login_required
@admin_required
def api_get_uploader_config():
    """获取上传配置API"""
    try:
        config = {
            'website_url': app.config.get('UPLOADER_WEBSITE_URL', 'http://0.0.0.0:5000'),
            'username': app.config.get('UPLOADER_USERNAME', 'admin'),
            'password': '',  # 出于安全考虑，不返回密码
            'upload_path': app.config.get('UPLOADER_PATH', '/uploads'),
            'batch_size': app.config.get('UPLOADER_BATCH_SIZE', 10),
            'max_retries': app.config.get('UPLOADER_MAX_RETRIES', 3),
            'timeout': app.config.get('UPLOADER_TIMEOUT', 30),
            'concurrent_uploads': app.config.get('UPLOADER_CONCURRENT', 3),
            'auto_create_tags': app.config.get('UPLOADER_AUTO_TAGS', True),
            'auto_publish': app.config.get('UPLOADER_AUTO_PUBLISH', False),
            'backup_files': app.config.get('UPLOADER_BACKUP', True),
            'delete_after_upload': app.config.get('UPLOADER_DELETE_AFTER', False)
        }

        return jsonify({
            'success': True,
            'data': config
        })
    except Exception as e:
        logger.error(f"获取上传配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/uploader/config', methods=['PUT'])
@login_required
@admin_required
def api_update_uploader_config():
    """更新上传配置API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': '无效的请求数据'}), 400

        # 验证配置数据
        if 'website_url' in data and not data['website_url'].startswith(('http://', 'https://')):
            return jsonify({'success': False, 'error': '无效的网站地址'}), 400

        logger.info(f"管理员 {current_user.username} 更新了上传配置")

        return jsonify({
            'success': True,
            'message': '上传配置更新成功'
        })
    except Exception as e:
        logger.error(f"更新上传配置失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/uploader/test-connection', methods=['POST'])
@login_required
@admin_required
def api_test_uploader_connection():
    """测试上传连接API"""
    try:
        data = request.get_json()
        website_url = data.get('website_url', '')
        username = data.get('username', '')
        password = data.get('password', '')

        # 这里应该实现实际的连接测试逻辑
        # 例如发送HTTP请求到目标服务器

        # 模拟连接测试
        import time
        time.sleep(1)  # 模拟网络延迟

        return jsonify({
            'success': True,
            'message': '连接测试成功'
        })
    except Exception as e:
        logger.error(f"连接测试失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/uploader/upload', methods=['POST'])
@login_required
@admin_required
def api_manual_upload():
    """手动上传文件API"""
    try:
        if 'files' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        files = request.files.getlist('files')
        if not files or files[0].filename == '':
            return jsonify({'success': False, 'error': '没有选择文件'}), 400

        upload_results = []
        for file in files:
            if file and file.filename:
                # 这里应该实现实际的文件上传逻辑
                # 例如保存到本地或云存储

                upload_results.append({
                    'filename': file.filename,
                    'status': 'success',
                    'message': '上传成功'
                })

        logger.info(f"管理员 {current_user.username} 手动上传了 {len(files)} 个文件")

        return jsonify({
            'success': True,
            'data': upload_results,
            'message': f'成功上传 {len(upload_results)} 个文件'
        })
    except Exception as e:
        logger.error(f"文件上传失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== 上传日志API ====================

@admin_bp.route('/api/uploader/logs', methods=['GET'])
@login_required
@admin_required
def api_get_upload_logs():
    """获取上传日志API"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        status = request.args.get('status', '')

        # 模拟上传日志数据
        logs = [
            {
                'id': 1,
                'filename': 'tarot_reading_guide.pdf',
                'file_size': 2048576,
                'status': 'success',
                'upload_time': '2025-08-03 14:30:25',
                'duration': 2.5,
                'error_message': None
            },
            {
                'id': 2,
                'filename': 'card_meanings.docx',
                'file_size': 1024000,
                'status': 'failed',
                'upload_time': '2025-08-03 14:25:10',
                'duration': None,
                'error_message': '文件格式不支持'
            }
        ]

        # 统计数据
        stats = {
            'success_count': 156,
            'failed_count': 12,
            'total_size': 2048576000,
            'today_count': 23
        }

        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'stats': stats,
                'pagination': {
                    'page': page,
                    'pages': 1,
                    'per_page': per_page,
                    'total': len(logs)
                }
            }
        })
    except Exception as e:
        logger.error(f"获取上传日志失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/uploader/logs/stats', methods=['GET'])
@login_required
@admin_required
def api_get_upload_logs_stats():
    """获取上传日志统计信息API"""
    try:
        from datetime import datetime, timedelta

        # 模拟上传日志统计数据（实际应该从上传日志表获取）
        total_uploads = 1234
        successful_uploads = 1156
        failed_uploads = 78

        # 时间统计
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)

        today_uploads = 23
        week_uploads = 156

        # 文件大小统计
        total_file_size = 2048576000  # 2GB
        avg_file_size = total_file_size // total_uploads if total_uploads > 0 else 0

        # 文件类型分布
        file_type_distribution = {
            'pdf': 456,
            'docx': 234,
            'txt': 123,
            'md': 89
        }

        # 上传趋势（最近7天）
        upload_trend = []
        for i in range(7):
            date = today - timedelta(days=i)
            # 模拟数据
            count = 20 + (hash(str(date)) % 10)
            success = count - (hash(str(date)) % 3)
            failed = count - success

            upload_trend.append({
                'date': date.strftime('%Y-%m-%d'),
                'count': count,
                'success': success,
                'failed': failed
            })
        upload_trend.reverse()

        stats = {
            'total_uploads': total_uploads,
            'successful_uploads': successful_uploads,
            'failed_uploads': failed_uploads,
            'today_uploads': today_uploads,
            'this_week_uploads': week_uploads,
            'total_file_size': total_file_size,
            'avg_file_size': avg_file_size,
            'file_type_distribution': file_type_distribution,
            'upload_trend': upload_trend
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取上传日志统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/uploader/logs', methods=['DELETE'])
@login_required
@admin_required
def api_clear_upload_logs():
    """清空上传日志API"""
    try:
        # 这里应该实现清空日志的逻辑
        logger.info(f"管理员 {current_user.username} 清空了上传日志")

        return jsonify({
            'success': True,
            'message': '上传日志已清空'
        })
    except Exception as e:
        logger.error(f"清空上传日志失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/uploader/logs/<int:log_id>', methods=['DELETE'])
@login_required
@admin_required
def api_delete_upload_log(log_id):
    """删除单个上传日志API"""
    try:
        # 这里应该实现删除单个日志的逻辑
        logger.info(f"管理员 {current_user.username} 删除了上传日志 {log_id}")

        return jsonify({
            'success': True,
            'message': '上传日志已删除'
        })
    except Exception as e:
        logger.error(f"删除上传日志失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

# ==================== 数据统计API ====================

@admin_bp.route('/api/statistics/overview', methods=['GET'])
@login_required
@admin_required
def api_get_statistics_overview():
    """获取统计概览API"""
    try:
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        period = request.args.get('period', 'month')

        # 从数据库获取实际统计数据
        from models import User, Article, Order

        # 用户统计
        total_users = User.query.count()
        active_users = User.query.filter(User.last_login.isnot(None)).count()
        vip_users = User.query.filter(User.is_vip == True).count()

        # 文章统计
        total_articles = Article.query.count()
        published_articles = Article.query.filter(Article.status == 'published').count()
        draft_articles = Article.query.filter(Article.status == 'draft').count()

        # 今日新增统计
        from datetime import datetime, timedelta
        today = datetime.now().date()
        new_users_today = User.query.filter(User.created_at >= today).count()
        new_articles_today = Article.query.filter(Article.created_at >= today).count()

        # 收入统计（如果有订单表）
        total_revenue = 234567  # 这里应该从订单表计算
        today_revenue = 1234
        month_revenue = 45678

        stats = {
            # 用户统计
            'total_users': total_users,
            'active_users': active_users,
            'vip_users': vip_users,
            'new_users_today': new_users_today,
            'new_users_week': 312,  # 这里应该计算本周新增
            'user_retention': 78.5,

            # 内容统计
            'total_articles': total_articles,
            'published_articles': published_articles,
            'draft_articles': draft_articles,
            'new_articles_today': new_articles_today,
            'total_views': 456789,  # 这里应该从阅读记录计算

            # 财务统计
            'total_revenue': total_revenue,
            'vip_revenue': 156789,
            'content_revenue': 77778,
            'today_revenue': today_revenue,
            'month_revenue': month_revenue,
            'avg_order_value': 89.5,

            # 系统统计
            'total_visits': 789456,
            'unique_visitors': 234567,
            'page_views': 1234567,
            'avg_session_duration': 12.5,
            'bounce_rate': 35.2,
            'avg_response_time': 245
        }

        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取统计概览失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/statistics/users', methods=['GET'])
@login_required
@admin_required
def api_get_user_statistics():
    """获取用户统计API"""
    try:
        # 这里应该实现详细的用户统计逻辑
        user_stats = {
            'registration_trend': [],  # 注册趋势数据
            'activity_distribution': [],  # 活跃度分布
            'retention_analysis': []  # 留存分析
        }

        return jsonify({
            'success': True,
            'data': user_stats
        })
    except Exception as e:
        logger.error(f"获取用户统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/statistics/content', methods=['GET'])
@login_required
@admin_required
def api_get_content_statistics():
    """获取内容统计API"""
    try:
        # 这里应该实现详细的内容统计逻辑
        content_stats = {
            'publish_trend': [],  # 发布趋势
            'category_distribution': [],  # 分类分布
            'popular_articles': []  # 热门文章
        }

        return jsonify({
            'success': True,
            'data': content_stats
        })
    except Exception as e:
        logger.error(f"获取内容统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@admin_bp.route('/api/statistics/revenue', methods=['GET'])
@login_required
@admin_required
def api_get_revenue_statistics():
    """获取收入统计API"""
    try:
        # 这里应该实现详细的收入统计逻辑
        revenue_stats = {
            'revenue_trend': [],  # 收入趋势
            'payment_methods': [],  # 支付方式分布
            'product_revenue': []  # 产品收入分析
        }

        return jsonify({
            'success': True,
            'data': revenue_stats
        })
    except Exception as e:
        logger.error(f"获取收入统计失败: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500
