import { http } from '@/utils/request'
import type { ApiResponse, Article, PaginatedResponse, PaginationParams } from '@/types'

export interface ArticleListParams extends PaginationParams {
  search?: string
  author_id?: number
  tag_id?: number
  is_published?: boolean
  is_premium?: boolean
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

export interface ArticleCreateParams {
  title: string
  content: string
  summary?: string
  author_id: number
  tag_ids?: number[]
  is_published?: boolean
  is_premium?: boolean
  price?: number
  cover_image?: string
}

export interface ArticleUpdateParams extends Partial<ArticleCreateParams> {
  id: number
}

export interface ArticleStats {
  total_articles: number
  published_articles: number
  premium_articles: number
  total_views: number
  total_likes: number
  today_articles: number
}

export const articlesApi = {
  // 获取文章列表
  getList(params: ArticleListParams): Promise<ApiResponse<PaginatedResponse<Article>>> {
    return http.get('/articles', { params })
  },

  // 获取文章详情
  getDetail(id: number): Promise<ApiResponse<Article>> {
    return http.get(`/articles/${id}`)
  },

  // 创建文章
  create(data: ArticleCreateParams): Promise<ApiResponse<Article>> {
    return http.post('/articles', data)
  },

  // 更新文章
  update(id: number, data: Partial<ArticleCreateParams>): Promise<ApiResponse<Article>> {
    return http.put(`/articles/${id}`, data)
  },

  // 删除文章
  delete(id: number): Promise<ApiResponse> {
    return http.delete(`/articles/${id}`)
  },

  // 批量删除文章
  batchDelete(ids: number[]): Promise<ApiResponse> {
    return http.post('/articles/batch_delete', { ids })
  },

  // 更新文章状态
  updateStatus(id: number, status: string): Promise<ApiResponse> {
    return http.put(`/articles/${id}/status`, { status })
  },

  // 发布/取消发布文章
  togglePublish(id: number, is_published: boolean): Promise<ApiResponse> {
    return http.put(`/articles/${id}/publish`, { is_published })
  },

  // 设置/取消精品文章
  togglePremium(id: number, is_premium: boolean): Promise<ApiResponse> {
    return http.put(`/articles/${id}/premium`, { is_premium })
  },

  // 获取文章统计
  getStats(): Promise<ApiResponse<ArticleStats>> {
    return http.get('/articles/stats')
  },

  // 上传文章封面
  uploadCover(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)
    return http.post('/articles/upload-cover', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 获取文章标签建议
  getTagSuggestions(query: string): Promise<ApiResponse<string[]>> {
    return http.get('/articles/tag-suggestions', { params: { q: query } })
  },

  // 获取文章购买者列表
  getBuyers(articleId: number): Promise<ApiResponse<{
    article: { id: number; title: string; status: string; created_at: string }
    buyers: Array<{
      // 基础信息
      purchase_id: number
      user_id: number
      username: string
      email: string
      content_type: string
      purchase_time: string

      // 支付信息
      amount_paid: number
      payment_method: string
      transaction_id: string

      // 积分消费详情
      points_used: number
      points_balance_before: number | null
      points_balance_after: number | null
      points_record_id: number | null

      // 配额消费详情
      quota_used: number
      quota_type: string
      exchange_used: boolean
      quota_record_id: number | null

      // 购买时用户状态
      vip_level_at_purchase: number

      // 消费方式分析
      cost_type: string
      cost_amount: number
      consumption_record_id: number | null
    }>
    total_count: number
  }>> {
    return http.get(`/articles/${articleId}/buyers`)
  }
}
