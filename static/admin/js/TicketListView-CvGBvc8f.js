var M=(C,p,d)=>new Promise((a,r)=>{var o=n=>{try{c(d.next(n))}catch(u){r(u)}},m=n=>{try{c(d.throw(n))}catch(u){r(u)}},c=n=>n.done?a(n.value):Promise.resolve(n.value).then(o,m);c((d=d.apply(C,p)).next())});import{d as P,i as _,m as h,k as U,c as y,a as t,g as V,n as f,p as w,s as k,b as j,e as z,F as I,l as Z,t as i,o as b}from"./index-Bo6OtMFR.js";import{_ as E}from"./_plugin-vue_export-helper-DlAUqK2U.js";const q={class:"ticket-management p-4"},A={class:"admin-card mb-6"},G={class:"p-4"},H={class:"flex items-center justify-between mb-4"},J={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},K={class:"admin-card p-4 mb-6 overflow-x-auto"},O={class:"table-admin w-full"},Q={id:"tickets-tbody"},R={class:"px-4 py-3 border-t border-gray-700"},W={class:"px-4 py-3 border-t border-gray-700 max-w-xs truncate"},X={class:"px-4 py-3 border-t border-gray-700"},Y={class:"px-4 py-3 border-t border-gray-700"},tt={class:"px-2 py-1 rounded-full text-xs bg-blue-900 text-blue-300"},et={class:"px-4 py-3 border-t border-gray-700"},st={class:"px-4 py-3 border-t border-gray-700"},lt={class:"px-4 py-3 border-t border-gray-700 text-sm text-gray-400"},ot={class:"px-4 py-3 border-t border-gray-700"},at={class:"flex space-x-1"},nt=["onClick","data-id"],it=["onClick","data-id"],dt=["onClick","data-id"],rt=["onClick","data-id"],pt={key:0},ct={class:"pagination"},ut=["disabled"],gt={class:"page-info"},xt=["disabled"],yt=P({__name:"TicketListView",setup(C){const p=_([]),d=_(!1);h({pending_tickets:12,processing_tickets:8,today_tickets:5,avg_response_time:2.5});const a=h({search:"",status:"all",priority:"all",category:"all",page:1,per_page:20}),r=_(!1),o=h({page:1,pages:1,total:0,per_page:20}),m=s=>new Date(s).toLocaleDateString("zh-CN"),c=s=>({pending:"待处理",processing:"处理中",resolved:"已解决",closed:"已关闭"})[s]||s,n=s=>({low:"低",medium:"中",high:"高"})[s]||s,u=s=>({high:"px-2 py-1 rounded-full text-xs bg-red-900 text-red-300",medium:"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300",low:"px-2 py-1 rounded-full text-xs bg-green-900 text-green-300"})[s]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",$=s=>({pending:"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300",processing:"px-2 py-1 rounded-full text-xs bg-blue-900 text-blue-300",resolved:"px-2 py-1 rounded-full text-xs bg-green-900 text-green-300",closed:"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300"})[s]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",D=()=>{r.value=!r.value},g=()=>{a.page=1,x()},S=()=>{x()},x=()=>M(this,null,function*(){d.value=!0;try{p.value=[{id:1,title:"登录问题",content:"无法正常登录系统，提示密码错误但密码确实正确",status:"pending",priority:"high",category:"technical",user:{username:"user1"},created_at:"2024-01-01T00:00:00Z"},{id:2,title:"功能建议",content:"希望能增加夜间模式功能",status:"processing",priority:"normal",category:"feature",user:{username:"user2"},created_at:"2024-01-02T00:00:00Z"}],o.total=2,o.pages=1}catch(s){console.error("加载工单失败:",s)}finally{d.value=!1}}),B=s=>{console.log("查看工单:",s)},F=s=>{console.log("编辑工单:",s)},N=s=>{console.log("回复工单:",s)},L=s=>{confirm(`确定要删除工单"${s.title}"吗？`)&&console.log("删除工单:",s)},T=s=>{a.page=s,o.page=s,x()};return U(()=>{x()}),(s,e)=>(b(),y("div",q,[t("div",{class:"flex justify-between items-center mb-6"},[e[6]||(e[6]=t("h1",{class:"text-2xl font-bold text-yellow-400"},"💬 工单管理",-1)),t("button",{onClick:S,id:"refresh-tickets",class:"admin-btn-primary"},e[5]||(e[5]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),t("span",{class:"hidden sm:inline"},"刷新",-1)]))]),t("div",A,[t("div",G,[t("div",H,[e[7]||(e[7]=t("h3",{class:"text-lg font-medium text-yellow-400"},[t("i",{class:"fas fa-filter mr-2"}),V("筛选条件 ")],-1)),t("button",{onClick:D,id:"toggle-filters",class:"text-gray-400 hover:text-yellow-400 md:hidden"},[t("i",{class:f(["fas",r.value?"fa-chevron-up":"fa-chevron-down"]),id:"filter-icon"},null,2)])]),t("div",{class:f(["filter-content",r.value?"block":"hidden md:block"]),id:"filter-content"},[t("div",J,[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"状态",-1)),w(t("select",{"onUpdate:modelValue":e[0]||(e[0]=l=>a.status=l),id:"status-filter",class:"form-input w-full",onChange:g},e[8]||(e[8]=[j('<option value="all" data-v-50d4212d>全部状态</option><option value="pending" data-v-50d4212d>待处理</option><option value="processing" data-v-50d4212d>处理中</option><option value="resolved" data-v-50d4212d>已解决</option><option value="closed" data-v-50d4212d>已关闭</option>',5)]),544),[[k,a.status]])]),t("div",null,[e[11]||(e[11]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"分类",-1)),w(t("select",{"onUpdate:modelValue":e[1]||(e[1]=l=>a.category=l),id:"category-filter",class:"form-input w-full",onChange:g},e[10]||(e[10]=[t("option",{value:"all"},"全部分类",-1),t("option",{value:"售前咨询"},"售前咨询",-1),t("option",{value:"售后支持"},"售后支持",-1)]),544),[[k,a.category]])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-gray-300 mb-2"},"优先级",-1)),w(t("select",{"onUpdate:modelValue":e[2]||(e[2]=l=>a.priority=l),id:"priority-filter",class:"form-input w-full",onChange:g},e[12]||(e[12]=[t("option",{value:"all"},"全部优先级",-1),t("option",{value:"low"},"低",-1),t("option",{value:"normal"},"普通",-1),t("option",{value:"high"},"高",-1)]),544),[[k,a.priority]])]),t("div",{class:"flex items-end"},[t("button",{onClick:g,id:"apply-filters",class:"admin-btn-primary w-full"},e[14]||(e[14]=[t("i",{class:"fas fa-search mr-2"},null,-1),V("筛选 ",-1)]))])])],2)])]),t("div",K,[t("table",O,[e[20]||(e[20]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-2 text-left"},"ID"),t("th",{class:"px-4 py-2 text-left"},"标题"),t("th",{class:"px-4 py-2 text-left"},"用户"),t("th",{class:"px-4 py-2 text-left"},"分类"),t("th",{class:"px-4 py-2 text-left"},"优先级"),t("th",{class:"px-4 py-2 text-left"},"状态"),t("th",{class:"px-4 py-2 text-left"},"创建时间"),t("th",{class:"px-4 py-2 text-left"},"操作")])],-1)),t("tbody",Q,[(b(!0),y(I,null,Z(p.value,l=>(b(),y("tr",{key:l.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",R,i(l.id),1),t("td",W,i(l.title),1),t("td",X,i(l.user_name),1),t("td",Y,[t("span",tt,i(l.category),1)]),t("td",et,[t("span",{class:f(u(l.priority))},i(n(l.priority)),3)]),t("td",st,[t("span",{class:f($(l.status))},i(c(l.status)),3)]),t("td",lt,i(m(l.created_at)),1),t("td",ot,[t("div",at,[t("button",{onClick:v=>B(l),class:"view-ticket-btn admin-btn-secondary px-2 py-1 rounded text-sm","data-id":l.id,title:"查看详情"},e[15]||(e[15]=[t("i",{class:"fas fa-eye"},null,-1)]),8,nt),t("button",{onClick:v=>F(l),class:"edit-ticket-btn admin-btn-primary px-2 py-1 rounded text-sm","data-id":l.id,title:"编辑"},e[16]||(e[16]=[t("i",{class:"fas fa-edit"},null,-1)]),8,it),t("button",{onClick:v=>N(l),class:"reply-ticket-btn admin-btn-secondary px-2 py-1 rounded text-sm","data-id":l.id,title:"回复"},e[17]||(e[17]=[t("i",{class:"fas fa-reply"},null,-1)]),8,dt),t("button",{onClick:v=>L(l),class:"delete-ticket-btn admin-btn-danger px-2 py-1 rounded text-sm","data-id":l.id,title:"删除"},e[18]||(e[18]=[t("i",{class:"fas fa-trash-alt"},null,-1)]),8,rt)])])]))),128)),p.value.length===0?(b(),y("tr",pt,e[19]||(e[19]=[t("td",{colspan:"8",class:"px-4 py-8 text-center text-gray-400"},[t("i",{class:"fas fa-ticket-alt text-4xl mb-4"}),t("p",null,"暂无工单数据")],-1)]))):z("",!0)])]),t("div",ct,[t("button",{class:"btn-secondary",disabled:o.page<=1,onClick:e[3]||(e[3]=l=>T(o.page-1))}," 上一页 ",8,ut),t("span",gt," 第 "+i(o.page)+" 页，共 "+i(o.pages)+" 页 ",1),t("button",{class:"btn-secondary",disabled:o.page>=o.pages,onClick:e[4]||(e[4]=l=>T(o.page+1))}," 下一页 ",8,xt)])])]))}}),vt=E(yt,[["__scopeId","data-v-50d4212d"]]);export{vt as default};
