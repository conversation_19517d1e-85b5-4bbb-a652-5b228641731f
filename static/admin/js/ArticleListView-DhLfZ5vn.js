var y=(V,k,p)=>new Promise((C,d)=>{var f=x=>{try{r(p.next(x))}catch(h){d(h)}},u=x=>{try{r(p.throw(x))}catch(h){d(h)}},r=x=>x.done?C(x.value):Promise.resolve(x.value).then(f,u);r((p=p.apply(V,k)).next())});import{d as Y,i as M,m as S,j as Z,k as tt,c as n,a as s,f as q,g as _,w as B,r as st,t as l,p as A,v as et,q as ot,s as lt,e as g,n as j,x as I,F as T,l as E,E as i,y as nt,o as a}from"./index-Bo6OtMFR.js";import{a as w}from"./articles-7SnkQc40.js";import{E as L}from"./index-DU0nyBIP.js";import{_ as at}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DM_Oxv62.js";const rt={class:"article-management"},it={class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},dt={class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},ut={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},ct={class:"bg-gray-800 rounded-lg p-4 bg-gradient-to-br from-blue-900 to-indigo-900 border border-blue-700"},pt={class:"flex items-center justify-between"},xt={class:"text-2xl font-bold text-white"},gt={class:"bg-gray-800 rounded-lg p-4 bg-gradient-to-br from-green-900 to-emerald-900 border border-green-700"},ft={class:"flex items-center justify-between"},mt={class:"text-2xl font-bold text-white"},bt={class:"bg-gray-800 rounded-lg p-4 bg-gradient-to-br from-yellow-900 to-amber-900 border border-yellow-700"},yt={class:"flex items-center justify-between"},_t={class:"text-2xl font-bold text-white"},ht={class:"bg-gray-800 rounded-lg p-4 bg-gradient-to-br from-purple-900 to-indigo-900 border border-purple-700"},vt={class:"flex items-center justify-between"},wt={class:"text-2xl font-bold text-white"},kt={class:"admin-card p-4 mb-6"},Ct={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},At={class:"admin-card p-4 mb-6 overflow-x-auto table-responsive"},jt={class:"mb-4 flex justify-between items-center"},$t={class:"flex gap-2"},Mt=["disabled"],St={class:"table-admin w-full"},Tt={class:"px-4 py-2 text-left"},Vt={class:"px-4 py-2"},Dt=["value"],Pt={class:"px-4 py-2 text-gray-300"},qt={class:"px-4 py-2"},Bt={class:"text-white font-medium"},It={key:0,class:"text-gray-400 text-sm mt-1"},Et={class:"px-4 py-2 text-gray-300"},Lt={class:"px-4 py-2 text-gray-300"},Nt={class:"px-4 py-2"},Ut={class:"px-4 py-2"},zt={class:"flex flex-col gap-1 text-sm"},Ft={class:"flex items-center gap-2"},Kt=["title"],Gt={class:"flex items-center gap-2 text-xs"},Ot={class:"text-green-300"},Rt={key:0,class:"text-gray-500"},Ht={class:"flex items-center gap-2 text-xs"},Jt={class:"text-yellow-300"},Qt={key:0,class:"text-gray-500"},Wt={class:"flex items-center gap-2 text-xs"},Xt={class:"text-purple-300"},Yt={key:0,class:"text-gray-500"},Zt={key:0,class:"flex items-center gap-2 text-xs"},ts={class:"text-gray-300"},ss={key:0,class:"text-gray-500"},es={class:"px-4 py-2"},os={key:0,class:"text-yellow-400"},ls={key:1,class:"text-gray-500"},ns={class:"px-4 py-2"},as={class:"flex flex-wrap gap-1"},rs=["onClick"],is=["onClick"],ds=["onClick"],us=["onClick"],cs=["onClick"],ps=["onClick"],xs={key:0,class:"flex justify-center mt-6"},gs={class:"flex flex-wrap justify-center rounded-md shadow"},fs=["disabled"],ms=["onClick"],bs={key:1,class:"relative inline-flex items-center px-4 py-2 border border-gray-600 bg-gray-700 text-sm font-medium text-gray-300"},ys=["disabled"],_s=Y({__name:"ArticleListView",setup(V){const k=nt(),p=M([]),C=M(!1),d=M([]),f=S({total_articles:0,published_articles:0,draft_articles:0,pending_articles:0,today_published:0,this_week_published:0,this_month_published:0}),u=S({search:"",status:"",page:1,per_page:20}),r=S({page:1,pages:1,total:0,per_page:20}),x=Z({get:()=>d.value.length===p.value.length&&p.value.length>0,set:o=>{o?d.value=p.value.map(t=>t.id):d.value=[]}}),h=o=>new Date(o).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit"}),N=o=>({published:"已发布",draft:"草稿",archived:"已归档"})[o]||o,U=o=>({published:"px-2 py-1 rounded-full text-xs bg-green-900 text-green-300",draft:"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300",archived:"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300"})[o]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",D=o=>{const t=o.paid_purchase_count||0,c=o.quota_purchase_count||0,e=o.points_purchase_count||0,v=o.free_purchase_count||0;return t+c+e+v},z=()=>y(this,null,function*(){try{console.log("开始加载文章统计数据...");const o=yield fetch("/admin/api/articles/stats",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(console.log("API响应状态:",o.status),o.ok){const t=yield o.json();console.log("API响应数据:",t),t.success?(Object.assign(f,t.data),console.log("统计数据已更新:",f)):console.error("API返回错误:",t.error)}else console.error("API请求失败:",o.status,o.statusText)}catch(o){console.error("获取文章统计失败:",o)}}),m=()=>y(this,null,function*(){C.value=!0;try{const o={page:u.page,per_page:u.per_page,search:u.search||void 0,status:u.status||void 0},t=yield w.getList(o);t.success?(p.value=t.articles||[],t.pagination&&(r.page=t.pagination.page,r.pages=t.pagination.pages,r.total=t.pagination.total,r.per_page=t.pagination.per_page)):i.error(t.message||"加载文章列表失败")}catch(o){console.error("加载文章失败:",o),i.error("加载文章列表失败")}finally{C.value=!1}}),F=()=>{m()},P=()=>{u.page=1,m()},K=()=>{},G=()=>y(this,null,function*(){if(d.value.length===0){i.warning("请至少选择一篇文章进行删除");return}try{yield L.confirm(`确定要删除选中的 ${d.value.length} 篇文章吗？此操作无法撤销。`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const o=yield w.batchDelete(d.value);o.success?(i.success("批量删除成功"),d.value=[],m()):i.error(o.message||"批量删除失败")}catch(o){o!=="cancel"&&(console.error("批量删除失败:",o),i.error("批量删除失败"))}}),O=o=>{window.open(`/admin/reading/${o.id}`,"_blank")},R=o=>{console.log("编辑文章被调用"),console.log("文章对象:",o),console.log("文章ID:",o.id);const t=`/articles/${o.id}/edit`;console.log("目标路径:",t),k.push(t)},H=o=>{k.push(`/articles/${o.id}/buyers`)},J=o=>y(this,null,function*(){try{yield L.confirm(`确定要删除文章"${o.title}"吗？此操作无法撤销。`,"删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning"});const t=yield w.delete(o.id);t.success?(i.success("删除成功"),m()):i.error(t.message||"删除失败")}catch(t){t!=="cancel"&&(console.error("删除失败:",t),i.error("删除失败"))}}),Q=o=>y(this,null,function*(){try{const t=yield w.updateStatus(o.id,"published");t.success?(i.success("发布成功"),m()):i.error(t.message||"发布失败")}catch(t){console.error("发布失败:",t),i.error("发布失败")}}),W=o=>y(this,null,function*(){try{const t=yield w.updateStatus(o.id,"archived");t.success?(i.success("归档成功"),m()):i.error(t.message||"归档失败")}catch(t){console.error("归档失败:",t),i.error("归档失败")}}),$=o=>{u.page=o,r.page=o,m()},X=()=>{const o=[],t=r.page,c=r.pages;if(c<=7)for(let e=1;e<=c;e++)o.push(e);else if(t<=4){for(let e=1;e<=5;e++)o.push(e);o.push("..."),o.push(c)}else if(t>=c-3){o.push(1),o.push("...");for(let e=c-4;e<=c;e++)o.push(e)}else{o.push(1),o.push("...");for(let e=t-1;e<=t+1;e++)o.push(e);o.push("..."),o.push(c)}return o};return tt(()=>{z(),m()}),(o,t)=>{const c=st("router-link");return a(),n("div",rt,[s("div",it,[t[8]||(t[8]=s("h1",{class:"text-2xl font-bold text-white"},"文章列表",-1)),s("div",dt,[s("button",{onClick:F,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},t[6]||(t[6]=[s("i",{class:"fas fa-sync-alt mr-2"},null,-1),_("刷新列表 ",-1)])),q(c,{to:"/articles/create",class:"admin-btn-primary px-4 py-2 rounded-lg text-center"},{default:B(()=>t[7]||(t[7]=[s("i",{class:"fas fa-plus mr-2"},null,-1),_("添加文章 ",-1)])),_:1,__:[7]})])]),s("div",ut,[s("div",ct,[s("div",pt,[s("div",null,[t[9]||(t[9]=s("h2",{class:"text-gray-300 mb-1"},"总文章数",-1)),s("p",xt,l(f.total_articles),1)]),t[10]||(t[10]=s("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-blue-800 text-blue-200"},[s("i",{class:"fas fa-file-alt text-xl"})],-1))])]),s("div",gt,[s("div",ft,[s("div",null,[t[11]||(t[11]=s("h2",{class:"text-gray-300 mb-1"},"已发布",-1)),s("p",mt,l(f.published_articles),1)]),t[12]||(t[12]=s("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-green-800 text-green-200"},[s("i",{class:"fas fa-check-circle text-xl"})],-1))])]),s("div",bt,[s("div",yt,[s("div",null,[t[13]||(t[13]=s("h2",{class:"text-gray-300 mb-1"},"草稿",-1)),s("p",_t,l(f.draft_articles),1)]),t[14]||(t[14]=s("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-yellow-800 text-yellow-200"},[s("i",{class:"fas fa-edit text-xl"})],-1))])]),s("div",ht,[s("div",vt,[s("div",null,[t[15]||(t[15]=s("h2",{class:"text-gray-300 mb-1"},"今日发布",-1)),s("p",wt,l(f.today_published),1)]),t[16]||(t[16]=s("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-purple-800 text-purple-200"},[s("i",{class:"fas fa-calendar-day text-xl"})],-1))])])]),s("div",kt,[s("div",Ct,[s("div",null,[t[17]||(t[17]=s("label",{for:"search",class:"block mb-2 text-gray-300"},"搜索标题",-1)),A(s("input",{type:"text",id:"search","onUpdate:modelValue":t[0]||(t[0]=e=>u.search=e),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"输入文章标题关键词...",onKeyup:ot(P,["enter"])},null,544),[[et,u.search]])]),s("div",null,[t[19]||(t[19]=s("label",{for:"status",class:"block mb-2 text-gray-300"},"状态过滤",-1)),A(s("select",{id:"status","onUpdate:modelValue":t[1]||(t[1]=e=>u.status=e),class:"form-input w-full px-3 py-2 rounded-lg"},t[18]||(t[18]=[s("option",{value:""},"全部状态",-1),s("option",{value:"draft"},"草稿",-1),s("option",{value:"published"},"已发布",-1),s("option",{value:"archived"},"已归档",-1)]),512),[[lt,u.status]])]),s("div",{class:"flex items-end"},[s("button",{onClick:P,class:"admin-btn-primary px-4 py-2 rounded-lg w-full"},t[20]||(t[20]=[s("i",{class:"fas fa-search mr-2"},null,-1),_("搜索 ",-1)]))])])]),s("div",At,[s("div",jt,[s("div",$t,[s("button",{id:"btn-batch-delete-articles",disabled:d.value.length===0,onClick:G,class:j(["btn btn-outline text-red-500",{"opacity-50 cursor-not-allowed":d.value.length===0}])},t[21]||(t[21]=[s("i",{class:"bi bi-trash mr-1"},null,-1),_(" 批量删除选中 ",-1)]),10,Mt)]),q(c,{to:"/articles/create",class:"btn btn-primary"},{default:B(()=>t[22]||(t[22]=[s("i",{class:"bi bi-plus-lg mr-1"},null,-1),_(" 创建新文章 ",-1)])),_:1,__:[22]})]),s("table",St,[s("thead",null,[s("tr",null,[s("th",Tt,[A(s("input",{type:"checkbox",id:"select-all-articles","onUpdate:modelValue":t[2]||(t[2]=e=>x.value=e),onChange:K,class:"form-checkbox h-4 w-4 text-yellow-500"},null,544),[[I,x.value]])]),t[23]||(t[23]=s("th",{class:"px-4 py-2 text-left"},"ID",-1)),t[24]||(t[24]=s("th",{class:"px-4 py-2 text-left"},"标题",-1)),t[25]||(t[25]=s("th",{class:"px-4 py-2 text-left"},"塔罗师",-1)),t[26]||(t[26]=s("th",{class:"px-4 py-2 text-left"},"创建时间",-1)),t[27]||(t[27]=s("th",{class:"px-4 py-2 text-left"},"状态",-1)),t[28]||(t[28]=s("th",{class:"px-4 py-2 text-left"},"购买统计",-1)),t[29]||(t[29]=s("th",{class:"px-4 py-2 text-left"},"是否有高级内容",-1)),t[30]||(t[30]=s("th",{class:"px-4 py-2 text-left"},"操作",-1))])]),s("tbody",null,[(a(!0),n(T,null,E(p.value,e=>{var v;return a(),n("tr",{key:e.id,class:"article-row hover:bg-gray-800 transition-colors"},[s("td",Vt,[A(s("input",{type:"checkbox",value:e.id,"onUpdate:modelValue":t[3]||(t[3]=b=>d.value=b),class:"select-article-item form-checkbox h-4 w-4 text-yellow-500"},null,8,Dt),[[I,d.value]])]),s("td",Pt,l(e.id),1),s("td",qt,[s("div",Bt,l(e.title),1),e.subtitle?(a(),n("div",It,l(e.subtitle),1)):g("",!0)]),s("td",Et,l(((v=e.reader)==null?void 0:v.name)||"未知塔罗师"),1),s("td",Lt,l(h(e.created_at)),1),s("td",Nt,[s("span",{class:j(U(e.status))},l(N(e.status)),3)]),s("td",Ut,[s("div",zt,[s("div",Ft,[t[31]||(t[31]=s("span",{class:"text-gray-400"},"总计:",-1)),s("span",{class:j((e.purchase_count||0)>0?"text-blue-400 font-semibold":"text-gray-400")},l(e.purchase_count||0),3),D(e)!==(e.purchase_count||0)?(a(),n("span",{key:0,class:"text-red-400 text-xs",title:`数据不一致: 总计${e.purchase_count||0} != 各项之和${D(e)}`}," ⚠️ ",8,Kt)):g("",!0)]),s("div",Gt,[t[32]||(t[32]=s("span",{class:"text-green-400",title:"现金购买"},"💰",-1)),s("span",Ot,l(e.paid_purchase_count||0),1),e.cash_basic_count||e.cash_premium_count?(a(),n("span",Rt," (基础:"+l(e.cash_basic_count||0)+" 高级:"+l(e.cash_premium_count||0)+") ",1)):g("",!0)]),s("div",Ht,[t[33]||(t[33]=s("span",{class:"text-yellow-400",title:"配额购买"},"🎫",-1)),s("span",Jt,l(e.quota_purchase_count||0),1),e.quota_basic_count||e.quota_premium_count?(a(),n("span",Qt," (基础:"+l(e.quota_basic_count||0)+" 高级:"+l(e.quota_premium_count||0)+") ",1)):g("",!0)]),s("div",Wt,[t[34]||(t[34]=s("span",{class:"text-purple-400",title:"积分购买"},"⭐",-1)),s("span",Xt,l(e.points_purchase_count||0),1),e.points_basic_count||e.points_premium_count?(a(),n("span",Yt," (基础:"+l(e.points_basic_count||0)+" 高级:"+l(e.points_premium_count||0)+") ",1)):g("",!0)]),(e.free_purchase_count||0)>0?(a(),n("div",Zt,[t[35]||(t[35]=s("span",{class:"text-gray-400",title:"免费/历史数据"},"🆓",-1)),s("span",ts,l(e.free_purchase_count||0),1),e.free_basic_count||e.free_premium_count?(a(),n("span",ss," (基础:"+l(e.free_basic_count||0)+" 高级:"+l(e.free_premium_count||0)+") ",1)):g("",!0)])):g("",!0)])]),s("td",es,[e.has_premium?(a(),n("span",os,t[36]||(t[36]=[s("i",{class:"fas fa-crown mr-1"},null,-1),_("是 ",-1)]))):(a(),n("span",ls,"否"))]),s("td",ns,[s("div",as,[s("button",{onClick:b=>O(e),class:"admin-btn-sm admin-btn-primary",title:"管理员阅读"},t[37]||(t[37]=[s("i",{class:"fas fa-eye text-xs"},null,-1)]),8,rs),s("button",{onClick:b=>R(e),class:"admin-btn-sm admin-btn-secondary",title:"编辑文章"},t[38]||(t[38]=[s("i",{class:"fas fa-edit text-xs"},null,-1)]),8,is),s("button",{onClick:b=>H(e),class:"admin-btn-sm admin-btn-info",title:"查看购买者"},t[39]||(t[39]=[s("i",{class:"fas fa-users text-xs"},null,-1)]),8,ds),s("button",{onClick:b=>J(e),class:"admin-btn-sm admin-btn-danger",title:"删除文章"},t[40]||(t[40]=[s("i",{class:"fas fa-trash text-xs"},null,-1)]),8,us),e.status==="draft"?(a(),n("button",{key:0,onClick:b=>Q(e),class:"admin-btn-sm admin-btn-success",title:"发布文章"},t[41]||(t[41]=[s("i",{class:"fas fa-paper-plane text-xs"},null,-1)]),8,cs)):e.status==="published"?(a(),n("button",{key:1,onClick:b=>W(e),class:"admin-btn-sm admin-btn-warning",title:"归档文章"},t[42]||(t[42]=[s("i",{class:"fas fa-archive text-xs"},null,-1)]),8,ps)):g("",!0)])])])}),128))])]),r.pages>1?(a(),n("div",xs,[s("nav",gs,[s("button",{disabled:r.page<=1,onClick:t[4]||(t[4]=e=>$(r.page-1)),class:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-600 bg-gray-700 text-sm font-medium text-gray-300 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"},t[43]||(t[43]=[s("span",{class:"sr-only"},"上一页",-1),s("i",{class:"fas fa-chevron-left"},null,-1)]),8,fs),(a(!0),n(T,null,E(X(),e=>(a(),n(T,{key:e},[e!=="..."?(a(),n("button",{key:0,onClick:v=>$(e),class:j(["relative inline-flex items-center px-4 py-2 border border-gray-600 text-sm font-medium",e===r.page?"z-10 bg-yellow-500 border-yellow-500 text-gray-900":"bg-gray-700 text-gray-300 hover:bg-gray-600"])},l(e),11,ms)):(a(),n("span",bs," ... "))],64))),128)),s("button",{disabled:r.page>=r.pages,onClick:t[5]||(t[5]=e=>$(r.page+1)),class:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-600 bg-gray-700 text-sm font-medium text-gray-300 hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"},t[44]||(t[44]=[s("span",{class:"sr-only"},"下一页",-1),s("i",{class:"fas fa-chevron-right"},null,-1)]),8,ys)])])):g("",!0)])])}}}),js=at(_s,[["__scopeId","data-v-6a3e7afd"]]);export{js as default};
