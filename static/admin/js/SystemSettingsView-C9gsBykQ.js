var V=Object.defineProperty;var v=Object.getOwnPropertySymbols;var c=Object.prototype.hasOwnProperty,U=Object.prototype.propertyIsEnumerable;var _=(n,i,t)=>i in n?V(n,i,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[i]=t,y=(n,i)=>{for(var t in i||(i={}))c.call(i,t)&&_(n,t,i[t]);if(v)for(var t of v(i))U.call(i,t)&&_(n,t,i[t]);return n};var x=(n,i,t)=>new Promise((m,f)=>{var b=l=>{try{r(t.next(l))}catch(s){f(s)}},g=l=>{try{r(t.throw(l))}catch(s){f(s)}},r=l=>l.done?m(l.value):Promise.resolve(l.value).then(b,g);r((t=t.apply(n,i)).next())});import{d as S,i as w,m as q,k as M,c as j,a as e,g as d,B as T,p as o,v as a,s as p,E as u,o as z}from"./index-DNYdNs_d.js";import{s as k}from"./settings-Bm6hMxSa.js";const B={class:"system-settings"},C={class:"admin-card p-6 mb-6"},P={class:"mb-8"},E={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},A={class:"mb-8"},N={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},O={class:"mb-8"},D={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},F={class:"mb-8"},I={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},L={class:"mb-8"},R={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},K=S({__name:"SystemSettingsView",setup(n){const i=w(!1),t=q({site_name:"塔罗嗅嗅",site_description:"专业的塔罗牌占卜平台",admin_email:"<EMAIL>",contact_phone:"************",allow_registration:"true",require_email_verification:"true",default_user_quota:100,max_login_attempts:5,articles_per_page:20,auto_approve_articles:"false",max_upload_size:10,allowed_file_types:"jpg,png,gif,pdf,doc,docx",enable_csrf_protection:"true",session_timeout:30,enable_rate_limiting:"true",backup_frequency:"daily",smtp_server:"smtp.gmail.com",smtp_port:587,smtp_username:"",smtp_use_tls:"true"}),m=w({}),f=()=>x(this,null,function*(){i.value=!0;try{const r=yield k.updateSystemSettings(t);r.success?(u.success("系统设置保存成功"),m.value=y({},t)):u.error(r.error||"保存设置失败")}catch(r){console.error("保存设置失败:",r),u.error("保存设置失败")}finally{i.value=!1}}),b=()=>{Object.assign(t,m.value),u.info("设置已重置")},g=()=>x(this,null,function*(){i.value=!0;try{const r=yield k.getSystemSettings();r.success?(Object.assign(t,r.data),m.value=y({},r.data)):u.error(r.error||"加载设置失败")}catch(r){console.error("加载设置失败:",r),u.error("加载设置失败")}finally{i.value=!1}});return M(()=>{g()}),(r,l)=>(z(),j("div",B,[e("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[l[22]||(l[22]=e("h1",{class:"text-2xl font-bold text-white"},"系统设置",-1)),e("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[e("button",{onClick:b,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},l[20]||(l[20]=[e("i",{class:"fas fa-undo mr-2"},null,-1),d("重置设置 ",-1)])),e("button",{onClick:f,class:"admin-btn-primary px-4 py-2 rounded-lg"},l[21]||(l[21]=[e("i",{class:"fas fa-save mr-2"},null,-1),d("保存设置 ",-1)]))])]),e("div",C,[e("form",{onSubmit:T(f,["prevent"])},[e("div",P,[l[27]||(l[27]=e("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[e("i",{class:"fas fa-cog mr-2 text-yellow-500"}),d("基础设置 ")],-1)),e("div",E,[e("div",null,[l[23]||(l[23]=e("label",{for:"site_name",class:"block mb-2 text-gray-300"},"网站名称",-1)),o(e("input",{type:"text",id:"site_name","onUpdate:modelValue":l[0]||(l[0]=s=>t.site_name=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"塔罗嗅嗅"},null,512),[[a,t.site_name]])]),e("div",null,[l[24]||(l[24]=e("label",{for:"site_description",class:"block mb-2 text-gray-300"},"网站描述",-1)),o(e("input",{type:"text",id:"site_description","onUpdate:modelValue":l[1]||(l[1]=s=>t.site_description=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"专业的塔罗牌占卜平台"},null,512),[[a,t.site_description]])]),e("div",null,[l[25]||(l[25]=e("label",{for:"admin_email",class:"block mb-2 text-gray-300"},"管理员邮箱",-1)),o(e("input",{type:"email",id:"admin_email","onUpdate:modelValue":l[2]||(l[2]=s=>t.admin_email=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"<EMAIL>"},null,512),[[a,t.admin_email]])]),e("div",null,[l[26]||(l[26]=e("label",{for:"contact_phone",class:"block mb-2 text-gray-300"},"联系电话",-1)),o(e("input",{type:"tel",id:"contact_phone","onUpdate:modelValue":l[3]||(l[3]=s=>t.contact_phone=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"************"},null,512),[[a,t.contact_phone]])])])]),e("div",A,[l[34]||(l[34]=e("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[e("i",{class:"fas fa-users mr-2 text-blue-500"}),d("用户设置 ")],-1)),e("div",N,[e("div",null,[l[29]||(l[29]=e("label",{for:"allow_registration",class:"block mb-2 text-gray-300"},"允许用户注册",-1)),o(e("select",{id:"allow_registration","onUpdate:modelValue":l[4]||(l[4]=s=>t.allow_registration=s),class:"form-input w-full px-3 py-2 rounded-lg"},l[28]||(l[28]=[e("option",{value:"true"},"允许",-1),e("option",{value:"false"},"禁止",-1)]),512),[[p,t.allow_registration]])]),e("div",null,[l[31]||(l[31]=e("label",{for:"require_email_verification",class:"block mb-2 text-gray-300"},"邮箱验证",-1)),o(e("select",{id:"require_email_verification","onUpdate:modelValue":l[5]||(l[5]=s=>t.require_email_verification=s),class:"form-input w-full px-3 py-2 rounded-lg"},l[30]||(l[30]=[e("option",{value:"true"},"必须验证",-1),e("option",{value:"false"},"无需验证",-1)]),512),[[p,t.require_email_verification]])]),e("div",null,[l[32]||(l[32]=e("label",{for:"default_user_quota",class:"block mb-2 text-gray-300"},"默认用户配额",-1)),o(e("input",{type:"number",id:"default_user_quota","onUpdate:modelValue":l[6]||(l[6]=s=>t.default_user_quota=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"100",min:"0"},null,512),[[a,t.default_user_quota]])]),e("div",null,[l[33]||(l[33]=e("label",{for:"max_login_attempts",class:"block mb-2 text-gray-300"},"最大登录尝试次数",-1)),o(e("input",{type:"number",id:"max_login_attempts","onUpdate:modelValue":l[7]||(l[7]=s=>t.max_login_attempts=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"5",min:"1"},null,512),[[a,t.max_login_attempts]])])])]),e("div",O,[l[40]||(l[40]=e("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[e("i",{class:"fas fa-file-alt mr-2 text-green-500"}),d("内容设置 ")],-1)),e("div",D,[e("div",null,[l[35]||(l[35]=e("label",{for:"articles_per_page",class:"block mb-2 text-gray-300"},"每页文章数量",-1)),o(e("input",{type:"number",id:"articles_per_page","onUpdate:modelValue":l[8]||(l[8]=s=>t.articles_per_page=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"20",min:"1"},null,512),[[a,t.articles_per_page]])]),e("div",null,[l[37]||(l[37]=e("label",{for:"auto_approve_articles",class:"block mb-2 text-gray-300"},"文章自动审核",-1)),o(e("select",{id:"auto_approve_articles","onUpdate:modelValue":l[9]||(l[9]=s=>t.auto_approve_articles=s),class:"form-input w-full px-3 py-2 rounded-lg"},l[36]||(l[36]=[e("option",{value:"true"},"自动通过",-1),e("option",{value:"false"},"人工审核",-1)]),512),[[p,t.auto_approve_articles]])]),e("div",null,[l[38]||(l[38]=e("label",{for:"max_upload_size",class:"block mb-2 text-gray-300"},"最大上传文件大小 (MB)",-1)),o(e("input",{type:"number",id:"max_upload_size","onUpdate:modelValue":l[10]||(l[10]=s=>t.max_upload_size=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"10",min:"1"},null,512),[[a,t.max_upload_size]])]),e("div",null,[l[39]||(l[39]=e("label",{for:"allowed_file_types",class:"block mb-2 text-gray-300"},"允许的文件类型",-1)),o(e("input",{type:"text",id:"allowed_file_types","onUpdate:modelValue":l[11]||(l[11]=s=>t.allowed_file_types=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"jpg,png,gif,pdf,doc,docx"},null,512),[[a,t.allowed_file_types]])])])]),e("div",F,[l[48]||(l[48]=e("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[e("i",{class:"fas fa-shield-alt mr-2 text-red-500"}),d("安全设置 ")],-1)),e("div",I,[e("div",null,[l[42]||(l[42]=e("label",{for:"enable_csrf_protection",class:"block mb-2 text-gray-300"},"CSRF保护",-1)),o(e("select",{id:"enable_csrf_protection","onUpdate:modelValue":l[12]||(l[12]=s=>t.enable_csrf_protection=s),class:"form-input w-full px-3 py-2 rounded-lg"},l[41]||(l[41]=[e("option",{value:"true"},"启用",-1),e("option",{value:"false"},"禁用",-1)]),512),[[p,t.enable_csrf_protection]])]),e("div",null,[l[43]||(l[43]=e("label",{for:"session_timeout",class:"block mb-2 text-gray-300"},"会话超时时间 (分钟)",-1)),o(e("input",{type:"number",id:"session_timeout","onUpdate:modelValue":l[13]||(l[13]=s=>t.session_timeout=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"30",min:"5"},null,512),[[a,t.session_timeout]])]),e("div",null,[l[45]||(l[45]=e("label",{for:"enable_rate_limiting",class:"block mb-2 text-gray-300"},"API限流",-1)),o(e("select",{id:"enable_rate_limiting","onUpdate:modelValue":l[14]||(l[14]=s=>t.enable_rate_limiting=s),class:"form-input w-full px-3 py-2 rounded-lg"},l[44]||(l[44]=[e("option",{value:"true"},"启用",-1),e("option",{value:"false"},"禁用",-1)]),512),[[p,t.enable_rate_limiting]])]),e("div",null,[l[47]||(l[47]=e("label",{for:"backup_frequency",class:"block mb-2 text-gray-300"},"备份频率",-1)),o(e("select",{id:"backup_frequency","onUpdate:modelValue":l[15]||(l[15]=s=>t.backup_frequency=s),class:"form-input w-full px-3 py-2 rounded-lg"},l[46]||(l[46]=[e("option",{value:"daily"},"每日",-1),e("option",{value:"weekly"},"每周",-1),e("option",{value:"monthly"},"每月",-1),e("option",{value:"disabled"},"禁用",-1)]),512),[[p,t.backup_frequency]])])])]),e("div",L,[l[54]||(l[54]=e("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[e("i",{class:"fas fa-envelope mr-2 text-purple-500"}),d("邮件设置 ")],-1)),e("div",R,[e("div",null,[l[49]||(l[49]=e("label",{for:"smtp_server",class:"block mb-2 text-gray-300"},"SMTP服务器",-1)),o(e("input",{type:"text",id:"smtp_server","onUpdate:modelValue":l[16]||(l[16]=s=>t.smtp_server=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"smtp.gmail.com"},null,512),[[a,t.smtp_server]])]),e("div",null,[l[50]||(l[50]=e("label",{for:"smtp_port",class:"block mb-2 text-gray-300"},"SMTP端口",-1)),o(e("input",{type:"number",id:"smtp_port","onUpdate:modelValue":l[17]||(l[17]=s=>t.smtp_port=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"587"},null,512),[[a,t.smtp_port]])]),e("div",null,[l[51]||(l[51]=e("label",{for:"smtp_username",class:"block mb-2 text-gray-300"},"SMTP用户名",-1)),o(e("input",{type:"text",id:"smtp_username","onUpdate:modelValue":l[18]||(l[18]=s=>t.smtp_username=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"<EMAIL>"},null,512),[[a,t.smtp_username]])]),e("div",null,[l[53]||(l[53]=e("label",{for:"smtp_use_tls",class:"block mb-2 text-gray-300"},"使用TLS",-1)),o(e("select",{id:"smtp_use_tls","onUpdate:modelValue":l[19]||(l[19]=s=>t.smtp_use_tls=s),class:"form-input w-full px-3 py-2 rounded-lg"},l[52]||(l[52]=[e("option",{value:"true"},"启用",-1),e("option",{value:"false"},"禁用",-1)]),512),[[p,t.smtp_use_tls]])])])]),e("div",{class:"flex justify-end space-x-4"},[e("button",{type:"button",onClick:b,class:"admin-btn-secondary px-6 py-2 rounded-lg"},l[55]||(l[55]=[e("i",{class:"fas fa-undo mr-2"},null,-1),d("重置 ",-1)])),l[56]||(l[56]=e("button",{type:"submit",class:"admin-btn-primary px-6 py-2 rounded-lg"},[e("i",{class:"fas fa-save mr-2"}),d("保存设置 ")],-1))])],32)])]))}});export{K as default};
