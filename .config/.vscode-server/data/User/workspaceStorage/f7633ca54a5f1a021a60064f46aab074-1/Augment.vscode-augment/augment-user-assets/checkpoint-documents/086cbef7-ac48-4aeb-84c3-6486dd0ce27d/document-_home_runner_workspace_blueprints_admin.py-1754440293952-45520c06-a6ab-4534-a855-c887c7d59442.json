{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "blueprints/admin.py"}, "originalCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nAdmin blueprint for tarot website.\nThis module contains routes for the admin interface, including article management.\n\"\"\"\n\nfrom flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_from_directory, session\nfrom flask_login import login_required, current_user\nfrom extensions import db\nfrom models import Article, Tag, Author, ArticleErrorReport, User, ReadingPreference, SystemSetting, Purchase, Order, Subscription, UserFinance, MediaFile, SupportTicket, SupportMessage\nfrom utils.decorators import admin_required, staff_required, customer_service_required, finance_required\nimport logging\nfrom datetime import date, datetime # 确保导入在不上他那个逼样。\nfrom werkzeug.utils import secure_filename\nfrom werkzeug.security import generate_password_hash\nimport re\nimport os\nimport uuid\n\n# 创建蓝图实例 - 使用admin作为蓝图名称，与模板中的url_for一致\nadmin_bp = Blueprint('admin', __name__, url_prefix='/admin')\nlogger = logging.getLogger(__name__)\n\ndef auto_detect_language(text):\n    \"\"\"自动检测文本语言\"\"\"\n    if not text or not text.strip():\n        return None\n    \n    # 计算中文字符比例\n    chinese_chars = len(re.findall(r'[\\u4e00-\\u9fff]', text))\n    total_chars = len(re.sub(r'\\s+', '', text))  # 去除空白字符\n    \n    if total_chars == 0:\n        return None\n    \n    chinese_ratio = chinese_chars / total_chars\n    \n    # 如果中文字符超过30%，认为是中文\n    if chinese_ratio > 0.3:\n        return 'zh'\n    \n    # 检测是否包含常见英文单词模式\n    english_words = len(re.findall(r'\\b[a-zA-Z]+\\b', text))\n    if english_words > 5:  # 包含超过5个英文单词\n        return 'en'\n    \n    # 默认返回中文（因为这是中文为主的塔罗网站）\n    return 'zh'\n\ndef auto_detect_publish_date(text, title):\n    \"\"\"自动检测发布日期\"\"\"\n    # 优先使用当前日期\n    current_date = date.today()\n    \n    # 可选：从标题或内容中提取日期信息\n    # 这里先简单返回当前日期，后续可以加入更复杂的日期提取逻辑\n    return current_date\n\n# Vue3管理后台路由 (暂时禁用，恢复原版Flask模板)\n# @admin_bp.route('/')\n# @admin_bp.route('/<path:path>')\n# @login_required\n# @staff_required\n# def vue_admin(path=''):\n#     \"\"\"Vue3管理后台 - SPA路由处理\"\"\"\n#     try:\n#         # 服务Vue3构建的index.html\n#         return send_from_directory('static/admin', 'index.html')\n#     except Exception as e:\n#         logger.error(f\"加载Vue3管理后台失败: {str(e)}\")\n#         return f\"管理后台加载失败: {str(e)}\", 500\n\n# 恢复原版Flask模板管理后台\n@admin_bp.route('/')\n@login_required\n@staff_required\ndef dashboard():\n    \"\"\"管理后台首页 - 原版Flask模板\"\"\"\n    try:\n        # 获取统计数据\n        from datetime import datetime, timedelta\n\n        # 用户统计\n        total_users = User.query.count()\n        new_users_today = User.query.filter(\n            User.created_at >= datetime.now().date()\n        ).count()\n\n        # 文章统计\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter_by(status='published').count()\n\n        # VIP用户统计\n        vip_users = User.query.filter(User.membership_type != 'free').count()\n\n        # 最近注册用户\n        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()\n\n        # 最近发布文章\n        recent_articles = Article.query.filter_by(status='published').order_by(\n            Article.created_at.desc()\n        ).limit(5).all()\n\n        stats = {\n            'user_count': total_users,\n            'new_users_today': new_users_today,\n            'article_count': total_articles,\n            'published_articles': published_articles,\n            'vip_users': vip_users\n        }\n\n        return render_template('admin/dashboard.html',\n                             stats=stats,\n                             recent_users=recent_users,\n                             recent_articles=recent_articles,\n                             now=datetime.now())\n    except Exception as e:\n        logger.error(f\"管理后台首页加载失败: {str(e)}\")\n        flash(f'加载失败: {str(e)}', 'error')\n        return render_template('admin/dashboard.html',\n                             stats={},\n                             recent_users=[],\n                             recent_articles=[],\n                             now=datetime.now())\n\n# ==================== Vue3管理后台独立路由 ====================\n\n# 静态资源路由 - 必须在SPA路由之前\n@admin_bp.route('/js/<path:filename>')\ndef vue_admin_js(filename):\n    \"\"\"Vue3管理后台 - JS文件服务\"\"\"\n    try:\n        return send_from_directory('static/admin/js', filename)\n    except Exception as e:\n        logger.error(f\"加载JS文件失败: {str(e)}\")\n        return f\"JS文件加载失败: {str(e)}\", 404\n\n@admin_bp.route('/css/<path:filename>')\ndef vue_admin_css(filename):\n    \"\"\"Vue3管理后台 - CSS文件服务\"\"\"\n    try:\n        return send_from_directory('static/admin/css', filename)\n    except Exception as e:\n        logger.error(f\"加载CSS文件失败: {str(e)}\")\n        return f\"CSS文件加载失败: {str(e)}\", 404\n\n\n\n\n\n# SPA路由 - 处理所有其他路径\n@admin_bp.route('/vue')\n@admin_bp.route('/vue/')\n@admin_bp.route('/vue/<path:path>')\n@login_required\n@staff_required\ndef vue_admin(path=''):\n    \"\"\"Vue3管理后台 - SPA路由处理\"\"\"\n    try:\n        # 服务Vue3构建的index.html\n        return send_from_directory('static/admin', 'index.html')\n    except Exception as e:\n        logger.error(f\"加载Vue3管理后台失败: {str(e)}\")\n        return f\"管理后台加载失败: {str(e)}\", 500\n\n# ==================== 原版Flask模板路由 ====================\n\n# 文章管理路由\n@admin_bp.route('/articles')\n@login_required\n@admin_required\ndef articles():\n    \"\"\"文章管理页面\"\"\"\n    page = request.args.get('page', 1, type=int)\n    per_page = request.args.get('per_page', 10, type=int) # 允许通过查询参数改变每页数量，默认为10\n    try:\n        articles_pagination = Article.query.order_by(Article.created_at.desc()).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        return render_template('admin/articles.html', \n                               articles=articles_pagination.items, \n                               pagination=articles_pagination,\n                               search=request.args.get('search', ''),\n                               status=request.args.get('status', ''))\n    except Exception as e:\n        logger.error(f\"加载文章管理页面失败: {str(e)}\")\n        flash('加载文章列表失败，请稍后再试。', 'error')\n        return render_template('admin/articles.html', articles=[], pagination=None, search='', status='') # 传递空列表和None\n\n# 删除文章API路由\n@admin_bp.route('/api/articles/backup_delete/<int:article_id>', methods=['POST', 'DELETE'])\n@login_required\n@admin_required\ndef backup_delete_article(article_id):\n    logger.critical(f\"CRITICAL_ADMIN_DEBUG: backup_delete_article function was called for article ID: {article_id}\")\n    \"\"\"备份并删除文章API - 简化版实现\"\"\"\n    logger.info(f\"删除文章API被调用，文章ID: {article_id}\")\n    try:\n        article = Article.query.get_or_404(article_id)\n        # 这里可以添加备份逻辑\n        db.session.delete(article)\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": f\"文章 {article_id} 已成功删除\"})\n    except Exception as e:\n        logger.error(f\"删除文章失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n    \n# 标签管理\n@admin_bp.route('/tags')\n@login_required\n@staff_required\ndef tags():\n    \"\"\"标签管理页面\"\"\"\n    # 初始加载时，仍然可以通过这里传递数据，或者前端完全通过API获取\n    all_tags = Tag.query.order_by(Tag.name).all()\n    return render_template('admin/tags.html', tags=all_tags)\n\n# 创建标签 (旧的表单处理，将被新的API替代，暂时保留或后续移除)\n@admin_bp.route('/tag/new', methods=['POST'])\n@login_required\n@staff_required\ndef create_tag():\n    \"\"\"创建新标签\"\"\"\n    name = request.form.get('name')\n    if not name:\n        flash('标签名不能为空', 'error')\n        return redirect(url_for('admin.tags'))\n    \n    existing_tag = Tag.query.filter_by(name=name).first()\n    if existing_tag:\n        flash(f'标签 \"{name}\" 已存在', 'error')\n        return redirect(url_for('admin.tags'))\n    \n    tag = Tag(name=name)\n    db.session.add(tag)\n    try:\n        db.session.commit()\n        flash('标签创建成功', 'success')\n    except Exception as e:\n        db.session.rollback()\n        flash(f'标签创建失败: {str(e)}', 'error')\n    \n    return redirect(url_for('admin.tags'))\n\n# 删除标签 (旧的表单处理，将被新的API替代，暂时保留或后续移除)\n@admin_bp.route('/tag/<int:tag_id>/delete', methods=['POST'])\n@login_required\n@staff_required\ndef delete_tag(tag_id):\n    \"\"\"删除标签\"\"\"\n    tag = Tag.query.get_or_404(tag_id)\n    try:\n        # 在删除标签前，需要处理与文章的关联\n        # article_tags 是关联表，SQLAlchemy 会自动处理多对多关系中关联表的记录删除\n        # 如果有其他直接关联或需要特殊处理的逻辑，在这里添加\n        \n        db.session.delete(tag)\n        db.session.commit()\n        flash('标签删除成功', 'success')\n    except Exception as e:\n        db.session.rollback()\n        flash(f'标签删除失败: {str(e)}', 'error')\n    \n    return redirect(url_for('admin.tags'))\n\n# --- 新增标签管理API端点 ---\n\n@admin_bp.route('/api/readers', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_readers():\n    \"\"\"获取塔罗师列表 (API) - 用于文章编辑页面\"\"\"\n    try:\n        # 查询所有作者（塔罗师）\n        readers_query = Author.query.order_by(Author.name).all()\n\n        # 构建返回数据\n        readers_data = []\n        for reader in readers_query:\n            readers_data.append({\n                'id': reader.id,\n                'name': reader.name,\n                'email': getattr(reader, 'email', ''),\n                'bio': reader.description or '',\n                'avatar': getattr(reader, 'avatar', ''),\n                'status': 'active',  # 默认状态\n                'article_count': len(reader.articles) if hasattr(reader, 'articles') else 0\n            })\n\n        return jsonify({\"success\": True, \"data\": readers_data})\n    except Exception as e:\n        logger.error(f\"获取塔罗师列表API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_tags():\n    \"\"\"获取所有标签 (API)\"\"\"\n    try:\n        tags_query = Tag.query.order_by(Tag.name).all()\n        # 对于每个标签，计算文章数量\n        # 这种方式会在循环中对每个标签执行一次 len(tag.articles)，可能有效率问题\n        # 更优的方式是使用 sqlalchemy.func.count 和 group_by\n        # from sqlalchemy import func\n        # tags_with_counts = db.session.query(\n        # Tag, func.count(article_tags.c.article_id).label('articles_count')\n        # ).outerjoin(article_tags, Tag.id == article_tags.c.tag_id)\\\n        # .group_by(Tag.id).order_by(Tag.name).all()\n        #\n        # results = []\n        # for tag, count in tags_with_counts:\n        #     tag_data = tag.to_dict()\n        #     tag_data['articles_count'] = count\n        #     results.append(tag_data)\n        # return jsonify({\"success\": True, \"tags\": results})\n\n        # 暂时使用简单方式，后续可优化\n        return jsonify({\"success\": True, \"data\": [tag.to_dict(include_article_count=True) for tag in tags_query]})\n    except Exception as e:\n        logger.error(f\"获取标签列表API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags/stats', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_tags_stats():\n    \"\"\"获取标签统计信息API\"\"\"\n    try:\n        from sqlalchemy import func\n\n        # 基础统计\n        total_tags = Tag.query.count()\n\n        # 获取有文章的标签数量\n        used_tags = db.session.query(Tag.id).join(\n            Article.tags\n        ).distinct().count()\n\n        unused_tags = total_tags - used_tags\n\n        # 最常用标签\n        most_used_tags = db.session.query(\n            Tag.name,\n            func.count(Article.id).label('article_count')\n        ).join(Article.tags)\\\n         .group_by(Tag.id, Tag.name)\\\n         .order_by(func.count(Article.id).desc())\\\n         .limit(10).all()\n\n        most_used_tags_data = [\n            {'name': tag.name, 'article_count': tag.article_count}\n            for tag in most_used_tags\n        ]\n\n        # 标签使用分布\n        tag_usage_stats = db.session.query(\n            func.count(Article.id).label('article_count'),\n            func.count(Tag.id).label('tag_count')\n        ).select_from(Tag)\\\n         .outerjoin(Article.tags)\\\n         .group_by(Tag.id).all()\n\n        # 统计不同使用范围的标签数量\n        usage_ranges = {'1-5篇': 0, '6-10篇': 0, '11+篇': 0, '未使用': 0}\n        for stat in tag_usage_stats:\n            count = stat.article_count or 0\n            if count == 0:\n                usage_ranges['未使用'] += 1\n            elif count <= 5:\n                usage_ranges['1-5篇'] += 1\n            elif count <= 10:\n                usage_ranges['6-10篇'] += 1\n            else:\n                usage_ranges['11+篇'] += 1\n\n        tag_usage_distribution = [\n            {'range': range_name, 'count': count}\n            for range_name, count in usage_ranges.items()\n        ]\n\n        stats = {\n            'total_tags': total_tags,\n            'used_tags': used_tags,\n            'unused_tags': unused_tags,\n            'most_used_tags': most_used_tags_data,\n            'tag_usage_distribution': tag_usage_distribution\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取标签统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/tags', methods=['POST'])\n@login_required\n@staff_required\ndef api_create_tag():\n    \"\"\"创建新标签 (API)\"\"\"\n    data = request.get_json()\n    if not data or 'name' not in data or not data['name'].strip():\n        return jsonify({\"success\": False, \"error\": \"标签名不能为空\"}), 400\n    \n    name = data['name'].strip()\n    existing_tag = Tag.query.filter_by(name=name).first()\n    if existing_tag:\n        return jsonify({\"success\": False, \"error\": f\"标签 '{name}' 已存在\"}), 409 # 409 Conflict\n        \n    tag = Tag(name=name)\n    db.session.add(tag)\n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"标签创建成功\", \"tag\": tag.to_dict(include_article_count=True)}), 201\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"创建标签API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags/<int:tag_id>', methods=['PUT'])\n@login_required\n@staff_required\ndef api_update_tag(tag_id):\n    \"\"\"更新标签 (API)\"\"\"\n    tag = Tag.query.get_or_404(tag_id)\n    data = request.get_json()\n    \n    if not data or 'name' not in data or not data['name'].strip():\n        return jsonify({\"success\": False, \"error\": \"标签名不能为空\"}), 400\n        \n    new_name = data['name'].strip()\n    if new_name != tag.name:\n        existing_tag = Tag.query.filter(Tag.id != tag_id, Tag.name == new_name).first()\n        if existing_tag:\n            return jsonify({\"success\": False, \"error\": f\"标签 '{new_name}' 已存在\"}), 409 # 409 Conflict\n        tag.name = new_name\n        \n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"标签更新成功\", \"tag\": tag.to_dict(include_article_count=True)})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新标签API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags/<int:tag_id>', methods=['DELETE'])\n@login_required\n@staff_required\ndef api_delete_tag(tag_id):\n    \"\"\"删除标签 (API)\"\"\"\n    tag = Tag.query.get_or_404(tag_id)\n    try:\n        # SQLAlchemy 会自动处理 article_tags 关联表中的记录\n        db.session.delete(tag)\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"标签删除成功\"})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"删除标签API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags/batch_delete', methods=['POST'])\n@login_required\n@staff_required\ndef api_batch_delete_tags():\n    \"\"\"批量删除标签 (API)\"\"\"\n    data = request.get_json()\n    if not data or 'ids' not in data or not isinstance(data['ids'], list):\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据，需要一个包含ID列表的 'ids' 键。\"}), 400\n\n    ids_to_delete = data['ids']\n    if not ids_to_delete:\n        return jsonify({\"success\": False, \"error\": \"没有提供要删除的标签ID。\"}), 400\n\n    deleted_count = 0\n    errors = []\n\n    for tag_id in ids_to_delete:\n        try:\n            tag_id_int = int(tag_id) # 确保是整数\n            tag = Tag.query.get(tag_id_int)\n            if tag:\n                db.session.delete(tag)\n                deleted_count += 1\n            else:\n                errors.append(f\"ID {tag_id_int} 未找到对应的标签。\")\n        except ValueError:\n            errors.append(f\"无效的标签ID格式: '{tag_id}'\")\n        except Exception as e:\n            # 捕获删除单个标签时可能发生的其他DB相关错误\n            errors.append(f\"删除ID {tag_id} 时发生错误: {str(e)}\")\n            logger.error(f\"批量删除标签中，删除ID {tag_id} 失败: {str(e)}\")\n    \n    if errors and deleted_count > 0:\n        # 如果有部分成功部分失败，需要决定是否回滚\n        # 为了简单起见，我们先提交成功的，并报告错误\n        # 更严格的事务可以回滚所有： db.session.rollback(); return jsonify(...)\n        try:\n            db.session.commit()\n            message = f\"成功删除了 {deleted_count} 个标签。\"\n            if errors:\n                message += \" 但以下ID处理失败： \" + \"; \".join(errors)\n            return jsonify({\"success\": True, \"message\": message, \"errors\": errors if errors else None})\n        except Exception as e:\n            db.session.rollback()\n            logger.error(f\"批量删除标签提交事务时失败: {str(e)}\")\n            return jsonify({\"success\": False, \"error\": f\"提交批量删除操作时发生严重错误: {str(e)}\", \"details\": errors}), 500\n    elif errors: # 全部失败\n        db.session.rollback() # 确保没有任何更改被提交\n        return jsonify({\"success\": False, \"error\": \"所有选中的标签都删除失败。\", \"details\": errors}), 400\n    elif deleted_count > 0: # 全部成功\n        try:\n            db.session.commit()\n            return jsonify({\"success\": True, \"message\": f\"成功删除了 {deleted_count} 个标签。\"})\n        except Exception as e:\n            db.session.rollback()\n            logger.error(f\"批量删除标签提交事务时失败: {str(e)}\")\n            return jsonify({\"success\": False, \"error\": f\"提交批量删除操作时发生严重错误: {str(e)}\"}), 500\n    else: # ids_to_delete 为空或所有ID都无效但未引发异常（例如，全是未找到的ID）\n        return jsonify({\"success\": False, \"error\": \"没有有效的标签被删除。可能所有提供的ID都无效。\"}), 400\n\n# 添加占位 uploader_dashboard 路由\n@admin_bp.route('/uploader')\n@login_required\n@staff_required\ndef uploader_dashboard():\n    \"\"\"上传管理面板 (占位)\"\"\"\n    flash('上传管理功能正在建设中...', 'info')\n    return redirect(url_for('admin.dashboard'))\n\n# 添加占位 create_article 路由\n@admin_bp.route('/articles/create', methods=['GET', 'POST'])\n@login_required\n@staff_required\ndef create_article():\n    \"\"\"创建文章页面 (占位)\"\"\"\n    # flash('创建新文章功能正在建设中...', 'info') # 改为直接渲染模板\n    all_readers = Author.query.all()\n    all_tags = Tag.query.order_by(Tag.name).all() # 新增：获取所有标签\n    return render_template('admin/article_upload.html', \n                           article=None, \n                           all_readers=all_readers, \n                           all_tags=all_tags) # 新增：传递all_tags到模板\n\n# 添加占位 edit_article 路由\n@admin_bp.route('/articles/<int:article_id>/edit', methods=['GET', 'POST'])\n@login_required\n@staff_required\ndef edit_article(article_id):\n    \"\"\"编辑文章页面\"\"\"\n    article = Article.query.get_or_404(article_id)\n    all_readers = Author.query.order_by(Author.name).all() # 按名称排序\n    all_tags = Tag.query.order_by(Tag.name).all() # 按名称排序\n    # # 简单的表单处理示例 (如果需要POST请求)\n    # if request.method == 'POST':\n    #     # 处理表单提交逻辑\n    #     # article.title = request.form.get('title')\n    #     # db.session.commit()\n    #     # flash('文章更新成功!', 'success')\n    #     # return redirect(url_for('admin.articles'))\n    #     pass # 暂时不处理POST，只关注GET请求渲染\n\n    return render_template('admin/article_upload.html', article=article, all_readers=all_readers, all_tags=all_tags)\n\n# --- 新增API端点 ---\n\n@admin_bp.route('/api/articles', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_articles():\n    \"\"\"获取文章列表API (管理员) - 支持通过作者名称或ID、标签名称或ID进行查询\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        search = request.args.get('search', '')\n        status_filter = request.args.get('status', '')\n        author_filter = request.args.get('author', '')  # 支持作者名称或ID\n        tag_filter = request.args.get('tag', '')        # 支持标签名称或ID\n        \n        query = Article.query.order_by(Article.created_at.desc())\n        \n        # 搜索标题\n        if search:\n            query = query.filter(Article.title.ilike(f'%{search}%'))\n        \n        # 状态过滤\n        if status_filter:\n            query = query.filter(Article.status == status_filter)\n        \n        # 作者过滤（支持名称或ID）\n        if author_filter:\n            if author_filter.isdigit():\n                # 按ID过滤（reader_id）\n                query = query.filter(Article.reader_id == int(author_filter))\n            else:\n                # 按名称过滤，需要join Author表\n                query = query.join(Author, Article.reader_id == Author.id).filter(Author.name.ilike(f'%{author_filter}%'))\n        \n        # 标签过滤（支持名称或ID）\n        if tag_filter:\n            if tag_filter.isdigit():\n                # 按标签ID过滤\n                query = query.join(Article.tags).filter(Tag.id == int(tag_filter))\n            else:\n                # 按标签名称过滤\n                query = query.join(Article.tags).filter(Tag.name.ilike(f'%{tag_filter}%'))\n\n        pagination = query.paginate(page=page, per_page=per_page, error_out=False)\n        articles_data = []\n        \n        for article in pagination.items:\n            # 使用增强的to_dict方法，返回完整的作者和标签信息\n            article_dict = article.to_dict(\n                include_content=False,\n                include_premium_content=False,\n                include_author_details=True,\n                include_tag_details=True\n            )\n            articles_data.append(article_dict)\n        \n        return jsonify({\n            \"success\": True,\n            \"articles\": articles_data,\n            \"pagination\": {\n                \"total\": pagination.total,\n                \"page\": pagination.page,\n                \"per_page\": pagination.per_page,\n                \"pages\": pagination.pages,\n                \"has_next\": pagination.has_next,\n                \"has_prev\": pagination.has_prev\n            },\n            \"filters\": {\n                \"search\": search,\n                \"status\": status_filter,\n                \"author\": author_filter,\n                \"tag\": tag_filter\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取管理员文章列表失败: {str(e)}\", exc_info=True)\n        return jsonify({'success': False, 'error': f'获取文章列表失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/articles/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_articles_stats():\n    \"\"\"获取文章统计信息API\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 基础统计\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter(Article.status == 'published').count()\n        draft_articles = Article.query.filter(Article.status == 'draft').count()\n        pending_articles = Article.query.filter(Article.status == 'pending').count()\n\n        # 时间统计\n        today = datetime.now().date()\n        week_ago = today - timedelta(days=7)\n        month_ago = today - timedelta(days=30)\n\n        today_published = Article.query.filter(\n            Article.status == 'published',\n            Article.created_at >= today\n        ).count()\n\n        week_published = Article.query.filter(\n            Article.status == 'published',\n            Article.created_at >= week_ago\n        ).count()\n\n        month_published = Article.query.filter(\n            Article.status == 'published',\n            Article.created_at >= month_ago\n        ).count()\n\n        # 状态分布\n        status_distribution = {\n            'published': published_articles,\n            'draft': draft_articles,\n            'pending': pending_articles\n        }\n\n        # 作者分布\n        author_distribution = []\n        try:\n            from sqlalchemy import func\n            author_stats = db.session.query(\n                Author.name,\n                func.count(Article.id).label('count')\n            ).join(Article, Article.reader_id == Author.id)\\\n             .group_by(Author.id, Author.name)\\\n             .order_by(func.count(Article.id).desc())\\\n             .limit(10).all()\n\n            author_distribution = [\n                {'name': stat.name, 'count': stat.count}\n                for stat in author_stats\n            ]\n        except Exception as e:\n            logger.warning(f\"获取作者分布失败: {str(e)}\")\n\n        stats = {\n            'total_articles': total_articles,\n            'published_articles': published_articles,\n            'draft_articles': draft_articles,\n            'pending_articles': pending_articles,\n            'today_published': today_published,\n            'this_week_published': week_published,\n            'this_month_published': month_published,\n            'status_distribution': status_distribution,\n            'author_distribution': author_distribution\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取文章统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/articles', methods=['POST'])\n@login_required\n@staff_required\ndef api_create_article():\n    data = request.get_json()\n    if not data:\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据\"}), 400\n\n    title = data.get('title')\n    content = data.get('content')\n    status = data.get('status', 'draft')\n    reader_id_str = data.get('reader_id') \n    premium_content = data.get('premium_content')\n    content_type = data.get('content_type', 'normal')\n    price_str = data.get('price')\n    premium_price_str = data.get('premium_price')\n    cover_url = data.get('cover_url')\n\n    # 获取其他可选字段\n    publish_date_str = data.get('publish_date')\n    language_code = data.get('language_code')\n    tags_data = data.get('tags', []) # 可以是ID数组或名称数组\n    author_id = data.get('author_id') # 新增：支持直接传递author_id\n    premium_content_chinese = data.get('premium_content_chinese')  # 新增：高级翻译纯中文内容\n    original_id = data.get('original_id')  # 新增：YouTube视频ID\n\n    if not title:\n        return jsonify({\"success\": False, \"error\": \"文章标题不能为空\"}), 400\n\n    # 检查original_id是否重复\n    if original_id:\n        existing_article = Article.query.filter_by(original_id=original_id).first()\n        if existing_article:\n            return jsonify({\"success\": False, \"error\": f\"Article with original_id '{original_id}' already exists (ID: {existing_article.id})\"}), 409\n\n    try:\n        # 改进：支持塔罗师名称和ID\n        reader_id = None\n        if reader_id_str and str(reader_id_str).strip() not in [\"\", \"None\", \"null\"]:\n            # 尝试作为ID处理\n            if str(reader_id_str).isdigit():\n                reader_id = int(reader_id_str)\n            else:\n                # 作为名称处理，查找对应的塔罗师\n                author_by_name = Author.query.filter_by(name=str(reader_id_str).strip()).first()\n                if author_by_name:\n                    reader_id = author_by_name.id\n                    logger.info(f\"通过名称 '{reader_id_str}' 找到塔罗师ID: {reader_id}\")\n                else:\n                    logger.warning(f\"未找到名称为 '{reader_id_str}' 的塔罗师\")\n        \n        # 改进：支持传递author_id，如果没有则使用当前用户\n        final_author_id = author_id if author_id else current_user.id\n        \n        # 智能处理语言代码\n        final_language_code = None\n        if language_code and language_code.strip():\n            final_language_code = language_code\n            logger.info(f\"使用用户提供的语言代码: {language_code}\")\n        else:\n            # 自动检测语言\n            detected_language = auto_detect_language(f\"{title} {content}\")\n            if detected_language:\n                final_language_code = detected_language\n                logger.info(f\"自动检测语言为: {detected_language}\")\n        \n        new_article = Article(\n            title=title,\n            content=content,\n            status=status,\n            reader_id=None, # 先设为None，后续验证后再赋值\n            author_id=final_author_id,\n            premium_content=premium_content,\n            premium_content_chinese=premium_content_chinese,  # 新增：高级翻译纯中文内容\n            original_id=original_id,  # 新增：YouTube视频ID\n            content_type=content_type,\n            has_premium=bool(premium_content and premium_content.strip()),\n            cover_url=cover_url,\n            language_code=final_language_code\n        )\n\n        # 智能处理发布日期\n        if publish_date_str and publish_date_str.strip():\n            try:\n                new_article.publish_date = date.fromisoformat(publish_date_str)\n                logger.info(f\"使用用户提供的发布日期: {publish_date_str}\")\n            except ValueError:\n                logger.warning(f\"创建文章时，提供的发布日期格式无效: '{publish_date_str}'，将使用自动检测\")\n                new_article.publish_date = auto_detect_publish_date(content, title)\n        else:\n            # 自动检测发布日期\n            new_article.publish_date = auto_detect_publish_date(content, title)\n            logger.info(f\"自动设置发布日期为: {new_article.publish_date}\")\n\n        # 设置价格：如果提供了价格则使用，否则使用统一默认价格\n        if price_str is not None:\n            try:\n                new_article.price = int(float(price_str) * 100)\n            except ValueError:\n                return jsonify({\"success\": False, \"error\": \"基础内容价格格式无效\"}), 400\n        else:\n            # 使用统一的默认价格：¥12.00\n            new_article.price = 1200\n\n        if premium_price_str is not None:\n            try:\n                new_article.premium_price = int(float(premium_price_str) * 100)\n            except ValueError:\n                return jsonify({\"success\": False, \"error\": \"高级内容价格格式无效\"}), 400\n        else:\n            # 使用统一的默认价格：¥20.00\n            new_article.premium_price = 2000\n        \n        # 验证并设置塔罗师ID\n        if reader_id:\n            author_exists = Author.query.get(reader_id)\n            if author_exists:\n                new_article.reader_id = reader_id\n            else:\n                logger.warning(f\"创建文章时，尝试关联无效的塔罗师ID: {reader_id} 到新文章 '{title}'\")\n\n        # 改进：处理标签关联 - 支持ID和名称混合\n        if tags_data:\n            for tag_item in tags_data:\n                tag = None\n                \n                # 判断是ID还是名称\n                if isinstance(tag_item, int) or (isinstance(tag_item, str) and tag_item.isdigit()):\n                    # 作为ID处理\n                    tag_id = int(tag_item)\n                    tag = Tag.query.get(tag_id)\n                    if not tag:\n                        logger.warning(f\"创建文章 '{title}' 时，标签ID {tag_id} 不存在\")\n                elif isinstance(tag_item, str):\n                    # 作为名称处理\n                    tag_name = tag_item.strip()\n                    if tag_name:\n                        tag = Tag.query.filter_by(name=tag_name).first()\n                        if not tag:\n                            # 自动创建新标签\n                            tag = Tag(name=tag_name)\n                            db.session.add(tag)\n                            logger.info(f\"自动创建新标签: '{tag_name}'\")\n                \n                if tag and tag not in new_article.tags:\n                    new_article.tags.append(tag)\n\n        db.session.add(new_article)\n        db.session.flush() # 获取 new_article.id 用于标签关联\n\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"文章创建成功\", \"article\": new_article.to_dict(include_content=False, include_premium_content=True, include_author_details=False, include_tag_details=True)}), 201\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"创建文章API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"创建文章失败: {str(e)}\"}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>', methods=['PUT'])\n@login_required\n@staff_required\ndef api_update_article(article_id):\n    article = Article.query.get_or_404(article_id)\n    data = request.get_json()\n\n    if not data:\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据\"}), 400\n\n    # 根据前端JS的逻辑，它可能会为不同表单的保存发送不同的payload\n    # 例如，一个payload只包含title和content，另一个只包含status和reader_id\n    \n    if 'title' in data:\n        article.title = data.get('title', article.title)\n    if 'content' in data:\n        article.content = data.get('content', article.content)\n\n    if 'premium_content' in data:\n        article.premium_content = data.get('premium_content', article.premium_content)\n        article.has_premium = bool(article.premium_content and article.premium_content.strip())\n\n    if 'status' in data:\n        article.status = data.get('status', article.status)\n    \n    # 改进：支持塔罗师名称和ID\n    if 'reader_id' in data:\n        reader_id_str = data.get('reader_id')\n        if reader_id_str is None or reader_id_str == \"\" or str(reader_id_str).lower() == 'none':\n            article.reader_id = None\n        else:\n            reader_id_val = None\n            \n            # 判断是ID还是名称\n            if str(reader_id_str).isdigit():\n                # 作为ID处理\n                reader_id_val = int(reader_id_str)\n                if reader_id_val == 0:\n                     article.reader_id = None\n                else:\n                    author_exists = Author.query.get(reader_id_val)\n                    if author_exists:\n                        article.reader_id = reader_id_val\n                    else:\n                        logger.warning(f\"更新文章 {article_id} 时，塔罗师ID {reader_id_val} 不存在\")\n                        article.reader_id = None\n            else:\n                # 作为名称处理\n                author_by_name = Author.query.filter_by(name=str(reader_id_str).strip()).first()\n                if author_by_name:\n                    article.reader_id = author_by_name.id\n                    logger.info(f\"更新文章 {article_id} 时，通过名称 '{reader_id_str}' 找到塔罗师ID: {author_by_name.id}\")\n                else:\n                    logger.warning(f\"更新文章 {article_id} 时，未找到名称为 '{reader_id_str}' 的塔罗师\")\n                    article.reader_id = None\n    \n    if 'content_type' in data:\n        article.content_type = data.get('content_type', article.content_type)\n\n    if 'price' in data:\n        price_str = data.get('price')\n        if price_str is not None:\n            try:\n                 article.price = int(float(price_str) * 100)\n            except ValueError:\n                return jsonify({\"success\": False, \"error\": \"基础内容价格格式无效\"}), 400\n    \n    if 'premium_price' in data:\n        premium_price_str = data.get('premium_price')\n        if premium_price_str is not None:\n            try:\n                 article.premium_price = int(float(premium_price_str) * 100)\n            except ValueError:\n                return jsonify({\"success\": False, \"error\": \"高级内容价格格式无效\"}), 400\n            \n    if 'cover_url' in data:\n        article.cover_url = data.get('cover_url', article.cover_url)\n        # 如果URL被清空，也清空本地上传的封面记录（如果模型中有相关字段）\n        # if not article.cover_url or not article.cover_url.strip():\n        #     article.cover_image = None \n            \n    # 智能处理发布日期\n    if 'publish_date' in data:\n        publish_date_str = data.get('publish_date')\n        if publish_date_str and publish_date_str.strip():\n            try:\n                article.publish_date = date.fromisoformat(publish_date_str)\n                logger.info(f\"更新文章 {article_id}，使用用户提供的发布日期: {publish_date_str}\")\n            except ValueError:\n                logger.warning(f\"更新文章 {article_id} 时，提供的发布日期格式无效: '{publish_date_str}'，将使用自动检测\")\n                article.publish_date = auto_detect_publish_date(data.get('content', article.content), data.get('title', article.title))\n        else:\n            # 如果传来空字符串，进行自动检测而不是清空\n            if publish_date_str == \"\":\n                article.publish_date = auto_detect_publish_date(data.get('content', article.content), data.get('title', article.title))\n                logger.info(f\"更新文章 {article_id}，自动设置发布日期为: {article.publish_date}\")\n            else:\n                article.publish_date = None # 只有明确传null才清空\n\n    # 智能处理语言代码\n    if 'language_code' in data:\n        language_code = data.get('language_code')\n        if language_code and language_code.strip():\n            article.language_code = language_code\n            logger.info(f\"更新文章 {article_id}，使用用户提供的语言代码: {language_code}\")\n        else:\n            # 如果没有提供语言代码，自动检测\n            content_for_detection = data.get('content', article.content)\n            title_for_detection = data.get('title', article.title)\n            detected_language = auto_detect_language(f\"{title_for_detection} {content_for_detection}\")\n            if detected_language:\n                article.language_code = detected_language\n                logger.info(f\"更新文章 {article_id}，自动检测语言为: {detected_language}\")\n            else:\n                article.language_code = None\n\n    # 改进：处理标签 - 支持ID和名称混合\n    if 'tags' in data:\n        tags_data = data.get('tags', [])\n        article.tags.clear() # 先清除旧的标签关联\n        if tags_data:\n            for tag_item in tags_data:\n                tag = None\n                \n                # 判断是ID还是名称\n                if isinstance(tag_item, int) or (isinstance(tag_item, str) and tag_item.isdigit()):\n                    # 作为ID处理\n                    tag_id = int(tag_item)\n                    tag = Tag.query.get(tag_id)\n                    if not tag:\n                        logger.warning(f\"更新文章 {article_id} 时，标签ID {tag_id} 不存在\")\n                elif isinstance(tag_item, str):\n                    # 作为名称处理\n                    tag_name = tag_item.strip()\n                    if tag_name:\n                        tag = Tag.query.filter_by(name=tag_name).first()\n                        if not tag:\n                            # 自动创建新标签\n                            tag = Tag(name=tag_name)\n                            db.session.add(tag)\n                            logger.info(f\"更新文章 {article_id} 时，自动创建新标签: '{tag_name}'\")\n                \n                if tag and tag not in article.tags:\n                        article.tags.append(tag)\n            \n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"文章更新成功\", \"article\": article.to_dict(include_content=True, include_premium_content=True, include_author_details=True, include_tag_details=True)})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新文章 {article_id} API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"更新文章失败: {str(e)}\"}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/premium', methods=['PUT'])\n@login_required\n@staff_required\ndef update_article_premium_content(article_id):\n    \"\"\"更新文章高级内容\"\"\"\n    logger.info(f\"收到更新文章高级内容请求，文章ID: {article_id}\")\n    article = Article.query.get_or_404(article_id)\n    data = request.get_json()\n    logger.info(f\"请求数据: {data}\")\n\n    if not data:\n        logger.warning(f\"更新文章 {article_id} 高级内容失败: 无效的请求数据\")\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据\"}), 400\n\n    # 更新高级内容\n    premium_content = data.get('premium_content')\n    logger.info(f\"更新文章 {article_id} 高级内容，内容长度: {len(str(premium_content)) if premium_content else 0}\")\n    \n    # 保存原始值用于对比\n    original_premium_content = article.premium_content\n    original_has_premium = article.has_premium\n    \n    article.premium_content = premium_content\n    article.has_premium = bool(premium_content and premium_content.strip())\n    \n    # 记录变化\n    logger.info(f\"高级内容变化: '{original_premium_content or 'None'}' -> '{premium_content or 'None'}'\")\n    logger.info(f\"has_premium变化: {original_has_premium} -> {article.has_premium}\")\n\n    try:\n        db.session.commit()\n        logger.info(f\"更新文章 {article_id} 高级内容成功\")\n        result = article.to_dict(include_content=True, include_premium_content=True, include_author_details=True, include_tag_details=True)\n        logger.info(f\"返回数据: has_premium={result.get('has_premium')}, premium_content长度={len(result.get('premium_content', '')) if result.get('premium_content') else 0}\")\n        return jsonify({\"success\": True, \"message\": \"高级内容更新成功\", \"article\": result})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新文章 {article_id} 高级内容失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"更新高级内容失败: {str(e)}\"}), 500\n\n# --- 结束高级内容API ---\n\n@admin_bp.route('/api/articles/<int:article_id>/price', methods=['PUT'])\n@login_required\n@finance_required\ndef update_article_price(article_id):\n    \"\"\"更新文章价格设置\"\"\"\n    article = Article.query.get_or_404(article_id)\n    data = request.get_json()\n\n    if not data:\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据\"}), 400\n\n    if 'price' in data:\n        try:\n            article.price = int(data['price'])\n        except (ValueError, TypeError):\n            return jsonify({\"success\": False, \"error\": \"基础内容价格格式无效\"}), 400\n\n    if 'premium_price' in data:\n        try:\n            article.premium_price = int(data['premium_price'])\n        except (ValueError, TypeError):\n            return jsonify({\"success\": False, \"error\": \"高级内容价格格式无效\"}), 400\n\n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"价格设置更新成功\", \"article\": article.to_dict(include_author_details=True, include_tag_details=True)})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新文章 {article_id} 价格失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"更新价格失败: {str(e)}\"}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/cover-url', methods=['PUT'])\n@login_required\n@staff_required\ndef update_article_cover_url(article_id):\n    \"\"\"更新文章封面URL\"\"\"\n    article = Article.query.get_or_404(article_id)\n    data = request.get_json()\n\n    if not data or 'cover_url' not in data:\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据，需要提供cover_url\"}), 400\n\n    article.cover_url = data['cover_url']\n\n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"封面URL更新成功\", \"article\": article.to_dict(include_author_details=True, include_tag_details=True)})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新文章 {article_id} 封面URL失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"更新封面URL失败: {str(e)}\"}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/cover', methods=['DELETE'])\n@login_required\n@staff_required\ndef delete_article_cover(article_id):\n    \"\"\"删除文章封面\"\"\"\n    article = Article.query.get_or_404(article_id)\n\n    # 清空封面相关字段\n    article.cover_url = None\n    # 如果有本地存储的封面图片，这里也需要清空\n    # article.cover_image = None\n\n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"封面已移除\"})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"删除文章 {article_id} 封面失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"移除封面失败: {str(e)}\"}), 500\n\n# --- 塔罗师管理 (Authors) ---\n@admin_bp.route('/authors')\n@login_required\n@admin_required\ndef authors():\n    \"\"\"塔罗师管理页面\"\"\"\n    return render_template('admin/authors.html')\n\n@admin_bp.route('/api/authors', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_authors():\n    \"\"\"获取塔罗师列表API - 支持分页和搜索\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        search = request.args.get('search', '')\n\n        query = Author.query\n\n        # 搜索过滤\n        if search:\n            query = query.filter(Author.name.ilike(f'%{search}%'))\n\n        # 分页查询\n        pagination = query.order_by(Author.name).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        authors_data = []\n        for author in pagination.items:\n            author_dict = author.to_dict(include_article_count=True)\n            authors_data.append(author_dict)\n\n        return jsonify({\n            \"success\": True,\n            \"data\": {\n                \"data\": authors_data,\n                \"pagination\": {\n                    \"page\": pagination.page,\n                    \"pages\": pagination.pages,\n                    \"per_page\": pagination.per_page,\n                    \"total\": pagination.total,\n                    \"has_prev\": pagination.has_prev,\n                    \"has_next\": pagination.has_next\n                }\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取塔罗师列表API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/authors', methods=['POST'])\n@login_required\n@admin_required\ndef api_create_author():\n    \"\"\"创建塔罗师API\"\"\"\n    try:\n        data = request.get_json()\n        if not data or 'name' not in data:\n            return jsonify({'success': False, 'error': '缺少必要参数name'}), 400\n\n        name = data['name'].strip()\n        if not name:\n            return jsonify({'success': False, 'error': '塔罗师名称不能为空'}), 400\n\n        # 检查是否已存在同名塔罗师\n        existing_author = Author.query.filter_by(name=name).first()\n        if existing_author:\n            return jsonify({'success': False, 'error': f'塔罗师 \"{name}\" 已存在'}), 409\n\n        # 创建新塔罗师\n        author = Author(\n            name=name,\n            description=data.get('description', ''),\n            specialty=data.get('specialty', '塔罗占卜'),\n            experience_years=data.get('experience_years', 0),\n            contact_info=data.get('contact_info', ''),\n            is_active=data.get('is_active', True)\n        )\n\n        db.session.add(author)\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '塔罗师创建成功',\n            'data': author.to_dict(include_article_count=True)\n        }), 201\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"创建塔罗师API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/<int:author_id>', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_author(author_id):\n    \"\"\"获取塔罗师详情API\"\"\"\n    try:\n        author = Author.query.get_or_404(author_id)\n        return jsonify({\n            'success': True,\n            'data': author.to_dict(include_article_count=True)\n        })\n    except Exception as e:\n        logger.error(f\"获取塔罗师详情API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/<int:author_id>', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_author(author_id):\n    \"\"\"更新塔罗师API\"\"\"\n    try:\n        author = Author.query.get_or_404(author_id)\n        data = request.get_json()\n\n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n\n        # 更新名称\n        if 'name' in data:\n            new_name = data['name'].strip()\n            if not new_name:\n                return jsonify({'success': False, 'error': '塔罗师名称不能为空'}), 400\n\n            # 检查名称是否与其他塔罗师冲突\n            if new_name != author.name:\n                existing_author = Author.query.filter(\n                    Author.id != author_id,\n                    Author.name == new_name\n                ).first()\n                if existing_author:\n                    return jsonify({'success': False, 'error': f'塔罗师 \"{new_name}\" 已存在'}), 409\n\n            author.name = new_name\n\n        # 更新其他字段\n        if 'description' in data:\n            author.description = data['description']\n        if 'specialty' in data:\n            author.specialty = data['specialty']\n        if 'experience_years' in data:\n            author.experience_years = data['experience_years']\n        if 'contact_info' in data:\n            author.contact_info = data['contact_info']\n        if 'is_active' in data:\n            author.is_active = data['is_active']\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '塔罗师更新成功',\n            'data': author.to_dict(include_article_count=True)\n        })\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新塔罗师API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/<int:author_id>', methods=['DELETE'])\n@login_required\n@admin_required\ndef api_delete_author(author_id):\n    \"\"\"删除塔罗师API\"\"\"\n    try:\n        author = Author.query.get_or_404(author_id)\n\n        # 检查是否有关联的文章\n        if hasattr(author, 'articles') and author.articles:\n            return jsonify({\n                'success': False,\n                'error': f'无法删除塔罗师 \"{author.name}\"，因为还有 {len(author.articles)} 篇关联文章'\n            }), 400\n\n        db.session.delete(author)\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '塔罗师删除成功'\n        })\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"删除塔罗师API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/batch_delete', methods=['POST'])\n@login_required\n@admin_required\ndef api_batch_delete_authors():\n    \"\"\"批量删除塔罗师API\"\"\"\n    try:\n        data = request.get_json()\n        if not data or 'ids' not in data or not isinstance(data['ids'], list):\n            return jsonify({'success': False, 'error': '无效的请求数据，需要一个包含ID列表的 \"ids\" 键'}), 400\n\n        ids_to_delete = data['ids']\n        if not ids_to_delete:\n            return jsonify({'success': False, 'error': '没有提供要删除的塔罗师ID'}), 400\n\n        # 查找要删除的塔罗师\n        authors_to_delete = Author.query.filter(Author.id.in_(ids_to_delete)).all()\n\n        if not authors_to_delete:\n            return jsonify({'success': False, 'error': '没有找到要删除的塔罗师'}), 404\n\n        # 检查是否有关联的文章\n        authors_with_articles = []\n        for author in authors_to_delete:\n            if hasattr(author, 'articles') and author.articles:\n                authors_with_articles.append(f'{author.name}({len(author.articles)}篇文章)')\n\n        if authors_with_articles:\n            return jsonify({\n                'success': False,\n                'error': f'以下塔罗师还有关联文章，无法删除：{\", \".join(authors_with_articles)}'\n            }), 400\n\n        # 执行删除\n        for author in authors_to_delete:\n            db.session.delete(author)\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': f'成功删除 {len(authors_to_delete)} 个塔罗师'\n        })\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"批量删除塔罗师API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_authors_stats():\n    \"\"\"获取塔罗师统计信息API\"\"\"\n    try:\n        from sqlalchemy import func\n\n        # 基础统计\n        total_authors = Author.query.count()\n        featured_authors = Author.query.filter(Author.featured == True).count()\n\n        # 有文章的塔罗师数量\n        active_authors = db.session.query(Author.id).join(\n            Article, Article.reader_id == Author.id\n        ).distinct().count()\n\n        # 总文章数\n        total_articles_by_authors = Article.query.filter(\n            Article.reader_id.isnot(None)\n        ).count()\n\n        # 平均每个塔罗师的文章数\n        avg_articles_per_author = (\n            total_articles_by_authors / total_authors\n            if total_authors > 0 else 0\n        )\n\n        # 顶级塔罗师（按文章数排序）\n        top_authors = db.session.query(\n            Author.name,\n            func.count(Article.id).label('article_count'),\n            func.sum(Article.view_count).label('total_views')\n        ).join(Article, Article.reader_id == Author.id)\\\n         .group_by(Author.id, Author.name)\\\n         .order_by(func.count(Article.id).desc())\\\n         .limit(10).all()\n\n        top_authors_data = []\n        for author in top_authors:\n            top_authors_data.append({\n                'name': author.name,\n                'article_count': author.article_count,\n                'total_views': author.total_views or 0\n            })\n\n        # 塔罗师表现数据\n        author_performance = []\n        for author in top_authors:\n            # 计算平均评分（这里使用模拟数据，实际应该从评分表获取）\n            rating = 4.5 + (hash(author.name) % 6) / 10  # 模拟评分 4.5-5.0\n            author_performance.append({\n                'name': author.name,\n                'articles': author.article_count,\n                'views': author.total_views or 0,\n                'rating': round(rating, 1)\n            })\n\n        stats = {\n            'total_authors': total_authors,\n            'active_authors': active_authors,\n            'featured_authors': featured_authors,\n            'total_articles_by_authors': total_articles_by_authors,\n            'avg_articles_per_author': round(avg_articles_per_author, 1),\n            'top_authors': top_authors_data,\n            'author_performance': author_performance\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取塔罗师统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n\n\n\n\n\n\n@admin_bp.route('/api/article-buyers/<int:article_id>', methods=['GET'])\n@login_required\n@admin_required\ndef get_article_buyers(article_id):\n    \"\"\"获取文章购买者列表API\"\"\"\n    try:\n        # 获取文章信息\n        article = Article.query.get_or_404(article_id)\n\n        # 获取购买记录 - 只查询已完成的购买\n        purchases = Purchase.query.filter_by(\n            article_id=article_id,\n            status='completed'\n        ).all()\n\n        # 如果没有购买记录，返回空列表\n        if not purchases:\n            return jsonify({\n                'success': True,\n                'article': {\n                    'id': article.id,\n                    'title': article.title,\n                    'status': article.status,\n                    'created_at': article.created_at.isoformat() if article.created_at else None\n                },\n                'buyers': [],\n                'total_count': 0\n            })\n\n        # 整理购买者信息 - 基于优化后的Purchase表\n        buyers = []\n        for purchase in purchases:\n            # 获取用户信息\n            user = User.query.get(purchase.user_id) if purchase.user_id else None\n\n            # 获取支付摘要（兼容当前数据库结构）\n            try:\n                payment_summary = purchase.get_payment_summary()\n            except:\n                # 如果新字段不存在，使用默认值\n                payment_summary = {\n                    'composition': 'unknown',\n                    'cash': {'amount': 0, 'currency': 'CNY', 'percentage': 0},\n                    'points': {'amount': 0, 'value': 0, 'rate': 0, 'percentage': 0},\n                    'quota': {'amount': 0, 'type': '', 'value': 0, 'percentage': 0},\n                    'total': {'value': 0, 'paid': 0, 'saved': 0}\n                }\n\n            # 查找积分使用记录（用于获取余额变化信息）\n            points_record = None\n            points_balance_before = None\n            points_balance_after = None\n\n            if purchase.user_id and purchase.article_id and purchase.points_used > 0:\n                points_record = PointsRecord.query.filter_by(\n                    user_id=purchase.user_id,\n                    type='used',\n                    order_id=f'ART{purchase.article_id}'\n                ).first()\n\n                if points_record and hasattr(points_record, 'balance_before'):\n                    points_balance_before = points_record.balance_before\n                    points_balance_after = points_record.balance_after\n\n            # 获取用户当前财务状态\n            user_finance = UserFinance.query.get(purchase.user_id) if purchase.user_id else None\n\n            # 安全获取字段值（兼容当前数据库结构）\n            def safe_get_attr(obj, attr, default=0):\n                try:\n                    return getattr(obj, attr, default)\n                except:\n                    return default\n\n            # 计算基本信息\n            cash_amount = safe_get_attr(purchase, 'cash_amount', purchase.amount or 0)\n            points_used = safe_get_attr(purchase, 'points_used', 0)\n            points_value = safe_get_attr(purchase, 'points_value', 0)\n            quota_used = safe_get_attr(purchase, 'quota_used', 0)\n            quota_type = safe_get_attr(purchase, 'quota_type', purchase.content_type)\n            quota_value = safe_get_attr(purchase, 'quota_value', 0)\n            total_value = safe_get_attr(purchase, 'total_value', cash_amount + points_value + quota_value)\n            total_amount = safe_get_attr(purchase, 'total_amount', cash_amount)\n            payment_composition = safe_get_attr(purchase, 'payment_composition', 'unknown')\n\n            # 如果没有配额价值但有配额使用，计算配额价值\n            if quota_value == 0 and quota_used > 0:\n                if purchase.content_type == 'premium':\n                    quota_value = quota_used * 2999  # 高级内容29.99元\n                else:\n                    quota_value = quota_used * 999   # 基础内容9.99元\n                total_value = cash_amount + points_value + quota_value\n\n            # 确定支付构成和用户状态\n            if payment_composition == 'unknown':\n                if quota_used > 0 and cash_amount == 0 and points_used == 0:\n                    # 纯配额支付\n                    if quota_type == 'premium':\n                        payment_composition = '高级配额'\n                    else:\n                        payment_composition = '基础配额'\n                elif points_used > 0 and quota_used == 0 and cash_amount == 0:\n                    payment_composition = '积分支付'\n                elif cash_amount > 0 and quota_used == 0 and points_used == 0:\n                    payment_composition = '现金支付'\n                elif quota_used > 0 and (cash_amount > 0 or points_used > 0):\n                    payment_composition = '混合支付'\n                else:\n                    payment_composition = '自动赠送'\n\n            # 确定获得方式\n            acquisition_method = '购买获得'\n            if quota_used > 0 and cash_amount == 0 and points_used == 0:\n                acquisition_method = '配额消耗'\n            elif user and hasattr(user, 'is_admin') and user.is_admin:\n                acquisition_method = '管理员权限'\n\n            # 计算节省金额（仅在有实际支付时显示）\n            total_saved = points_value + quota_value\n            savings_percentage = round(total_saved / total_value * 100, 2) if total_value > 0 else 0\n\n            buyer = {\n                # 基础信息\n                'purchase_id': purchase.id,\n                'user_id': purchase.user_id,\n                'username': user.username if user else '未知用户',\n                'email': user.email if user and user.email else '未提供',\n                'content_type': purchase.content_type,\n                'purchase_time': purchase.purchase_date.isoformat() if purchase.purchase_date else None,\n\n                # 支付信息（完整且准确）\n                'payment_method': purchase.payment_method or '未知',\n                'transaction_id': purchase.transaction_id or '',\n                'payment_composition': payment_composition,\n\n                # 现金支付详情\n                'cash_amount': cash_amount,\n                'cash_currency': safe_get_attr(purchase, 'cash_currency', 'CNY'),\n                'cash_percentage': round(cash_amount / total_value * 100, 2) if total_value > 0 else 0,\n\n                # 积分支付详情\n                'points_used': points_used,\n                'points_value': points_value,\n                'points_rate': float(safe_get_attr(purchase, 'points_rate', 0.01)),\n                'points_percentage': round(points_value / total_value * 100, 2) if total_value > 0 else 0,\n                'points_balance_before': points_balance_before,\n                'points_balance_after': points_balance_after,\n                'points_record_id': points_record.id if points_record else None,\n\n                # 配额支付详情\n                'quota_used': quota_used,\n                'quota_type': quota_type,\n                'quota_value': quota_value,\n                'quota_percentage': round(quota_value / total_value * 100, 2) if total_value > 0 else 0,\n                'exchange_used': False,  # Purchase表中没有这个字段\n                'quota_record_id': None,  # 可以后续从QuotaUsageRecord查找\n\n                # 总计信息\n                'total_value': total_value,\n                'total_paid': total_amount,\n                'total_saved': total_saved,\n                'savings_percentage': savings_percentage,\n\n                # 支付状态\n                'is_free_purchase': total_amount == 0 and (points_used > 0 or quota_used > 0),\n                'is_mixed_payment': payment_composition == 'mixed',\n\n                # 用户状态\n                'vip_level_at_purchase': user_finance.vip_level if user_finance else 0,\n\n                # 向后兼容字段\n                'amount_paid': total_amount / 100.0,  # 转换为元\n                'price': total_amount / 100.0,  # 前端期望的字段\n                'cost_type': payment_composition,\n                'cost_amount': total_saved if total_amount == 0 else total_amount,\n\n                # 完整的支付摘要\n                'payment_summary': payment_summary,\n\n                # 调试信息\n                'debug_info': {\n                    'purchase_amount': purchase.amount,\n                    'calculated_total_value': total_value,\n                    'calculated_total_amount': total_amount,\n                    'purchase_status': purchase.status,\n                    'has_points_record': points_record is not None,\n                    'user_finance_exists': user_finance is not None,\n                    'has_new_fields': hasattr(purchase, 'cash_amount')\n                }\n            }\n            buyers.append(buyer)\n\n        return jsonify({\n            'success': True,\n            'article': {\n                'id': article.id,\n                'title': article.title,\n                'status': article.status,\n                'created_at': article.created_at.isoformat() if article.created_at else None\n            },\n            'buyers': buyers,\n            'total_count': len(buyers)\n        })\n\n    except Exception as e:\n        logger.error(f\"获取文章购买者列表失败: {str(e)}\", exc_info=True)\n        return jsonify({\n            'success': False,\n            'error': f'获取文章购买者列表失败: {str(e)}'\n        }), 500\n\n@admin_bp.route('/api/debug-article-records/<int:article_id>', methods=['GET'])\n@login_required\n@admin_required\ndef debug_article_records(article_id):\n    \"\"\"调试API - 查看文章相关的所有记录\"\"\"\n    try:\n        # 获取所有购买记录\n        purchases = Purchase.query.filter_by(article_id=article_id).all()\n\n        debug_data = {\n            'article_id': article_id,\n            'purchases': [],\n            'all_points_records': [],\n            'all_quota_records': [],\n            'all_consumption_records': []\n        }\n\n        # 购买记录\n        for purchase in purchases:\n            debug_data['purchases'].append({\n                'id': purchase.id,\n                'user_id': purchase.user_id,\n                'amount': purchase.amount,\n                'content_type': purchase.content_type,\n                'purchase_date': purchase.purchase_date.isoformat() if purchase.purchase_date else None,\n                'status': purchase.status,\n                'quota_used': getattr(purchase, 'quota_used', 'N/A'),\n                'quota_type': getattr(purchase, 'quota_type', 'N/A')\n            })\n\n        # 获取所有用户的积分记录（包含这篇文章）\n        user_ids = [p.user_id for p in purchases]\n        if user_ids:\n            points_records = PointsRecord.query.filter(\n                PointsRecord.user_id.in_(user_ids),\n                PointsRecord.order_id.like(f'%{article_id}%')\n            ).all()\n\n            for record in points_records:\n                debug_data['all_points_records'].append({\n                    'id': record.id,\n                    'user_id': record.user_id,\n                    'amount': record.amount,\n                    'type': record.type,\n                    'order_id': record.order_id,\n                    'created_at': record.created_at.isoformat() if record.created_at else None\n                })\n\n        # 获取所有配额记录\n        if user_ids:\n            quota_records = QuotaUsageRecord.query.filter(\n                QuotaUsageRecord.user_id.in_(user_ids),\n                QuotaUsageRecord.article_id == article_id\n            ).all()\n\n            for record in quota_records:\n                debug_data['all_quota_records'].append({\n                    'id': record.id,\n                    'user_id': record.user_id,\n                    'article_id': record.article_id,\n                    'quota_type': record.quota_type,\n                    'amount_used': record.amount_used,\n                    'exchange_used': record.exchange_used,\n                    'vip_level_at_time': record.vip_level_at_time,\n                    'created_at': record.created_at.isoformat() if record.created_at else None\n                })\n\n        # 获取所有消费记录\n        if user_ids:\n            consumption_records = UserConsumptionRecord.query.filter(\n                UserConsumptionRecord.user_id.in_(user_ids),\n                UserConsumptionRecord.article_id == article_id\n            ).all()\n\n            for record in consumption_records:\n                debug_data['all_consumption_records'].append({\n                    'id': record.id,\n                    'user_id': record.user_id,\n                    'article_id': record.article_id,\n                    'content_type': record.content_type,\n                    'cost_type': record.cost_type,\n                    'cost_amount': record.cost_amount,\n                    'created_at': record.created_at.isoformat() if record.created_at else None\n                })\n\n        return jsonify({\n            'success': True,\n            'debug_data': debug_data\n        })\n\n    except Exception as e:\n        logger.error(f\"调试API失败: {str(e)}\", exc_info=True)\n        return jsonify({\n            'success': False,\n            'error': f'调试API失败: {str(e)}'\n        }), 500\n\n@admin_bp.route('/api/articles/batch_delete', methods=['POST'])\n@login_required\n@admin_required\ndef api_batch_delete_articles():\n    \"\"\"批量删除文章 (API)\"\"\"\n    data = request.get_json()\n    if not data or 'ids' not in data or not isinstance(data['ids'], list):\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据，需要一个包含ID列表的 'ids' 键。\"}), 400\n\n    ids_to_delete = data['ids']\n    if not ids_to_delete:\n        return jsonify({\"success\": False, \"error\": \"没有提供要删除的文章ID。\"}), 400\n\n    deleted_count = 0\n    errors = []\n\n    for article_id_str in ids_to_delete:\n        try:\n            article_id = int(article_id_str) # 确保是整数\n            article = Article.query.get(article_id)\n            if article:\n                # 这里可以集成更复杂的删除逻辑，比如调用一个负责删除文章及其关联数据（如图片）的函数\n                # 目前，我们直接删除文章对象，与 backup_delete_article 的核心逻辑一致\n                db.session.delete(article)\n                deleted_count += 1\n                logger.info(f\"批量删除：文章 ID {article_id} 已标记为删除。\")\n            else:\n                errors.append(f\"ID {article_id} 未找到对应的文章。\")\n                logger.warning(f\"批量删除：未找到文章 ID {article_id}。\")\n        except ValueError:\n            errors.append(f\"无效的文章ID格式: '{article_id_str}'\")\n            logger.warning(f\"批量删除：无效的文章ID格式 '{article_id_str}'\")\n        except Exception as e:\n            # 捕获删除单个文章时可能发生的其他DB相关错误\n            db.session.rollback() # 单个错误时，回滚当前更改，但继续处理下一个\n            errors.append(f\"删除ID {article_id_str} 时发生数据库错误: {str(e)}\")\n            logger.error(f\"批量删除文章中，删除ID {article_id_str} 失败: {str(e)}\")\n    \n    if not errors and deleted_count > 0:\n        # 全部成功\n        try:\n            db.session.commit()\n            logger.info(f\"批量删除：成功删除 {deleted_count} 篇文章并已提交事务。\")\n            return jsonify({\"success\": True, \"message\": f\"成功删除了 {deleted_count} 篇文章。\"})\n        except Exception as e:\n            db.session.rollback()\n            logger.error(f\"批量删除文章提交事务时失败: {str(e)}\")\n            return jsonify({\"success\": False, \"error\": f\"提交批量删除操作时发生严重错误: {str(e)}\"}), 500\n    elif errors:\n        # 如果有任何错误，我们选择回滚所有本次批量操作中已标记删除的内容，\n        # 以保证原子性，或者至少不提交部分成功的删除。\n        # 注意：上面循环中对单个错误的 rollback() 可能已经回滚了部分。\n        # 这里确保最终的 commit 不会发生，或者如果需要，显式地回滚整个事务。\n        # 根据具体需求，也可以选择提交已成功的，并报告失败的。\n        # 当前策略：如果任何一个失败，则不提交任何删除，并报告所有错误。\n        db.session.rollback() # 确保整体回滚\n        message = f\"批量删除操作中有 {len(errors)} 个错误，所有更改已回滚。\"\n        logger.warning(f\"批量删除文章操作失败，错误数量: {len(errors)}. 详情: {'; '.join(errors)}\")\n        return jsonify({\"success\": False, \"message\": message, \"error\": \"部分或全部文章删除失败。\", \"details\": errors}), 400 # 或 207 Multi-Status\n    elif deleted_count == 0 and not errors:\n         # 没有选中任何有效的、存在的文章\n        logger.info(\"批量删除：没有有效的文章被删除（可能ID列表为空或所有ID都无效）。\")\n        return jsonify({\"success\": False, \"message\": \"没有有效的文章被删除。\"}), 400\n    else: # deleted_count > 0 and not errors (这种情况被第一个if分支处理)\n        # 逻辑上这个else分支不应该被达到，但作为保险\n        db.session.rollback()\n        logger.error(\"批量删除文章时出现未预期的逻辑分支。\")\n        return jsonify({\"success\": False, \"error\": \"批量删除过程中发生未知错误。\"}), 500\n\n\n\n# 管理员阅读模式路由\n@admin_bp.route('/reading/<int:reading_id>')\n@login_required\n@admin_required\ndef admin_reading(reading_id):\n    \"\"\"管理员阅读模式视图 - 用于测试和管理阅读模式功能\"\"\"\n    try:\n        # 获取文章内容，与普通阅读视图逻辑类似\n        from models import Article, ReadingPreference, Author\n        from flask_login import current_user\n\n        reading = Article.query.get_or_404(reading_id)\n\n        # 获取作者信息\n        author_name = \"未知\"\n        reader = None\n        if reading.reader_id:\n            reader = Author.query.get(reading.reader_id)\n            if reader:\n                author_name = reader.name\n\n        # 获取用户阅读偏好\n        reading_preference = None\n        if current_user.is_authenticated:\n            reading_preference = ReadingPreference.query.filter_by(user_id=current_user.id).first()\n\n        # 返回模板，传递完整的变量（参考reading.py的实现）\n        return render_template('article_reading.html',\n                            article=reading,\n                            reading=reading,\n                            author_name=author_name,\n                            reader=reader,\n                            reading_preference=reading_preference,\n                            reading_preferences=reading_preference,  # 兼容性\n                            is_admin=True,\n                            # 管理员有完整访问权限\n                            has_basic_access=True,\n                            has_premium_access=True,\n                            can_access_basic=True,\n                            can_access_premium=True,\n                            # 其他必要变量\n                            basic_message=\"\",\n                            premium_message=\"\",\n                            basic_price=0,\n                            premium_price=0,\n                            is_bookmarked=False)\n    \n    except Exception as e:\n        logger.error(f\"管理员阅读模式页面错误: {str(e)}\")\n        return render_template('error.html', error=f\"加载管理员阅读模式时出错，请稍后再试。错误: {str(e)}\")\n\n@admin_bp.route('/settings')\n@login_required\n@admin_required\ndef system_settings():\n    \"\"\"显示系统设置页面\"\"\"\n    try:\n        # 获取所有设置\n        settings = SystemSetting.query.all()\n        settings_dict = {s.key: s.value for s in settings}\n        \n        return render_template('admin/settings.html', settings=settings_dict)\n    except Exception as e:\n        logger.error(f\"加载系统设置页面失败: {str(e)}\")\n        flash('加载系统设置时出错', 'error')\n        return redirect(url_for('admin.dashboard'))\n\n@admin_bp.route('/api/settings', methods=['GET'])\n@login_required\n@admin_required\ndef get_settings():\n    \"\"\"获取系统设置\"\"\"\n    try:\n        settings = SystemSetting.query.all()\n        settings_dict = {s.key: s.value for s in settings}\n        \n        return jsonify({\n            'success': True,\n            'settings': settings_dict\n        })\n    except Exception as e:\n        logger.error(f\"获取系统设置API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/settings', methods=['POST'])\n@login_required\n@admin_required\ndef update_settings():\n    \"\"\"更新系统设置\"\"\"\n    try:\n        data = request.get_json()\n        \n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n        \n        # 遍历所有设置键值对进行更新或创建\n        for key, value in data.items():\n            # 查找现有设置\n            setting = SystemSetting.query.filter_by(key=key).first()\n            \n            if setting:\n                # 更新现有设置\n                setting.value = value\n            else:\n                # 创建新设置\n                new_setting = SystemSetting(key=key, value=value)\n                db.session.add(new_setting)\n        \n        db.session.commit()\n        return jsonify({'success': True, 'message': '系统设置已更新'})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新系统设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# --- 工单管理 ---\n@admin_bp.route('/tickets')\n@login_required\n@staff_required\ndef tickets():\n    \"\"\"工单管理页面\"\"\"\n    try:\n        from models import SupportTicket\n        from datetime import datetime\n\n        page = request.args.get('page', 1, type=int)\n        per_page = 20\n        query = SupportTicket.query.order_by(SupportTicket.created_at.desc())\n\n        # 获取筛选参数\n        status = request.args.get('status', '')\n        priority = request.args.get('priority', '')\n        category = request.args.get('category', '')\n        start_date = request.args.get('start_date', '')\n        end_date = request.args.get('end_date', '')\n\n        # 状态筛选\n        if status:\n            query = query.filter(SupportTicket.status == status)\n\n        # 优先级筛选\n        if priority:\n            query = query.filter(SupportTicket.priority == priority)\n\n        # 分类筛选\n        if category:\n            query = query.filter(SupportTicket.category == category)\n\n        # 时间筛选\n        if start_date:\n            try:\n                start_dt = datetime.strptime(start_date, '%Y-%m-%d')\n                query = query.filter(SupportTicket.created_at >= start_dt)\n            except ValueError:\n                flash('开始日期格式错误', 'error')\n\n        if end_date:\n            try:\n                end_dt = datetime.strptime(end_date, '%Y-%m-%d')\n                # 结束日期包含当天的所有时间\n                end_dt = end_dt.replace(hour=23, minute=59, second=59)\n                query = query.filter(SupportTicket.created_at <= end_dt)\n            except ValueError:\n                flash('结束日期格式错误', 'error')\n\n        pagination = query.paginate(page=page, per_page=per_page)\n        tickets = pagination.items\n\n        return render_template('admin/ticket_list.html',\n                              tickets=tickets,\n                              pagination=pagination,\n                              status=status,\n                              priority=priority,\n                              category=category,\n                              start_date=start_date,\n                              end_date=end_date)\n    except Exception as e:\n        logger.error(f\"加载工单管理页面失败: {str(e)}\")\n        flash('加载工单管理页面时出错', 'error')\n        return redirect(url_for('admin.dashboard'))\n\n@admin_bp.route('/tickets/<int:ticket_id>')\n@login_required\n@staff_required\ndef ticket_detail(ticket_id):\n    \"\"\"工单详情页面\"\"\"\n    try:\n        from models import SupportTicket, TicketLog\n        ticket = SupportTicket.query.get_or_404(ticket_id)\n        logs = TicketLog.query.filter_by(ticket_id=ticket_id).order_by(TicketLog.created_at.asc()).all()\n\n        return render_template('admin/ticket_detail.html', ticket=ticket, logs=logs)\n    except Exception as e:\n        logger.error(f\"加载工单详情页面失败: {str(e)}\")\n        flash('加载工单详情时出错', 'error')\n        return redirect(url_for('admin.tickets'))\n\n@admin_bp.route('/api/tickets', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_tickets():\n    \"\"\"获取工单列表API\"\"\"\n    try:\n        from models import SupportTicket\n\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        status = request.args.get('status', 'all')\n        category = request.args.get('category', 'all')\n        priority = request.args.get('priority', 'all')\n\n        query = SupportTicket.query\n\n        if status != 'all':\n            query = query.filter_by(status=status)\n\n        if category != 'all':\n            query = query.filter_by(category_primary=category)\n\n        if priority != 'all':\n            query = query.filter_by(priority=priority)\n\n        tickets = query.order_by(SupportTicket.created_at.desc()).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        ticket_list = []\n        for ticket in tickets.items:\n            # 获取用户VIP信息 - 仅管理员可见\n            vip_level = \"普通用户\"\n            if current_user.can_access_finance() and ticket.user.finance and ticket.user.finance.vip_level > 0:\n                vip_levels = {1: \"初级VIP\", 2: \"高级VIP\"}\n                vip_level = vip_levels.get(ticket.user.finance.vip_level, \"VIP用户\")\n\n            ticket_data = {\n                'id': ticket.id,\n                'ticket_number': ticket.get_ticket_number(),\n                'subject': ticket.subject,\n                'content': ticket.content,\n                'category_primary': ticket.category_primary,\n                'category_secondary': ticket.category_secondary,\n                'status': ticket.status,\n                'priority': ticket.priority,\n                'created_at': ticket.created_at.isoformat(),\n                'updated_at': ticket.updated_at.isoformat(),\n                'resolved_at': ticket.resolved_at.isoformat() if ticket.resolved_at else None,\n                'admin_reply': ticket.admin_reply,\n                'user': {\n                    'id': ticket.user.id,\n                    'username': ticket.user.username,\n                    'email': ticket.user.email,\n                    'vip_level': vip_level\n                },\n                'admin': {\n                    'id': ticket.admin.id,\n                    'username': ticket.admin.username\n                } if ticket.admin else None,\n                'staff': {\n                    'id': ticket.staff.id,\n                    'username': ticket.staff.username\n                } if ticket.staff else None\n            }\n            ticket_list.append(ticket_data)\n\n        return jsonify({\n            'success': True,\n            'tickets': ticket_list,\n            'pagination': {\n                'page': tickets.page,\n                'pages': tickets.pages,\n                'per_page': tickets.per_page,\n                'total': tickets.total,\n                'has_next': tickets.has_next,\n                'has_prev': tickets.has_prev\n            }\n        })\n\n    except Exception as e:\n        logger.error(f\"获取工单列表API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/tickets/stats', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_tickets_stats():\n    \"\"\"获取工单统计信息API\"\"\"\n    try:\n        from models import SupportTicket\n        from datetime import datetime, timedelta\n        from sqlalchemy import func\n\n        # 基础统计\n        total_tickets = SupportTicket.query.count()\n        open_tickets = SupportTicket.query.filter(SupportTicket.status == 'open').count()\n        closed_tickets = SupportTicket.query.filter(SupportTicket.status == 'closed').count()\n\n        # 时间统计\n        today = datetime.now().date()\n        today_created = SupportTicket.query.filter(\n            func.date(SupportTicket.created_at) == today\n        ).count()\n\n        today_closed = SupportTicket.query.filter(\n            SupportTicket.status == 'closed',\n            func.date(SupportTicket.updated_at) == today\n        ).count()\n\n        # 状态分布\n        status_stats = db.session.query(\n            SupportTicket.status,\n            func.count(SupportTicket.id).label('count')\n        ).group_by(SupportTicket.status).all()\n\n        status_distribution = {stat.status: stat.count for stat in status_stats}\n\n        # 优先级分布\n        priority_stats = db.session.query(\n            SupportTicket.priority,\n            func.count(SupportTicket.id).label('count')\n        ).group_by(SupportTicket.priority).all()\n\n        priority_distribution = {stat.priority: stat.count for stat in priority_stats}\n\n        # 分类分布\n        category_stats = db.session.query(\n            SupportTicket.category,\n            func.count(SupportTicket.id).label('count')\n        ).group_by(SupportTicket.category).all()\n\n        category_distribution = {stat.category: stat.count for stat in category_stats}\n\n        # 员工表现（模拟数据）\n        staff_performance = [\n            {'staff': '客服A', 'assigned': 23, 'resolved': 20, 'avg_time': 18.5},\n            {'staff': '客服B', 'assigned': 19, 'resolved': 17, 'avg_time': 22.3}\n        ]\n\n        stats = {\n            'total_tickets': total_tickets,\n            'open_tickets': open_tickets,\n            'closed_tickets': closed_tickets,\n            'today_created': today_created,\n            'today_closed': today_closed,\n            'avg_response_time': 2.5,  # 模拟数据\n            'avg_resolution_time': 24.5,  # 模拟数据\n            'status_distribution': status_distribution,\n            'priority_distribution': priority_distribution,\n            'category_distribution': category_distribution,\n            'staff_performance': staff_performance\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取工单统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/tickets/<int:ticket_id>/reply', methods=['POST'])\n@login_required\n@staff_required\ndef api_reply_ticket(ticket_id):\n    \"\"\"回复工单API\"\"\"\n    try:\n        from models import SupportTicket, TicketLog\n        from utils.customer_service import send_ticket_notification, update_ticket_txt_log, create_ticket_log\n\n        ticket = SupportTicket.query.get_or_404(ticket_id)\n        data = request.get_json()\n\n        if not data.get('reply'):\n            return jsonify({'success': False, 'error': '回复内容不能为空'}), 400\n\n        # 更新工单\n        ticket.admin_reply = data.get('reply')\n        ticket.admin_id = current_user.id\n        ticket.status = data.get('status', 'processing')\n        ticket.updated_at = datetime.utcnow()\n\n        if data.get('status') == 'resolved':\n            ticket.resolved_at = datetime.utcnow()\n\n        # 记录操作日志\n        create_ticket_log(\n            ticket_id=ticket.id,\n            action_type='replied',\n            action_by=current_user.id,\n            action_details=f\"回复: {data.get('reply')[:50]}...\"\n        )\n\n        db.session.commit()\n\n        # 发送邮件通知用户\n        try:\n            send_ticket_notification(ticket, 'replied')\n        except Exception as e:\n            logger.error(f\"发送回复邮件通知失败: {e}\")\n\n        # 更新TXT日志\n        try:\n            update_ticket_txt_log(ticket, '客服回复', data.get('reply'))\n        except Exception as e:\n            logger.error(f\"更新TXT日志失败: {e}\")\n\n        return jsonify({'success': True, 'message': '回复成功'})\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"回复工单失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\ndef get_vip_status_indicator(user_id):\n    \"\"\"获取VIP状态指示器\"\"\"\n    try:\n        from vip_status_indicator import VIPStatusIndicator\n        return VIPStatusIndicator.check_vip_status(user_id)\n    except Exception as e:\n        return {\n            'status': 'red',\n            'message': '检查失败',\n            'details': str(e)\n        }\n\ndef get_vip_status_summary():\n    \"\"\"获取VIP状态汇总\"\"\"\n    try:\n        from vip_status_indicator import VIPStatusIndicator\n        return VIPStatusIndicator.get_status_summary()\n    except Exception as e:\n        return {\n            'total_vip_users': 0,\n            'green': 0,\n            'yellow': 0,\n            'red': 0,\n            'health_rate': 0\n        }\n\n@admin_bp.route('/users')\n@login_required\n@staff_required\ndef users_dashboard():\n    \"\"\"显示用户管理页面 - 简化版本\"\"\"\n    try:\n        # 获取基本用户列表\n        page = request.args.get('page', 1, type=int)\n        per_page = 20\n\n        # 简单查询，避免复杂的join操作\n        users_query = User.query.order_by(User.created_at.desc())\n\n        # 基本搜索\n        search = request.args.get('search', '')\n        if search:\n            users_query = users_query.filter(User.username.contains(search))\n\n        pagination = users_query.paginate(page=page, per_page=per_page, error_out=False)\n        users = pagination.items\n\n        # 使用财务计算系统获取准确数据\n        from services.finance_calculator import FinanceCalculator\n\n        # 构建用户行HTML\n        user_rows = []\n        for user in users:\n            role_map = {'admin': '管理员', 'staff': '员工', 'customer_service': '客服', 'user': '普通用户'}\n            role_text = role_map.get(user.role, user.role)\n\n            if user.role == 'admin':\n                role_class = 'bg-red-900 text-red-300'\n            elif user.role == 'staff':\n                role_class = 'bg-blue-900 text-blue-300'\n            else:\n                role_class = 'bg-gray-900 text-gray-300'\n\n            created_time = user.created_at.strftime('%Y-%m-%d') if user.created_at else '未知'\n\n            # 使用财务计算系统获取准确的财务信息\n            if user.role in ['admin', 'superadmin']:\n                # 管理员显示无限权限\n                finance_info = f'''\n                <div class=\"bg-gray-800 p-3 rounded text-xs\">\n                    <div class=\"mb-2\"><span class=\"px-2 py-1 rounded bg-red-900 text-red-300 font-bold\">管理员</span></div>\n                    <div class=\"grid grid-cols-2 gap-1\">\n                        <div>余额: <span class=\"text-green-400 font-bold\">∞</span></div>\n                        <div>积分: <span class=\"text-blue-400 font-bold\">∞</span></div>\n                        <div>基础: <span class=\"text-yellow-400 font-bold\">∞</span></div>\n                        <div>高级: <span class=\"text-purple-400 font-bold\">∞</span></div>\n                    </div>\n                </div>\n                '''\n            else:\n                # 普通用户使用财务计算系统\n                finance_summary = FinanceCalculator.get_user_finance_summary(user.id)\n                if finance_summary:\n                    vip_level = finance_summary['vip_level']\n                    vip_text = f\"VIP{vip_level}\" if vip_level > 0 else \"免费\"\n                    vip_class = \"bg-yellow-900 text-yellow-300\" if vip_level > 0 else \"bg-gray-700 text-gray-300\"\n\n                    # VIP状态指示器（专门用于检查购买记录是否正常）\n                    vip_status = get_vip_status_indicator(user.id) if vip_level > 0 else {'status': 'gray', 'message': '非VIP'}\n                    if vip_level > 0:\n                        status_light = \"🟢\" if vip_status['status'] == 'green' else \"🟡\" if vip_status['status'] == 'yellow' else \"🔴\"\n                    else:\n                        status_light = \"\"  # 非VIP用户不显示状态灯\n\n                    # VIP到期时间\n                    vip_expire = \"\"\n                    if finance_summary['vip_expire_at']:\n                        expire_date = finance_summary['vip_expire_at'].strftime('%m-%d')\n                        vip_expire = f\"<br><small class='text-gray-400'>到期:{expire_date}</small>\"\n\n                    # 财务数据\n                    balance = finance_summary['balance_yuan']\n                    points = finance_summary['points_balance']\n                    quota = finance_summary['quota_info']\n\n                    # 数据一致性状态（保留原有的）\n                    integrity = finance_summary['data_integrity']\n                    integrity_icon = \"🔴\" if integrity['status'] == 'inconsistent' else \"🟢\"\n\n                    # 构建状态指示器说明\n                    status_indicators = \"\"\n                    if vip_level > 0:\n                        # 只显示VIP购买记录状态指示器，移除重复的指示器\n                        status_indicators = f'<span class=\"ml-1\" title=\"VIP购买记录状态：{vip_status[\"message\"]}\">{status_light}</span>'\n\n                    finance_info = f'''\n                    <div class=\"bg-gray-800 p-3 rounded text-xs\">\n                        <div class=\"mb-2\">\n                            <span class=\"px-2 py-1 rounded {vip_class} font-bold\">{vip_text}</span>\n                            {status_indicators}\n                            {vip_expire}\n                        </div>\n                        <div class=\"grid grid-cols-2 gap-1\">\n                            <div>余额: <span class=\"text-green-400 font-bold\">¥{balance:.2f}</span></div>\n                            <div>积分: <span class=\"text-blue-400 font-bold\">{points}</span></div>\n                            <div>基础: <span class=\"text-yellow-400 font-bold\">{quota['basic_used']}/{'∞' if quota['basic_limit'] >= 999999 else quota['basic_limit']}</span></div>\n                            <div>高级: <span class=\"text-purple-400 font-bold\">{quota['premium_used']}/{'∞' if quota['premium_limit'] >= 999999 else quota['premium_limit']}</span></div>\n                        </div>\n                        {f'<div class=\"mt-1 text-xs text-gray-400\">🚦 {vip_status[\"message\"]}</div>' if vip_status['status'] != 'green' and vip_level > 0 else ''}\n                    </div>\n                    '''\n                else:\n                    finance_info = '<div class=\"text-xs text-red-500 bg-gray-800 p-2 rounded\">❌ 财务数据错误</div>'\n\n            user_row = f'''\n            <tr class=\"border-b border-gray-800 hover:bg-gray-800\">\n                <td class=\"py-3 px-2 text-center font-mono\">{user.id}</td>\n                <td class=\"py-3 px-3\">\n                    <div class=\"font-medium text-white\">{user.username}</div>\n                    <div class=\"text-xs text-gray-400\">{user.email or \"未设置邮箱\"}</div>\n                </td>\n                <td class=\"py-3 px-2 text-center\">\n                    <span class=\"px-2 py-1 rounded text-xs {role_class} font-bold\">\n                        {role_text}\n                    </span>\n                </td>\n                <td class=\"py-3 px-3\">{finance_info}</td>\n                <td class=\"py-3 px-2 text-center text-xs text-gray-400\">{created_time}</td>\n                <td class=\"py-3 px-2 text-center\">\n                    <a href=\"/admin/users/{user.id}/finance\"\n                       class=\"px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 font-bold\">\n                        详情\n                    </a>\n                </td>\n            </tr>\n            '''\n            user_rows.append(user_row)\n\n        # 获取VIP状态汇总\n        vip_summary = get_vip_status_summary()\n\n        # 分页链接\n        prev_link = f'<a href=\"?page={pagination.prev_num}&search={search}\" class=\"px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700\">上一页</a>' if pagination.has_prev else '<span class=\"px-3 py-1 bg-gray-800 text-gray-500 rounded\">上一页</span>'\n        next_link = f'<a href=\"?page={pagination.next_num}&search={search}\" class=\"px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700\">下一页</a>' if pagination.has_next else '<span class=\"px-3 py-1 bg-gray-800 text-gray-500 rounded\">下一页</span>'\n\n        # 返回简化的HTML页面\n        return f'''\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>用户管理 - 塔罗解读</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        body {{ background-color: #121212; color: #e0e0e0; }}\n        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"container mx-auto px-4 py-8\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h1 class=\"text-3xl font-bold text-yellow-400\">👥 用户管理</h1>\n            <a href=\"/admin/\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                ← 返回管理后台\n            </a>\n        </div>\n\n        <!-- VIP状态汇总面板 -->\n        <div class=\"card rounded-lg p-4 mb-6\">\n            <h3 class=\"text-lg font-semibold text-yellow-400 mb-3\">🚦 VIP购买记录状态监控</h3>\n            <div class=\"flex space-x-6 mb-3\">\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🟢</span>\n                    <span>正常: {vip_summary['green']}</span>\n                </div>\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🟡</span>\n                    <span>警告: {vip_summary['yellow']}</span>\n                </div>\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🔴</span>\n                    <span>异常: {vip_summary['red']}</span>\n                </div>\n                <div class=\"ml-auto\">\n                    <span class=\"text-sm text-gray-400\">VIP健康率: {vip_summary['health_rate']:.1f}%</span>\n                </div>\n            </div>\n            <div class=\"text-xs text-gray-400\">\n                <strong>红绿灯说明：</strong>\n                🟢 VIP用户且有对应购买记录 |\n                🟡 VIP用户但数据不完整 |\n                🔴 VIP用户但完全没有购买记录（需要处理）\n            </div>\n        </div>\n\n        <div class=\"card rounded-lg p-6\">\n            <div class=\"mb-4\">\n                <form method=\"GET\" class=\"flex gap-4\">\n                    <input type=\"text\" name=\"search\" value=\"{search}\"\n                           placeholder=\"搜索用户名...\"\n                           class=\"px-3 py-2 bg-gray-700 text-white rounded border border-gray-600\">\n                    <button type=\"submit\" class=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\">\n                        搜索\n                    </button>\n                </form>\n            </div>\n\n            <div class=\"overflow-x-auto\">\n                <table class=\"w-full table-fixed\">\n                    <thead>\n                        <tr class=\"border-b border-gray-700 bg-gray-900\">\n                            <th class=\"w-16 text-center py-3 px-2 text-yellow-400 font-bold\">ID</th>\n                            <th class=\"w-48 text-left py-3 px-3 text-yellow-400 font-bold\">用户信息</th>\n                            <th class=\"w-20 text-center py-3 px-2 text-yellow-400 font-bold\">角色</th>\n                            <th class=\"w-64 text-left py-3 px-3 text-yellow-400 font-bold\">💰 财务状况</th>\n                            <th class=\"w-24 text-center py-3 px-2 text-yellow-400 font-bold\">注册</th>\n                            <th class=\"w-20 text-center py-3 px-2 text-yellow-400 font-bold\">操作</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        {\"\".join(user_rows)}\n                    </tbody>\n                </table>\n            </div>\n\n            <div class=\"mt-6 flex justify-between items-center\">\n                <div class=\"text-gray-400\">\n                    共 {pagination.total} 个用户，第 {pagination.page} / {pagination.pages} 页\n                </div>\n                <div class=\"flex gap-2\">\n                    {prev_link}\n                    {next_link}\n                </div>\n            </div>\n        </div>\n    </div>\n</body>\n</html>\n        '''\n\n    except Exception as e:\n        logger.error(f\"加载用户管理页面失败: {str(e)}\")\n        import traceback\n        return f'''\n<!DOCTYPE html>\n<html>\n<head><title>用户管理错误</title></head>\n<body style=\"background: #121212; color: white; padding: 20px;\">\n    <h1>用户管理页面错误</h1>\n    <p>错误信息: {str(e)}</p>\n    <pre>{traceback.format_exc()}</pre>\n    <a href=\"/admin/\" style=\"color: #60a5fa;\">返回管理后台</a>\n</body>\n</html>\n        '''\n\n@admin_bp.route('/users/<int:user_id>/finance')\n@login_required\n@staff_required\ndef user_finance_detail(user_id):\n    \"\"\"用户财务详情页面 - 显示所有原始单据和余额计算过程\"\"\"\n    try:\n        from sqlalchemy.orm import joinedload\n        from models import Purchase, Order, UserFinance, UserConsumptionRecord, Article\n        from collections import defaultdict\n        from datetime import datetime\n\n        # 获取用户信息\n        user = User.query.options(joinedload(User.finance)).get_or_404(user_id)\n\n        # 获取所有购买记录（原始单据）- 只查询存在的字段\n        purchases = db.session.query(Purchase.id, Purchase.user_id, Purchase.article_id,\n                                    Purchase.amount, Purchase.content_type, Purchase.purchase_date,\n                                    Purchase.payment_method, Purchase.transaction_id)\\\n                             .filter_by(user_id=user_id)\\\n                             .order_by(Purchase.purchase_date.desc()).all()\n\n        # 获取所有订单记录\n        orders = Order.query.filter_by(user_id=user_id)\\\n                           .order_by(Order.created_at.desc()).all()\n\n        # 获取所有消费记录\n        consumption_records = UserConsumptionRecord.query.filter_by(user_id=user_id)\\\n                                                         .order_by(UserConsumptionRecord.created_at.desc()).all()\n\n        # 构建详细的财务流水记录\n        finance_transactions = []\n\n        # 添加购买记录（收入）\n        for purchase in purchases:\n            article = Article.query.get(purchase.article_id) if purchase.article_id else None\n            if purchase.article_id:\n                description = f'购买文章: {article.title if article else \"已删除文章\"}'\n            else:\n                # VIP购买或其他类型\n                if purchase.amount == 12800:\n                    description = '购买VIP1个月'\n                elif purchase.amount == 140800:\n                    description = '购买VIP1年'\n                else:\n                    description = f'购买VIP ¥{purchase.amount/100:.2f}'\n\n            finance_transactions.append({\n                'type': 'purchase',\n                'date': purchase.purchase_date,\n                'description': description,\n                'content_type': purchase.content_type,\n                'payment_method': purchase.payment_method,\n                'amount': purchase.amount,\n                'transaction_id': purchase.transaction_id,\n                'status': 'completed'\n            })\n\n        # 添加消费记录（支出）\n        for record in consumption_records:\n            article = Article.query.get(record.article_id) if record.article_id else None\n            finance_transactions.append({\n                'type': 'consumption',\n                'date': record.created_at,\n                'description': f'消费: {article.title if article else \"系统消费\"}',\n                'content_type': record.content_type,\n                'cost_type': record.cost_type,\n                'amount': -record.cost_amount,  # 负数表示支出\n                'status': 'completed'\n            })\n\n        # 按时间排序\n        finance_transactions.sort(key=lambda x: x['date'] if x['date'] else datetime.min, reverse=True)\n\n        # 计算余额变化过程\n        running_balance = 0\n        if user.finance:\n            running_balance = user.finance.balance or 0\n\n        # 从最新记录开始，反向计算每笔交易后的余额\n        for i, transaction in enumerate(finance_transactions):\n            transaction['balance_after'] = running_balance\n            running_balance -= transaction['amount']\n            transaction['balance_before'] = running_balance\n\n        # 反转列表，使其按时间正序显示\n        finance_transactions.reverse()\n\n        # 计算统计数据\n        finance_stats = {\n            'total_purchases': len(purchases),\n            'total_spent': sum(p.amount for p in purchases),\n            'basic_purchases': len([p for p in purchases if p.content_type == 'basic']),\n            'premium_purchases': len([p for p in purchases if p.content_type == 'premium']),\n            'cash_payments': len([p for p in purchases if p.payment_method in ['alipay', 'wechat', 'mock']]),\n            'quota_payments': len([p for p in purchases if p.payment_method == 'quota']),\n            'points_payments': len([p for p in purchases if p.payment_method == 'points']),\n            'total_consumption': sum(r.cost_amount for r in consumption_records),\n            'current_balance': user.finance.balance if user.finance else 0\n        }\n\n        # 按月统计\n        monthly_stats = defaultdict(lambda: {'purchases': 0, 'consumption': 0, 'net': 0})\n\n        for transaction in finance_transactions:\n            if transaction['date']:\n                month_key = transaction['date'].strftime('%Y-%m')\n                if transaction['type'] == 'purchase':\n                    monthly_stats[month_key]['purchases'] += transaction['amount']\n                else:\n                    monthly_stats[month_key]['consumption'] += abs(transaction['amount'])\n                monthly_stats[month_key]['net'] = monthly_stats[month_key]['purchases'] - monthly_stats[month_key]['consumption']\n\n        # 转换为列表\n        monthly_data = []\n        for month, stats in sorted(monthly_stats.items(), reverse=True):\n            monthly_data.append({\n                'month': month,\n                'purchases': stats['purchases'],\n                'consumption': stats['consumption'],\n                'net': stats['net']\n            })\n\n        # 构建财务交易记录HTML\n        transaction_rows = []\n        for transaction in finance_transactions:\n            transaction_date = transaction['date'].strftime('%Y-%m-%d %H:%M') if transaction['date'] else '未知'\n            amount_class = 'text-green-400' if transaction['amount'] > 0 else 'text-red-400'\n            # 正确转换金额：分 -> 元\n            amount_yuan = transaction['amount'] / 100.0\n            amount_text = f\"+¥{amount_yuan:.2f}\" if amount_yuan > 0 else f\"-¥{abs(amount_yuan):.2f}\"\n\n            transaction_row = f'''\n            <tr class=\"border-b border-gray-800 hover:bg-gray-800\">\n                <td class=\"py-3 px-4\">{transaction_date}</td>\n                <td class=\"py-3 px-4\">{transaction['type']}</td>\n                <td class=\"py-3 px-4\">{transaction['description']}</td>\n                <td class=\"py-3 px-4\">{transaction.get('content_type', '-')}</td>\n                <td class=\"py-3 px-4\">{transaction.get('payment_method', transaction.get('cost_type', '-'))}</td>\n                <td class=\"py-3 px-4 {amount_class}\">{amount_text}</td>\n                <td class=\"py-3 px-4\">¥{transaction.get('balance_before', 0)/100:.2f}</td>\n                <td class=\"py-3 px-4\">¥{transaction.get('balance_after', 0)/100:.2f}</td>\n                <td class=\"py-3 px-4\">{transaction.get('transaction_id', '-')}</td>\n            </tr>\n            '''\n            transaction_rows.append(transaction_row)\n\n        # 构建月度统计HTML\n        monthly_rows = []\n        for month_data in monthly_data:\n            net_class = 'text-green-400' if month_data['net'] >= 0 else 'text-red-400'\n            monthly_row = f'''\n            <tr class=\"border-b border-gray-800\">\n                <td class=\"py-2 px-4\">{month_data['month']}</td>\n                <td class=\"py-2 px-4 text-green-400\">¥{month_data['purchases']/100:.2f}</td>\n                <td class=\"py-2 px-4 text-red-400\">¥{month_data['consumption']/100:.2f}</td>\n                <td class=\"py-2 px-4 {net_class}\">¥{month_data['net']/100:.2f}</td>\n            </tr>\n            '''\n            monthly_rows.append(monthly_row)\n\n        # 构建UserFinance信息HTML\n        finance_info_html = \"\"\n        if user.finance:\n            vip_level = user.finance.vip_level or 0\n            vip_expire = user.finance.vip_expire_at or \"N/A\"\n            balance = (user.finance.balance or 0) / 100\n            total_spent = (user.finance.total_spent or 0) / 100\n            points_balance = user.finance.points_balance or 0\n            basic_quota_used = user.finance.basic_quota_used or 0\n            premium_quota_used = user.finance.premium_quota_used or 0\n            quota_reset_date = user.finance.quota_reset_date or \"N/A\"\n\n            finance_info_html = f'''\n                <p><strong>VIP等级:</strong> {vip_level}</p>\n                <p><strong>VIP到期时间:</strong> {vip_expire}</p>\n                <p><strong>余额:</strong> ¥{balance:.2f}</p>\n                <p><strong>总消费金额:</strong> ¥{total_spent:.2f}</p>\n                <p><strong>积分余额:</strong> {points_balance}</p>\n                <p><strong>基础配额已用:</strong> {basic_quota_used}</p>\n                <p><strong>高级配额已用:</strong> {premium_quota_used}</p>\n                <p><strong>配额重置日期:</strong> {quota_reset_date}</p>\n            '''\n\n            # 添加状态分析\n            if vip_level > 0 and total_spent > 0:\n                finance_info_html += f'''\n                    <div class=\"mt-2 p-2 bg-green-800 rounded\">\n                        <p class=\"text-green-300\">✅ <strong>发现购买痕迹！</strong></p>\n                        <p class=\"text-green-300\">用户是VIP且total_spent > 0，说明确实有消费记录</p>\n                        <p class=\"text-green-300\">应该为这¥{total_spent:.2f}的消费创建Purchase记录</p>\n                    </div>\n                '''\n            elif vip_level > 0 and total_spent == 0:\n                finance_info_html += '''\n                    <div class=\"mt-2 p-2 bg-red-800 rounded\">\n                        <p class=\"text-red-300\">🚨 <strong>异常：VIP但total_spent=0</strong></p>\n                        <p class=\"text-red-300\">可能是管理员手动设置的VIP或数据异常</p>\n                    </div>\n                '''\n        else:\n            finance_info_html = '<p class=\"text-red-300\">❌ 该用户没有UserFinance记录</p>'\n\n        # 获取Order数量\n        order_count = Order.query.filter_by(user_id=user.id).count()\n\n        # 返回简化的HTML页面\n        return f'''\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>用户财务详情 - {user.username}</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        body {{ background-color: #121212; color: #e0e0e0; }}\n        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"container mx-auto px-4 py-8\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h1 class=\"text-3xl font-bold text-yellow-400\">💰 {user.username} 的财务详情</h1>\n            <a href=\"/admin/users\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                ← 返回用户管理\n            </a>\n        </div>\n\n        <!-- UserFinance详细信息 -->\n        <div class=\"card p-4 rounded-lg mb-6 bg-blue-900 border border-blue-700\">\n            <h3 class=\"text-lg font-semibold text-blue-400 mb-2\">🔍 UserFinance表详细信息</h3>\n            <div class=\"text-sm\">\n                {finance_info_html}\n                <hr class=\"my-2 border-blue-600\">\n                <p><strong>Purchase记录数:</strong> {len(purchases)}</p>\n                <p><strong>Order记录数:</strong> {order_count}</p>\n            </div>\n        </div>\n\n\n\n        <!-- 财务统计概览 -->\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n            <div class=\"card p-4 rounded-lg\">\n                <h3 class=\"text-lg font-semibold text-yellow-400 mb-2\">总购买次数</h3>\n                <p class=\"text-2xl font-bold\">{finance_stats['total_purchases']}</p>\n            </div>\n            <div class=\"card p-4 rounded-lg\">\n                <h3 class=\"text-lg font-semibold text-green-400 mb-2\">总购买金额</h3>\n                <p class=\"text-2xl font-bold text-green-400\">¥{finance_stats['total_spent']/100:.2f}</p>\n            </div>\n            <div class=\"card p-4 rounded-lg\">\n                <h3 class=\"text-lg font-semibold text-red-400 mb-2\">总消费金额</h3>\n                <p class=\"text-2xl font-bold text-red-400\">¥{finance_stats['total_consumption']/100:.2f}</p>\n            </div>\n            <div class=\"card p-4 rounded-lg\">\n                <h3 class=\"text-lg font-semibold text-blue-400 mb-2\">当前余额</h3>\n                <p class=\"text-2xl font-bold text-blue-400\">¥{finance_stats['current_balance']/100:.2f}</p>\n            </div>\n        </div>\n\n        <!-- 支付方式统计 -->\n        <div class=\"card p-6 rounded-lg mb-6\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">支付方式统计</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">现金支付</p>\n                    <p class=\"text-2xl font-bold text-green-400\">{finance_stats['cash_payments']}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">配额支付</p>\n                    <p class=\"text-2xl font-bold text-blue-400\">{finance_stats['quota_payments']}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">积分支付</p>\n                    <p class=\"text-2xl font-bold text-purple-400\">{finance_stats['points_payments']}</p>\n                </div>\n            </div>\n        </div>\n\n        <!-- 月度统计 -->\n        <div class=\"card p-6 rounded-lg mb-6\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">月度财务统计</h2>\n            <div class=\"overflow-x-auto\">\n                <table class=\"w-full\">\n                    <thead>\n                        <tr class=\"border-b border-gray-700\">\n                            <th class=\"text-left py-2 px-4\">月份</th>\n                            <th class=\"text-left py-2 px-4\">购买金额</th>\n                            <th class=\"text-left py-2 px-4\">消费金额</th>\n                            <th class=\"text-left py-2 px-4\">净收支</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        {\"\".join(monthly_rows)}\n                    </tbody>\n                </table>\n            </div>\n        </div>\n\n        <!-- 详细财务流水 -->\n        <div class=\"card p-6 rounded-lg\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">详细财务流水（原始单据）</h2>\n            <div class=\"overflow-x-auto\">\n                <table class=\"w-full text-sm\">\n                    <thead>\n                        <tr class=\"border-b border-gray-700\">\n                            <th class=\"text-left py-2 px-4\">时间</th>\n                            <th class=\"text-left py-2 px-4\">类型</th>\n                            <th class=\"text-left py-2 px-4\">描述</th>\n                            <th class=\"text-left py-2 px-4\">内容类型</th>\n                            <th class=\"text-left py-2 px-4\">支付方式</th>\n                            <th class=\"text-left py-2 px-4\">金额</th>\n                            <th class=\"text-left py-2 px-4\">交易前余额</th>\n                            <th class=\"text-left py-2 px-4\">交易后余额</th>\n                            <th class=\"text-left py-2 px-4\">交易ID</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        {\"\".join(transaction_rows)}\n                    </tbody>\n                </table>\n            </div>\n        </div>\n    </div>\n</body>\n</html>\n        '''\n\n    except Exception as e:\n        logger.error(f\"加载用户财务详情失败: {str(e)}\")\n        import traceback\n        return f'''\n<!DOCTYPE html>\n<html>\n<head><title>财务详情错误</title></head>\n<body style=\"background: #121212; color: white; padding: 20px;\">\n    <h1>财务详情页面错误</h1>\n    <p>用户ID: {user_id}</p>\n    <p>错误信息: {str(e)}</p>\n    <pre>{traceback.format_exc()}</pre>\n    <a href=\"/admin/users\" style=\"color: #60a5fa;\">返回用户管理</a>\n</body>\n</html>\n        '''\n\n@admin_bp.route('/diagnostic')\ndef link_diagnostic():\n    \"\"\"链接跳转诊断页面\"\"\"\n    return render_template('admin/link_diagnostic.html')\n\n@admin_bp.route('/users/<int:user_id>/fix-finance', methods=['POST'])\n@login_required\n@staff_required\ndef fix_user_finance(user_id):\n    \"\"\"修复用户财务数据\"\"\"\n    try:\n        from services.finance_calculator import AdminFinanceTools\n        success, message = AdminFinanceTools.fix_user_finance_data(user_id)\n\n        if success:\n            return f'''\n            <div style=\"background: #121212; color: white; padding: 20px; font-family: Arial;\">\n                <h2 style=\"color: #4ade80;\">✅ 财务数据修复成功</h2>\n                <p>用户ID: {user_id}</p>\n                <p>修复结果: {message}</p>\n                <a href=\"/admin/users\" style=\"color: #60a5fa; text-decoration: none;\">← 返回用户管理</a>\n            </div>\n            '''\n        else:\n            return f'''\n            <div style=\"background: #121212; color: white; padding: 20px; font-family: Arial;\">\n                <h2 style=\"color: #ef4444;\">❌ 财务数据修复失败</h2>\n                <p>用户ID: {user_id}</p>\n                <p>错误信息: {message}</p>\n                <a href=\"/admin/users\" style=\"color: #60a5fa; text-decoration: none;\">← 返回用户管理</a>\n            </div>\n            '''\n    except Exception as e:\n        return f'''\n        <div style=\"background: #121212; color: white; padding: 20px; font-family: Arial;\">\n            <h2 style=\"color: #ef4444;\">❌ 修复过程出错</h2>\n            <p>用户ID: {user_id}</p>\n            <p>错误信息: {str(e)}</p>\n            <a href=\"/admin/users\" style=\"color: #60a5fa; text-decoration: none;\">← 返回用户管理</a>\n        </div>\n        '''\n\n@admin_bp.route('/data-repair')\n@login_required\n@admin_required\ndef data_repair_dashboard():\n    \"\"\"数据修复控制台\"\"\"\n    try:\n        from services.data_repair import DataValidator\n\n        # 获取数据验证结果\n        validation_result = DataValidator.validate_all_users()\n\n        return f'''\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>数据修复控制台</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        body {{ background-color: #121212; color: #e0e0e0; }}\n        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"container mx-auto px-4 py-8\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h1 class=\"text-3xl font-bold text-red-400\">🔧 数据修复控制台</h1>\n            <a href=\"/admin/\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                ← 返回管理后台\n            </a>\n        </div>\n\n        <!-- 数据验证概览 -->\n        <div class=\"card p-6 rounded-lg mb-6\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">📊 数据完整性概览</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">总用户数</p>\n                    <p class=\"text-3xl font-bold text-blue-400\">{validation_result['total_users'] if validation_result else 0}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">数据正常</p>\n                    <p class=\"text-3xl font-bold text-green-400\">{validation_result['valid_users'] if validation_result else 0}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">数据异常</p>\n                    <p class=\"text-3xl font-bold text-red-400\">{validation_result['invalid_users'] if validation_result else 0}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">完整性率</p>\n                    <p class=\"text-3xl font-bold text-purple-400\">{validation_result['validation_rate']*100:.1f}%</p>\n                </div>\n            </div>\n        </div>\n\n        <!-- 修复操作 -->\n        <div class=\"card p-6 rounded-lg mb-6\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">🛠️ 数据修复操作</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <button onclick=\"runRepair('purchase_fields')\"\n                        class=\"px-4 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 font-bold\">\n                    修复Purchase字段缺失\n                </button>\n                <button onclick=\"runRepair('consumption_records')\"\n                        class=\"px-4 py-3 bg-green-600 text-white rounded hover:bg-green-700 font-bold\">\n                    补充消费记录\n                </button>\n                <button onclick=\"runRepair('quota_records')\"\n                        class=\"px-4 py-3 bg-purple-600 text-white rounded hover:bg-purple-700 font-bold\">\n                    创建配额使用记录\n                </button>\n                <button onclick=\"runRepair('points_records')\"\n                        class=\"px-4 py-3 bg-yellow-600 text-white rounded hover:bg-yellow-700 font-bold\">\n                    修复积分记录\n                </button>\n            </div>\n            <div class=\"mt-4\">\n                <button onclick=\"runRepair('full_repair')\"\n                        class=\"w-full px-4 py-3 bg-red-600 text-white rounded hover:bg-red-700 font-bold\">\n                    🚨 运行完整修复（谨慎操作）\n                </button>\n            </div>\n        </div>\n\n        <!-- 修复结果 -->\n        <div id=\"repair-result\" class=\"card p-6 rounded-lg\" style=\"display: none;\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">修复结果</h2>\n            <div id=\"repair-content\" class=\"text-sm\"></div>\n        </div>\n    </div>\n\n    <script>\n        function runRepair(repairType) {{\n            const resultDiv = document.getElementById('repair-result');\n            const contentDiv = document.getElementById('repair-content');\n\n            resultDiv.style.display = 'block';\n            contentDiv.innerHTML = '<p class=\"text-blue-400\">正在执行修复操作，请稍候...</p>';\n\n            fetch('/admin/data-repair/run', {{\n                method: 'POST',\n                headers: {{\n                    'Content-Type': 'application/json',\n                }},\n                body: JSON.stringify({{repair_type: repairType}})\n            }})\n            .then(response => response.json())\n            .then(data => {{\n                if (data.success) {{\n                    contentDiv.innerHTML = '<div class=\"text-green-400\"><h3>✅ 修复成功</h3><pre>' +\n                                         JSON.stringify(data.results, null, 2) + '</pre></div>';\n                }} else {{\n                    contentDiv.innerHTML = '<div class=\"text-red-400\"><h3>❌ 修复失败</h3><p>' +\n                                         data.error + '</p></div>';\n                }}\n            }})\n            .catch(error => {{\n                contentDiv.innerHTML = '<div class=\"text-red-400\"><h3>❌ 请求失败</h3><p>' +\n                                     error.message + '</p></div>';\n            }});\n        }}\n    </script>\n</body>\n</html>\n        '''\n\n    except Exception as e:\n        return f'''\n        <div style=\"background: #121212; color: white; padding: 20px;\">\n            <h1>数据修复控制台错误</h1>\n            <p>错误信息: {str(e)}</p>\n            <a href=\"/admin/\" style=\"color: #60a5fa;\">返回管理后台</a>\n        </div>\n        '''\n\n@admin_bp.route('/data-repair/run', methods=['POST'])\n@login_required\n@admin_required\ndef run_data_repair():\n    \"\"\"执行数据修复操作\"\"\"\n    try:\n        from services.data_repair import DataRepairTool\n\n        data = request.get_json()\n        repair_type = data.get('repair_type')\n\n        if repair_type == 'purchase_fields':\n            success, message = DataRepairTool.repair_purchase_missing_fields()\n            return jsonify({'success': success, 'results': [message]})\n\n        elif repair_type == 'consumption_records':\n            success, message = DataRepairTool.repair_missing_consumption_records()\n            return jsonify({'success': success, 'results': [message]})\n\n        elif repair_type == 'quota_records':\n            success, message = DataRepairTool.create_missing_quota_records()\n            return jsonify({'success': success, 'results': [message]})\n\n        elif repair_type == 'points_records':\n            success, message = DataRepairTool.repair_points_records()\n            return jsonify({'success': success, 'results': [message]})\n\n        elif repair_type == 'full_repair':\n            results = DataRepairTool.run_full_repair()\n            return jsonify({'success': True, 'results': results})\n\n        else:\n            return jsonify({'success': False, 'error': '未知的修复类型'})\n\n    except Exception as e:\n        logger.error(f\"执行数据修复失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)})\n\n\n\n@admin_bp.route('/vip-status')\n@login_required\n@staff_required\ndef vip_status_page():\n    \"\"\"VIP状态监控页面\"\"\"\n    try:\n        # 获取VIP状态指示器\n        vip_status_data = get_vip_status_summary()\n        all_vip_status = []\n\n        # 获取所有VIP用户\n        vip_user_ids = db.session.query(UserFinance.user_id).filter(UserFinance.vip_level > 0).all()\n        vip_user_ids = [uid[0] for uid in vip_user_ids]\n\n        for user_id in vip_user_ids:\n            user = User.query.get(user_id)\n            if user:\n                status = get_vip_status_indicator(user_id)\n                all_vip_status.append({\n                    'user': user,\n                    'status': status\n                })\n\n        # 生成HTML\n        user_rows = []\n        for item in all_vip_status:\n            user = item['user']\n            status = item['status']\n\n            # 状态指示灯\n            if status['status'] == 'green':\n                light = '🟢'\n                status_class = 'text-green-400'\n            elif status['status'] == 'yellow':\n                light = '🟡'\n                status_class = 'text-yellow-400'\n            else:\n                light = '🔴'\n                status_class = 'text-red-400'\n\n            # 获取财务信息\n            finance = UserFinance.query.filter_by(user_id=user.id).first()\n            if finance:\n                vip_info = f\"VIP{finance.vip_level}\"\n                balance = finance.balance / 100 if finance.balance else 0\n                total_spent = finance.total_spent / 100 if finance.total_spent else 0\n                finance_text = f\"余额: ¥{balance:.2f} | 消费: ¥{total_spent:.2f}\"\n            else:\n                vip_info = \"无财务记录\"\n                finance_text = \"N/A\"\n\n            user_row = f'''\n            <tr class=\"border-b border-gray-700 hover:bg-gray-800\">\n                <td class=\"py-3 px-4 text-center\">{light}</td>\n                <td class=\"py-3 px-4\">\n                    <div class=\"font-medium\">{user.username}</div>\n                    <div class=\"text-xs text-gray-400\">{user.email or \"无邮箱\"}</div>\n                </td>\n                <td class=\"py-3 px-4 text-center\">\n                    <span class=\"px-2 py-1 bg-yellow-800 text-yellow-300 rounded text-xs\">{vip_info}</span>\n                </td>\n                <td class=\"py-3 px-4 text-xs\">{finance_text}</td>\n                <td class=\"py-3 px-4 {status_class} text-sm\">{status['message']}</td>\n                <td class=\"py-3 px-4 text-xs text-gray-400\">{status.get('details', '')}</td>\n            </tr>\n            '''\n            user_rows.append(user_row)\n\n        return f'''\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>VIP状态监控</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        body {{ background-color: #121212; color: #e0e0e0; }}\n        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"container mx-auto px-4 py-8\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h1 class=\"text-3xl font-bold text-yellow-400\">🚦 VIP状态监控</h1>\n            <a href=\"/admin/\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                ← 返回管理后台\n            </a>\n        </div>\n\n        <!-- VIP状态汇总 -->\n        <div class=\"card rounded-lg p-4 mb-6\">\n            <h3 class=\"text-lg font-semibold text-yellow-400 mb-3\">📊 VIP状态概览</h3>\n            <div class=\"flex space-x-6\">\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🟢</span>\n                    <span>正常: {vip_status_data.get('green', 0)}</span>\n                </div>\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🟡</span>\n                    <span>警告: {vip_status_data.get('yellow', 0)}</span>\n                </div>\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🔴</span>\n                    <span>异常: {vip_status_data.get('red', 0)}</span>\n                </div>\n                <div class=\"ml-auto\">\n                    <span class=\"text-sm text-gray-400\">健康率: {vip_status_data.get('health_rate', 0):.1f}%</span>\n                </div>\n            </div>\n            <div class=\"mt-2 text-sm text-gray-400\">\n                说明: 🟢正常 🟡需注意 🔴需处理\n            </div>\n        </div>\n\n        <!-- VIP用户详情 -->\n        <div class=\"card rounded-lg p-6\">\n            <h3 class=\"text-lg font-semibold text-yellow-400 mb-4\">VIP用户详情</h3>\n            <div class=\"overflow-x-auto\">\n                <table class=\"w-full\">\n                    <thead>\n                        <tr class=\"border-b border-gray-700 bg-gray-900\">\n                            <th class=\"text-center py-3 px-4 text-yellow-400\">状态</th>\n                            <th class=\"text-left py-3 px-4 text-yellow-400\">用户</th>\n                            <th class=\"text-center py-3 px-4 text-yellow-400\">VIP等级</th>\n                            <th class=\"text-left py-3 px-4 text-yellow-400\">财务信息</th>\n                            <th class=\"text-left py-3 px-4 text-yellow-400\">状态说明</th>\n                            <th class=\"text-left py-3 px-4 text-yellow-400\">详情</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        {\"\".join(user_rows)}\n                    </tbody>\n                </table>\n            </div>\n\n            <div class=\"mt-4 text-center text-gray-400\">\n                总VIP用户: {len(all_vip_status)} | 刷新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n            </div>\n        </div>\n    </div>\n</body>\n</html>\n        '''\n\n    except Exception as e:\n        return f'''\n        <div style=\"background: #121212; color: white; padding: 20px;\">\n            <h1>VIP状态监控错误</h1>\n            <p>错误信息: {str(e)}</p>\n            <a href=\"/admin/\" style=\"color: #60a5fa;\">返回管理后台</a>\n        </div>\n        '''\n\n@admin_bp.route('/test-clean')\n@login_required\n@staff_required\ndef test_clean_page():\n    \"\"\"完全干净的测试页面\"\"\"\n    return '''\n<!DOCTYPE html>\n<html>\n<head>\n    <title>干净测试页面</title>\n    <style>\n        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }\n        .test-link {\n            display: inline-block;\n            padding: 15px 30px;\n            margin: 10px;\n            background: #4CAF50;\n            color: white;\n            text-decoration: none;\n            border-radius: 5px;\n            font-size: 16px;\n        }\n        .test-link:hover { background: #45a049; }\n        .log { background: #333; padding: 10px; margin: 10px 0; border-radius: 5px; }\n    </style>\n</head>\n<body>\n    <h1>🧪 干净的链接测试页面</h1>\n    <p>这个页面没有任何复杂的JavaScript或CSS，用于测试基本的链接跳转。</p>\n\n    <div>\n        <h2>测试链接：</h2>\n        <a href=\"/admin/users\" class=\"test-link\">👥 用户管理 (普通链接)</a>\n        <a href=\"/admin/\" class=\"test-link\">🏠 管理后台首页</a>\n        <a href=\"/admin/articles\" class=\"test-link\">📄 文章管理</a>\n    </div>\n\n    <div>\n        <h2>强制跳转测试：</h2>\n        <button class=\"test-link\" onclick=\"window.location.href='/admin/users'\">🚀 强制跳转到用户管理</button>\n        <button class=\"test-link\" onclick=\"window.open('/admin/users', '_blank')\">🪟 新窗口打开用户管理</button>\n    </div>\n\n    <div id=\"log\" class=\"log\">\n        <h3>测试日志：</h3>\n        <div id=\"log-content\">页面加载完成，等待测试...</div>\n    </div>\n\n    <script>\n        function log(message) {\n            const logContent = document.getElementById('log-content');\n            const timestamp = new Date().toLocaleTimeString();\n            logContent.innerHTML += '<br>[' + timestamp + '] ' + message;\n        }\n\n        // 监听所有点击事件\n        document.addEventListener('click', function(e) {\n            if (e.target.tagName === 'A') {\n                log('点击链接: ' + e.target.href);\n                log('链接文本: ' + e.target.textContent);\n\n                // 检查是否被阻止\n                setTimeout(function() {\n                    if (window.location.href === e.target.href) {\n                        log('✅ 跳转成功');\n                    } else {\n                        log('❌ 跳转失败，当前URL: ' + window.location.href);\n                    }\n                }, 100);\n            } else if (e.target.tagName === 'BUTTON') {\n                log('点击按钮: ' + e.target.textContent);\n            }\n        });\n\n        // 监听页面跳转\n        window.addEventListener('beforeunload', function() {\n            log('页面即将跳转...');\n        });\n\n        log('JavaScript加载完成');\n    </script>\n</body>\n</html>\n    '''\n\n@admin_bp.route('/api/users', methods=['GET'])\n@login_required\n@admin_required\ndef get_users():\n    \"\"\"获取用户列表\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        \n        # 支持按角色和状态过滤\n        role = request.args.get('role')\n        status = request.args.get('status')\n        \n        users_query = User.query\n        \n        if role:\n            users_query = users_query.filter(User.role == role)\n            \n        if status:\n            users_query = users_query.filter(User.status == status)\n            \n        # 按创建时间倒序排列\n        users_query = users_query.order_by(User.created_at.desc())\n        \n        # 执行分页查询\n        pagination = users_query.paginate(page=page, per_page=per_page)\n        \n        # 格式化结果 - 包含完整的用户数据管理信息\n        users_data = []\n        for user in pagination.items:\n            # 获取用户财务信息\n            finance_info = {\n                'balance': 0,\n                'total_spent': 0,\n                'vip_level': 0,\n                'vip_expire_at': None,\n                'basic_quota_used': 0,\n                'premium_quota_used': 0,\n                'quota_reset_date': None,\n                'remaining_quotas': {'basic_remaining': 0, 'premium_remaining': 0, 'can_exchange': False, 'exchange_available': 0}\n            }\n\n            # 仅管理员可以看到财务信息\n            if user.finance and current_user.can_access_finance():\n                finance_info.update({\n                    'balance': user.finance.balance,\n                    'total_spent': user.finance.total_spent,\n                    'vip_level': user.finance.vip_level,\n                    'vip_expire_at': user.finance.vip_expire_at.strftime('%Y-%m-%d %H:%M:%S') if user.finance.vip_expire_at else None,\n                    'basic_quota_used': user.finance.basic_quota_used,\n                    'premium_quota_used': user.finance.premium_quota_used,\n                    'quota_reset_date': user.finance.quota_reset_date.strftime('%Y-%m-%d') if user.finance.quota_reset_date else None,\n                    'remaining_quotas': user.finance.get_remaining_quotas()\n                })\n\n            # 统计用户行为数据 - 使用安全的查询方式\n            try:\n                articles_count = user.articles.count() if hasattr(user, 'articles') and user.articles else 0\n            except:\n                articles_count = 0\n\n            try:\n                bookmarks_count = user.bookmarks.count() if hasattr(user, 'bookmarks') and user.bookmarks else 0\n            except:\n                bookmarks_count = 0\n\n            try:\n                favorites_count = user.favorites.count() if hasattr(user, 'favorites') and user.favorites else 0\n            except:\n                favorites_count = 0\n\n            try:\n                support_tickets_count = user.support_tickets.count() if hasattr(user, 'support_tickets') and user.support_tickets else 0\n            except:\n                support_tickets_count = 0\n\n            try:\n                # 使用数据库查询而不是关系属性\n                from models import ParagraphBookmark\n                paragraph_bookmarks_count = ParagraphBookmark.query.filter_by(user_id=user.id).count()\n            except:\n                paragraph_bookmarks_count = 0\n\n            user_stats = {\n                'articles_count': articles_count,\n                'bookmarks_count': bookmarks_count,\n                'favorites_count': favorites_count,\n                'support_tickets_count': support_tickets_count,\n                'paragraph_bookmarks_count': paragraph_bookmarks_count\n            }\n\n            users_data.append({\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'role': user.role,\n                'email_verified': user.email_verified,\n                'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else None,\n                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else None,\n                'finance': finance_info,\n                'stats': user_stats\n            })\n        \n        return jsonify({\n            'success': True,\n            'users': users_data,\n            'total': pagination.total,\n            'pages': pagination.pages,\n            'current_page': pagination.page\n        })\n    except Exception as e:\n        logger.error(f\"获取用户列表API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_users_stats():\n    \"\"\"获取用户统计信息API\"\"\"\n    try:\n        from datetime import datetime, timedelta\n        from sqlalchemy import func\n\n        # 基础统计\n        total_users = User.query.count()\n        active_users = User.query.filter(User.last_login.isnot(None)).count()\n        vip_users = User.query.filter(User.is_vip == True).count()\n\n        # 时间统计\n        today = datetime.now().date()\n        week_ago = today - timedelta(days=7)\n        month_ago = today - timedelta(days=30)\n\n        today_registered = User.query.filter(User.created_at >= today).count()\n        week_registered = User.query.filter(User.created_at >= week_ago).count()\n        month_registered = User.query.filter(User.created_at >= month_ago).count()\n\n        # 角色分布\n        role_stats = db.session.query(\n            User.role,\n            func.count(User.id).label('count')\n        ).group_by(User.role).all()\n\n        role_distribution = {stat.role: stat.count for stat in role_stats}\n\n        # 会员类型分布\n        membership_stats = db.session.query(\n            User.membership_type,\n            func.count(User.id).label('count')\n        ).group_by(User.membership_type).all()\n\n        membership_distribution = {\n            stat.membership_type or 'free': stat.count\n            for stat in membership_stats\n        }\n\n        # 注册趋势（最近7天）\n        registration_trend = []\n        for i in range(7):\n            date = today - timedelta(days=i)\n            count = User.query.filter(\n                func.date(User.created_at) == date\n            ).count()\n            registration_trend.append({\n                'date': date.strftime('%Y-%m-%d'),\n                'count': count\n            })\n        registration_trend.reverse()\n\n        stats = {\n            'total_users': total_users,\n            'active_users': active_users,\n            'vip_users': vip_users,\n            'today_registered': today_registered,\n            'this_week_registered': week_registered,\n            'this_month_registered': month_registered,\n            'role_distribution': role_distribution,\n            'membership_distribution': membership_distribution,\n            'registration_trend': registration_trend\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取用户统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>', methods=['GET'])\n@login_required\n@admin_required\ndef get_user(user_id):\n    \"\"\"获取单个用户详细信息\"\"\"\n    try:\n        from sqlalchemy.orm import joinedload\n        user = User.query.options(joinedload(User.finance)).filter_by(id=user_id).first()\n\n        if not user:\n            return jsonify({'success': False, 'error': '用户不存在'}), 404\n\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'role': user.role,\n            'email_verified': user.email_verified,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'last_login': user.last_login.isoformat() if user.last_login else None,\n            'finance': None\n        }\n\n        # 添加财务信息 - 仅管理员可见\n        if user.finance and current_user.can_access_finance():\n            user_data['finance'] = {\n                'vip_level': user.finance.vip_level,\n                'vip_expire_at': user.finance.vip_expire_at.isoformat() if user.finance.vip_expire_at else None,\n                'basic_quota': user.finance.basic_quota,\n                'basic_quota_used': user.finance.basic_quota_used,\n                'premium_quota': user.finance.premium_quota,\n                'premium_quota_used': user.finance.premium_quota_used,\n                'balance': float(user.finance.balance) if user.finance.balance else 0.0\n            }\n\n        return jsonify({'success': True, 'user': user_data})\n\n    except Exception as e:\n        logger.error(f\"获取用户信息失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>/details', methods=['GET'])\n@login_required\n@admin_required\ndef get_user_details(user_id):\n    \"\"\"获取用户详细信息，包括消费记录、推广信息等\"\"\"\n    try:\n        from sqlalchemy.orm import joinedload\n        from models import UserConsumptionRecord, UserReferralRecord, UserPointsRecord, Article\n\n        # 获取用户基本信息和财务信息\n        user = User.query.options(joinedload(User.finance)).filter_by(id=user_id).first()\n\n        if not user:\n            return jsonify({'success': False, 'error': '用户不存在'}), 404\n\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'role': user.role,\n            'email_verified': user.email_verified,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'last_login': user.last_login.isoformat() if user.last_login else None,\n            'finance': None,\n            'consumption_records': [],\n            'referral_rewards': [],\n            'referred_records': []\n        }\n\n        # 添加财务信息 - 仅管理员可见\n        if user.finance and current_user.can_access_finance():\n            user_data['finance'] = {\n                'vip_level': user.finance.vip_level,\n                'vip_expire_at': user.finance.vip_expire_at.isoformat() if user.finance.vip_expire_at else None,\n                'basic_quota': user.finance.basic_quota,\n                'basic_quota_used': user.finance.basic_quota_used,\n                'premium_quota': user.finance.premium_quota,\n                'premium_quota_used': user.finance.premium_quota_used,\n                'balance': float(user.finance.balance) if user.finance.balance else 0.0,\n                'points_balance': user.finance.points_balance,\n                'referral_code': user.finance.referral_code,\n                'referred_by': user.finance.referred_by,\n                'last_quota_reset': user.finance.last_quota_reset.isoformat() if user.finance.last_quota_reset else None\n            }\n\n        # 获取购买记录统计（Purchase表）\n        from models import Purchase\n        purchases = Purchase.query.filter_by(user_id=user_id).all()\n\n        # 购买统计\n        purchase_stats = {\n            'total_purchases': len(purchases),\n            'basic_purchases': len([p for p in purchases if p.content_type == 'basic']),\n            'premium_purchases': len([p for p in purchases if p.content_type == 'premium']),\n            'total_spent': sum(p.amount for p in purchases),\n            'cash_purchases': len([p for p in purchases if p.payment_method in ['alipay', 'wechat', 'mock']]),\n            'quota_purchases': len([p for p in purchases if p.payment_method == 'quota']),\n            'points_purchases': len([p for p in purchases if p.payment_method == 'points'])\n        }\n        user_data['purchase_stats'] = purchase_stats\n\n        # 最近购买记录（最多10条）\n        recent_purchases = Purchase.query.filter_by(user_id=user_id)\\\n                                        .order_by(Purchase.created_at.desc())\\\n                                        .limit(10).all()\n\n        user_data['recent_purchases'] = []\n        for purchase in recent_purchases:\n            article = Article.query.get(purchase.article_id)\n            user_data['recent_purchases'].append({\n                'id': purchase.id,\n                'article_title': article.title if article else '文章已删除',\n                'content_type': purchase.content_type,\n                'amount': purchase.amount,\n                'payment_method': purchase.payment_method,\n                'transaction_id': purchase.transaction_id,\n                'created_at': purchase.created_at.isoformat() if purchase.created_at else None\n            })\n\n        # 获取最近的消费记录（最多10条）\n        consumption_records = UserConsumptionRecord.query.filter_by(user_id=user_id)\\\n            .join(Article, UserConsumptionRecord.article_id == Article.id)\\\n            .order_by(UserConsumptionRecord.created_at.desc())\\\n            .limit(10).all()\n\n        for record in consumption_records:\n            user_data['consumption_records'].append({\n                'id': record.id,\n                'article_id': record.article_id,\n                'article_title': record.article.title if record.article else None,\n                'content_type': record.content_type,\n                'cost_type': record.cost_type,\n                'cost_amount': record.cost_amount,\n                'created_at': record.created_at.isoformat()\n            })\n\n        # 获取推广奖励记录\n        referral_rewards = UserReferralRecord.query.filter_by(referrer_id=user_id)\\\n            .order_by(UserReferralRecord.created_at.desc()).all()\n\n        for reward in referral_rewards:\n            user_data['referral_rewards'].append({\n                'id': reward.id,\n                'referred_id': reward.referred_id,\n                'reward_type': reward.reward_type,\n                'reward_amount': reward.reward_amount,\n                'trigger_event': reward.trigger_event,\n                'created_at': reward.created_at.isoformat()\n            })\n\n        # 获取被推广记录\n        referred_records = UserReferralRecord.query.filter_by(referred_id=user_id)\\\n            .order_by(UserReferralRecord.created_at.desc()).all()\n\n        for record in referred_records:\n            user_data['referred_records'].append({\n                'id': record.id,\n                'referrer_id': record.referrer_id,\n                'reward_type': record.reward_type,\n                'reward_amount': record.reward_amount,\n                'trigger_event': record.trigger_event,\n                'created_at': record.created_at.isoformat()\n            })\n\n        return jsonify({'success': True, 'user': user_data})\n\n    except Exception as e:\n        logger.error(f\"获取用户详细信息失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>', methods=['PUT'])\n@login_required\n@admin_required\ndef update_user(user_id):\n    \"\"\"更新用户信息\"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n        data = request.get_json()\n        \n        # 不允许修改超级管理员状态\n        if user.role == 'superadmin' and current_user.role != 'superadmin':\n            return jsonify({'success': False, 'error': '您没有权限修改超级管理员用户'}), 403\n        \n        # 不允许用户修改自己的角色（防止权限降级）\n        if user.id == current_user.id and 'role' in data and data['role'] != user.role:\n            return jsonify({'success': False, 'error': '不能修改自己的角色'}), 403\n        \n        if 'username' in data:\n            # 检查用户名是否已被使用\n            if data['username'] != user.username:\n                existing_user = User.query.filter_by(username=data['username']).first()\n                if existing_user:\n                    return jsonify({'success': False, 'error': '此用户名已被使用'}), 400\n            user.username = data['username']\n            \n        if 'email' in data:\n            # 检查邮箱是否已被使用\n            if data['email'] != user.email:\n                existing_user = User.query.filter_by(email=data['email']).first()\n                if existing_user:\n                    return jsonify({'success': False, 'error': '此邮箱已被使用'}), 400\n            user.email = data['email']\n            \n        if 'role' in data and current_user.role == 'superadmin':\n            user.role = data['role']\n            \n        if 'status' in data:\n            user.status = data['status']\n            \n        db.session.commit()\n        return jsonify({'success': True, 'message': '用户信息已更新'})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新用户 {user_id} 失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>/reset_password', methods=['POST'])\n@login_required\n@admin_required\ndef reset_user_password(user_id):\n    \"\"\"重置用户密码\"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n        data = request.get_json()\n        \n        # 超级管理员密码只能由超级管理员重置\n        if user.role == 'superadmin' and current_user.role != 'superadmin':\n            return jsonify({'success': False, 'error': '您没有权限重置超级管理员密码'}), 403\n        \n        new_password = data.get('new_password')\n        if not new_password or len(new_password) < 8:\n            return jsonify({'success': False, 'error': '新密码长度必须至少为8个字符'}), 400\n            \n        # 设置新密码\n        user.password = generate_password_hash(new_password)\n        \n        db.session.commit()\n        return jsonify({'success': True, 'message': '用户密码已重置'})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"重置用户 {user_id} 密码失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users', methods=['POST'])\n@login_required\n@admin_required\ndef create_user():\n    \"\"\"创建新用户\"\"\"\n    try:\n        data = request.get_json()\n        \n        username = data.get('username')\n        email = data.get('email')\n        password = data.get('password')\n        role = data.get('role', 'user')\n        \n        # 验证必填字段\n        if not username or not email or not password:\n            return jsonify({'success': False, 'error': '用户名、邮箱和密码都是必需的'}), 400\n            \n        # 验证密码长度\n        if len(password) < 8:\n            return jsonify({'success': False, 'error': '密码长度必须至少为8个字符'}), 400\n            \n        # 检查用户名是否已被使用\n        existing_user = User.query.filter_by(username=username).first()\n        if existing_user:\n            return jsonify({'success': False, 'error': '此用户名已被使用'}), 400\n            \n        # 检查邮箱是否已被使用\n        existing_user = User.query.filter_by(email=email).first()\n        if existing_user:\n            return jsonify({'success': False, 'error': '此邮箱已被使用'}), 400\n            \n        # 创建新用户\n        new_user = User(\n            username=username,\n            email=email,\n            password=generate_password_hash(password),\n            role=role,\n            status='active'\n        )\n        \n        db.session.add(new_user)\n        db.session.commit()\n        \n        return jsonify({\n            'success': True, \n            'message': '用户创建成功',\n            'user': {\n                'id': new_user.id,\n                'username': new_user.username,\n                'email': new_user.email,\n                'role': new_user.role,\n                'status': new_user.status\n            }\n        }), 201\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"创建用户失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>', methods=['DELETE'])\n@login_required\n@admin_required\ndef delete_user(user_id):\n    \"\"\"删除用户\"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n        \n        # 不允许删除超级管理员\n        if user.role == 'superadmin':\n            return jsonify({'success': False, 'error': '不能删除超级管理员用户'}), 403\n            \n        # 不允许删除自己\n        if user.id == current_user.id:\n            return jsonify({'success': False, 'error': '不能删除当前登录的用户账户'}), 403\n            \n        db.session.delete(user)\n        db.session.commit()\n        return jsonify({'success': True, 'message': '用户已删除'})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"删除用户 {user_id} 失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>/vip', methods=['PUT'])\n@login_required\n@finance_required\ndef update_user_vip(user_id):\n    \"\"\"更新用户VIP状态和配额\"\"\"\n    try:\n        from models import UserFinance\n        from datetime import datetime, timedelta\n\n        user = User.query.get_or_404(user_id)\n        data = request.get_json()\n\n        # 确保用户有财务记录\n        if not user.finance:\n            user_finance = UserFinance(user_id=user.id)\n            db.session.add(user_finance)\n            db.session.flush()  # 获取ID但不提交\n        else:\n            user_finance = user.finance\n\n        # 更新VIP等级\n        if 'vip_level' in data:\n            vip_level = int(data['vip_level'])\n            if vip_level not in [0, 1, 2]:\n                return jsonify({'success': False, 'error': 'VIP等级必须是0、1或2'}), 400\n            user_finance.vip_level = vip_level\n\n            # 如果设置为VIP，自动设置过期时间（如果没有提供）\n            if vip_level > 0 and 'vip_expire_at' not in data:\n                # 默认设置为1个月后过期\n                user_finance.vip_expire_at = datetime.utcnow() + timedelta(days=30)\n\n        # 更新VIP过期时间\n        if 'vip_expire_at' in data and data['vip_expire_at']:\n            try:\n                user_finance.vip_expire_at = datetime.strptime(data['vip_expire_at'], '%Y-%m-%d %H:%M:%S')\n            except ValueError:\n                try:\n                    user_finance.vip_expire_at = datetime.strptime(data['vip_expire_at'], '%Y-%m-%d')\n                except ValueError:\n                    return jsonify({'success': False, 'error': 'VIP过期时间格式错误，请使用YYYY-MM-DD或YYYY-MM-DD HH:MM:SS'}), 400\n\n        # 重置配额\n        if data.get('reset_quota', False):\n            user_finance.basic_quota_used = 0\n            user_finance.premium_quota_used = 0\n            user_finance.quota_reset_date = datetime.utcnow()\n\n        # 更新配额总量\n        if 'basic_quota' in data:\n            user_finance.basic_quota = max(0, int(data['basic_quota']))\n\n        if 'premium_quota' in data:\n            user_finance.premium_quota = max(0, int(data['premium_quota']))\n\n        # 手动调整配额使用量\n        if 'basic_quota_used' in data:\n            user_finance.basic_quota_used = max(0, int(data['basic_quota_used']))\n\n        if 'premium_quota_used' in data:\n            user_finance.premium_quota_used = max(0, int(data['premium_quota_used']))\n\n        # 更新余额\n        if 'balance' in data:\n            user_finance.balance = float(data['balance'])\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '用户VIP状态已更新',\n            'finance': {\n                'vip_level': user_finance.vip_level,\n                'vip_expire_at': user_finance.vip_expire_at.strftime('%Y-%m-%d %H:%M:%S') if user_finance.vip_expire_at else None,\n                'basic_quota': user_finance.basic_quota,\n                'basic_quota_used': user_finance.basic_quota_used,\n                'premium_quota': user_finance.premium_quota,\n                'premium_quota_used': user_finance.premium_quota_used,\n                'balance': float(user_finance.balance) if user_finance.balance else 0.0\n            }\n        })\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新用户 {user_id} VIP状态失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# --- 公告管理 ---\n@admin_bp.route('/announcements')\n@login_required\n@admin_required\ndef announcement_list():\n    \"\"\"公告管理页面\"\"\"\n    try:\n        return render_template('admin/announcement_list.html')\n    except Exception as e:\n        logger.error(f\"加载公告管理页面失败: {str(e)}\")\n        flash('加载公告管理页面失败', 'error')\n        return redirect(url_for('admin.dashboard'))\n\n@admin_bp.route('/api/announcements', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_announcements():\n    \"\"\"获取公告列表API\"\"\"\n    try:\n        from models import Announcement\n        announcements = Announcement.query.order_by(Announcement.created_at.desc()).all()\n\n        result = []\n        for announcement in announcements:\n            result.append({\n                'id': announcement.id,\n                'title': announcement.title,\n                'content': announcement.content,\n                'priority': announcement.priority,\n                'is_active': announcement.is_active,\n                'start_date': announcement.start_date.isoformat() if announcement.start_date else None,\n                'end_date': announcement.end_date.isoformat() if announcement.end_date else None,\n                'created_at': announcement.created_at.isoformat() if announcement.created_at else None\n            })\n\n        return jsonify({'success': True, 'data': result})\n    except Exception as e:\n        logger.error(f\"获取公告列表失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_announcements_stats():\n    \"\"\"获取公告统计信息API\"\"\"\n    try:\n        from models import Announcement\n        from datetime import datetime, timedelta\n        from sqlalchemy import func\n\n        # 基础统计\n        total_announcements = Announcement.query.count()\n\n        # 活跃公告（未过期的）\n        now = datetime.now()\n        active_announcements = Announcement.query.filter(\n            Announcement.is_active == True,\n            Announcement.end_date >= now\n        ).count()\n\n        # 过期公告\n        expired_announcements = Announcement.query.filter(\n            Announcement.end_date < now\n        ).count()\n\n        # 时间统计\n        today = datetime.now().date()\n        week_ago = today - timedelta(days=7)\n\n        today_created = Announcement.query.filter(\n            func.date(Announcement.created_at) == today\n        ).count()\n\n        week_created = Announcement.query.filter(\n            Announcement.created_at >= week_ago\n        ).count()\n\n        # 公告类型分布（这里使用模拟数据，实际应该从数据库字段获取）\n        announcement_types = {\n            'system': total_announcements // 3,\n            'promotion': total_announcements // 3,\n            'maintenance': total_announcements - (total_announcements // 3) * 2\n        }\n\n        # 浏览统计（模拟数据，实际应该从浏览记录表获取）\n        total_views = total_announcements * 1028  # 模拟总浏览量\n        avg_views_per_announcement = 1028 if total_announcements > 0 else 0\n\n        stats = {\n            'total_announcements': total_announcements,\n            'active_announcements': active_announcements,\n            'expired_announcements': expired_announcements,\n            'today_created': today_created,\n            'this_week_created': week_created,\n            'announcement_types': announcement_types,\n            'view_stats': {\n                'total_views': total_views,\n                'avg_views_per_announcement': avg_views_per_announcement\n            }\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取公告统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements', methods=['POST'])\n@login_required\n@admin_required\ndef api_create_announcement():\n    \"\"\"创建公告API\"\"\"\n    try:\n        from models import Announcement\n        data = request.get_json()\n\n        announcement = Announcement(\n            title=data.get('title'),\n            content=data.get('content'),\n            priority=data.get('priority', 'normal'),\n            is_active=data.get('is_active', True),\n            start_date=datetime.strptime(data.get('start_date'), '%Y-%m-%dT%H:%M') if data.get('start_date') else None,\n            end_date=datetime.strptime(data.get('end_date'), '%Y-%m-%dT%H:%M') if data.get('end_date') else None\n        )\n\n        db.session.add(announcement)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '公告创建成功'})\n    except Exception as e:\n        logger.error(f\"创建公告失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_announcement(announcement_id):\n    \"\"\"获取单个公告API\"\"\"\n    try:\n        from models import Announcement\n        announcement = Announcement.query.get_or_404(announcement_id)\n\n        result = {\n            'id': announcement.id,\n            'title': announcement.title,\n            'content': announcement.content,\n            'priority': announcement.priority,\n            'is_active': announcement.is_active,\n            'start_date': announcement.start_date.isoformat() if announcement.start_date else None,\n            'end_date': announcement.end_date.isoformat() if announcement.end_date else None,\n            'created_at': announcement.created_at.isoformat() if announcement.created_at else None\n        }\n\n        return jsonify({'success': True, 'data': result})\n    except Exception as e:\n        logger.error(f\"获取公告失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_announcement(announcement_id):\n    \"\"\"更新公告API\"\"\"\n    try:\n        from models import Announcement\n        announcement = Announcement.query.get_or_404(announcement_id)\n        data = request.get_json()\n\n        announcement.title = data.get('title', announcement.title)\n        announcement.content = data.get('content', announcement.content)\n        announcement.priority = data.get('priority', announcement.priority)\n        announcement.is_active = data.get('is_active', announcement.is_active)\n\n        if data.get('start_date'):\n            announcement.start_date = datetime.strptime(data.get('start_date'), '%Y-%m-%dT%H:%M')\n        if data.get('end_date'):\n            announcement.end_date = datetime.strptime(data.get('end_date'), '%Y-%m-%dT%H:%M')\n\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '公告更新成功'})\n    except Exception as e:\n        logger.error(f\"更新公告失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['DELETE'])\n@login_required\n@admin_required\ndef api_delete_announcement(announcement_id):\n    \"\"\"删除公告API\"\"\"\n    try:\n        from models import Announcement\n        announcement = Announcement.query.get_or_404(announcement_id)\n\n        db.session.delete(announcement)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '公告删除成功'})\n    except Exception as e:\n        logger.error(f\"删除公告失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# --- 积分管理 ---\n@admin_bp.route('/points')\n@login_required\n@admin_required\ndef points_management():\n    \"\"\"积分管理页面\"\"\"\n    try:\n        return render_template('admin/points_management.html')\n    except Exception as e:\n        logger.error(f\"加载积分管理页面失败: {str(e)}\")\n        flash('加载积分管理页面失败', 'error')\n        return redirect(url_for('admin.dashboard'))\n\n@admin_bp.route('/api/points/transactions', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_point_transactions():\n    \"\"\"获取积分交易记录API\"\"\"\n    try:\n        from models import PointTransaction\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n\n        transactions = PointTransaction.query.order_by(PointTransaction.created_at.desc()).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        result = []\n        for transaction in transactions.items:\n            result.append({\n                'id': transaction.id,\n                'user_id': transaction.user_id,\n                'username': transaction.user.username if transaction.user else '未知用户',\n                'points': transaction.points,\n                'transaction_type': transaction.transaction_type,\n                'description': transaction.description,\n                'created_at': transaction.created_at.isoformat() if transaction.created_at else None\n            })\n\n        return jsonify({\n            'success': True,\n            'data': result,\n            'pagination': {\n                'page': transactions.page,\n                'pages': transactions.pages,\n                'per_page': transactions.per_page,\n                'total': transactions.total\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取积分交易记录失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/points/adjust', methods=['POST'])\n@login_required\n@admin_required\ndef api_adjust_user_points():\n    \"\"\"调整用户积分API\"\"\"\n    try:\n        from models import PointTransaction\n        data = request.get_json()\n\n        user_id = data.get('user_id')\n        points = data.get('points')\n        description = data.get('description', '管理员调整')\n\n        user = User.query.get_or_404(user_id)\n\n        # 创建积分交易记录\n        transaction = PointTransaction(\n            user_id=user_id,\n            points=points,\n            transaction_type='admin_adjust',\n            description=description\n        )\n\n        # 更新用户积分\n        user.points = (user.points or 0) + points\n\n        db.session.add(transaction)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '积分调整成功'})\n    except Exception as e:\n        logger.error(f\"调整用户积分失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/points/statistics', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_points_statistics():\n    \"\"\"获取积分统计信息API\"\"\"\n    try:\n        from models import PointTransaction\n        from sqlalchemy import func\n        from datetime import date\n\n        # 总积分发放（正数）\n        total_issued = db.session.query(func.sum(PointTransaction.points)).filter(\n            PointTransaction.points > 0\n        ).scalar() or 0\n\n        # 总积分消费（负数的绝对值）\n        total_spent = db.session.query(func.sum(PointTransaction.points)).filter(\n            PointTransaction.points < 0\n        ).scalar() or 0\n        total_spent = abs(total_spent)\n\n        # 有积分的用户数\n        active_users = db.session.query(func.count(func.distinct(User.id))).filter(\n            User.points > 0\n        ).scalar() or 0\n\n        # 今日交易数\n        today = date.today()\n        today_transactions = PointTransaction.query.filter(\n            func.date(PointTransaction.created_at) == today\n        ).count()\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'total_issued': total_issued,\n                'total_spent': total_spent,\n                'active_users': active_users,\n                'today_transactions': today_transactions\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取积分统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/search', methods=['GET'])\n@login_required\n@admin_required\ndef api_search_users():\n    \"\"\"搜索用户API\"\"\"\n    try:\n        query = request.args.get('q', '').strip()\n        if len(query) < 2:\n            return jsonify({'success': True, 'data': []})\n\n        # 搜索用户名或邮箱\n        users = User.query.filter(\n            db.or_(\n                User.username.ilike(f'%{query}%'),\n                User.email.ilike(f'%{query}%')\n            )\n        ).limit(10).all()\n\n        result = []\n        for user in users:\n            result.append({\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'points': user.points or 0\n            })\n\n        return jsonify({'success': True, 'data': result})\n    except Exception as e:\n        logger.error(f\"搜索用户失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# --- 文章媒体文件管理 ---\n@admin_bp.route('/api/articles/<int:article_id>', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_article_info(article_id):\n    \"\"\"获取文章完整信息API - 用于Vue3管理后台编辑页面\"\"\"\n    try:\n        article = Article.query.get_or_404(article_id)\n\n        return jsonify({\n            'success': True,\n            'article': {\n                'id': article.id,\n                'title': article.title,\n                'content': article.content,\n                'premium_content': article.premium_content,\n                'status': article.status,\n                'reader_id': article.reader_id,\n                'author_id': article.author_id,\n                'tags': [{'id': tag.id, 'name': tag.name} for tag in article.tags] if article.tags else [],\n                'publish_date': article.publish_date.isoformat() if article.publish_date else None,\n                'language_code': article.language_code,\n                'content_type': article.content_type,\n                'price': article.price,\n                'premium_price': article.premium_price,\n                'cover_image': article.cover_image,\n                'cover_url': article.cover_url,\n                'created_at': article.created_at.isoformat() if article.created_at else None,\n                'updated_at': article.updated_at.isoformat() if article.updated_at else None,\n                'views': article.views,\n                'original_id': article.original_id,\n                'author': article.author,\n                'original_audio_url': article.original_audio_url,\n                'content_images': article.content_images,\n                'audio_sync_data': article.audio_sync_data\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取文章信息失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/media', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_article_media(article_id):\n    \"\"\"获取文章关联的媒体文件API\"\"\"\n    try:\n        from models import MediaFile\n        article = Article.query.get_or_404(article_id)\n\n        # 获取与文章关联的媒体文件\n        media_files = MediaFile.query.filter_by(article_id=article_id).order_by(MediaFile.created_at.desc()).all()\n\n        # 按文件类型分组\n        audio_files = []\n        subtitle_files = []\n        image_files = []\n\n        for file in media_files:\n            file_dict = file.to_dict()\n            if file.file_type == 'audio':\n                audio_files.append(file_dict)\n            elif file.file_type == 'subtitle':\n                subtitle_files.append(file_dict)\n            elif file.file_type == 'image':\n                image_files.append(file_dict)\n\n        # 如果没有字幕文件记录，尝试从audio_sync_data中提取\n        if not subtitle_files and article.audio_sync_data:\n            try:\n                import json\n                sync_data = json.loads(article.audio_sync_data)\n                subtitle_url = sync_data.get('subtitle_url')\n                if subtitle_url and article.original_id:\n                    # 构建虚拟字幕文件记录\n                    subtitle_files.append({\n                        'id': f'virtual_{article.original_id}_subtitle',\n                        'filename': f'{article.original_id}_subtitle.srt',\n                        'original_filename': f'{article.original_id}_subtitle.srt',\n                        'file_type': 'subtitle',\n                        'file_category': 'subtitle',\n                        'url': subtitle_url,\n                        'upload_status': 'uploaded',\n                        'created_at': article.created_at.isoformat() if article.created_at else None\n                    })\n                    logger.info(f\"从audio_sync_data中提取到字幕文件: {subtitle_url}\")\n            except (json.JSONDecodeError, Exception) as e:\n                logger.warning(f\"解析audio_sync_data失败: {str(e)}\")\n\n        # 如果没有图片文件记录，尝试从content_images中提取\n        if not image_files and article.content_images:\n            try:\n                import json\n                image_urls = json.loads(article.content_images)\n                for i, image_url in enumerate(image_urls, 1):\n                    if image_url and image_url.strip():\n                        image_files.append({\n                            'id': f'virtual_{article.original_id}_image_{i:02d}',\n                            'filename': f'{article.original_id}_image_{i:02d}.jpg',\n                            'original_filename': f'{article.original_id}_image_{i:02d}.jpg',\n                            'file_type': 'image',\n                            'file_category': 'content',\n                            'url': image_url,\n                            'upload_status': 'uploaded',\n                            'created_at': article.created_at.isoformat() if article.created_at else None\n                        })\n                logger.info(f\"从content_images中提取到{len(image_files)}个图片文件\")\n            except (json.JSONDecodeError, Exception) as e:\n                logger.warning(f\"解析content_images失败: {str(e)}\")\n\n        # 如果没有音频文件记录，尝试从original_audio_url中提取\n        if not audio_files and article.original_audio_url and article.original_id:\n            audio_files.append({\n                'id': f'virtual_{article.original_id}_audio',\n                'filename': f'{article.original_id}_audio.mp3',\n                'original_filename': f'{article.original_id}_audio.mp3',\n                'file_type': 'audio',\n                'file_category': 'original_audio',\n                'url': article.original_audio_url,\n                'upload_status': 'uploaded',\n                'created_at': article.created_at.isoformat() if article.created_at else None\n            })\n            logger.info(f\"从original_audio_url中提取到音频文件: {article.original_audio_url}\")\n\n        # 如果没有封面文件记录，尝试从cover_url中提取\n        cover_files = []\n        cover_media_files = [f for f in media_files if f.file_category == 'cover']\n        if not cover_media_files and article.cover_url and article.original_id:\n            cover_files.append({\n                'id': f'virtual_{article.original_id}_cover',\n                'filename': f'{article.original_id}_cover.jpg',\n                'original_filename': f'{article.original_id}_cover.jpg',\n                'file_type': 'image',\n                'file_category': 'cover',\n                'url': article.cover_url,\n                'upload_status': 'uploaded',\n                'created_at': article.created_at.isoformat() if article.created_at else None\n            })\n            logger.info(f\"从cover_url中提取到封面文件: {article.cover_url}\")\n        else:\n            # 如果有MediaFile记录的封面，使用它们\n            for file in cover_media_files:\n                cover_files.append(file.to_dict())\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'audio': audio_files,\n                'subtitle': subtitle_files,\n                'image': image_files,\n                'cover': cover_files\n            },\n            'article': {\n                'id': article.id,\n                'title': article.title,\n                'original_id': article.original_id\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取文章媒体文件失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/media/upload', methods=['POST'])\n@login_required\n@staff_required\ndef api_upload_article_media(article_id):\n    \"\"\"为文章上传媒体文件API\"\"\"\n    try:\n        from models import MediaFile\n        article = Article.query.get_or_404(article_id)\n\n        if 'file' not in request.files:\n            return jsonify({'success': False, 'error': '没有选择文件'}), 400\n\n        file = request.files['file']\n        if file.filename == '':\n            return jsonify({'success': False, 'error': '没有选择文件'}), 400\n\n        # 这里应该调用R2上传功能\n        # 暂时创建媒体文件记录\n        media_file = MediaFile(\n            article_id=article_id,\n            original_id=article.original_id,\n            filename=file.filename,\n            original_filename=file.filename,\n            file_type='image',  # 需要根据文件扩展名判断\n            url='https://example.com/placeholder.jpg',  # 临时URL\n            r2_key='placeholder'\n        )\n\n        db.session.add(media_file)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '文件上传成功', 'data': media_file.to_dict()})\n    except Exception as e:\n        logger.error(f\"上传文章媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/media/<int:file_id>', methods=['DELETE'])\n@login_required\n@staff_required\ndef api_delete_media_file(file_id):\n    \"\"\"删除媒体文件API\"\"\"\n    try:\n        from models import MediaFile\n        file = MediaFile.query.get_or_404(file_id)\n\n        # 这里应该同时删除R2上的文件\n        db.session.delete(file)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '文件删除成功'})\n    except Exception as e:\n        logger.error(f\"删除媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n\n# --- 音频同步功能管理 ---\n@admin_bp.route('/audio-sync-test')\n@login_required\n@admin_required\ndef audio_sync_test():\n    \"\"\"音频同步测试页面\"\"\"\n    # 查找测试文章\n    test_article = Article.query.filter_by(title=\"【测试】惊天预兆！即将发生的事！\").first()\n\n    return render_template('audio_sync_test.html', article=test_article)\n\n@admin_bp.route('/api/media/match-by-original-id', methods=['POST'])\n@login_required\n@admin_required\ndef api_match_media_by_original_id():\n    \"\"\"根据原始ID匹配媒体文件到文章API\"\"\"\n    try:\n        from models import MediaFile\n        data = request.get_json()\n        original_id = data.get('original_id')\n\n        if not original_id:\n            return jsonify({'success': False, 'error': '缺少原始ID'}), 400\n\n        # 查找具有该原始ID的文章\n        article = Article.query.filter_by(original_id=original_id).first()\n        if not article:\n            return jsonify({'success': False, 'error': f'未找到原始ID为 {original_id} 的文章'}), 404\n\n        # 查找未关联的媒体文件（通过文件名中的OID匹配）\n        unmatched_files = MediaFile.query.filter(\n            MediaFile.article_id.is_(None),\n            MediaFile.original_filename.like(f'%OID{original_id}%')\n        ).all()\n\n        matched_count = 0\n        for file in unmatched_files:\n            file.article_id = article.id\n            file.original_id = original_id\n            matched_count += 1\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': f'成功匹配 {matched_count} 个媒体文件到文章 \"{article.title}\"',\n            'matched_count': matched_count\n        })\n    except Exception as e:\n        logger.error(f\"匹配媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/tickets/export')\n@login_required\n@staff_required\ndef export_tickets():\n    \"\"\"导出工单API\"\"\"\n    try:\n        from models import SupportTicket, User\n        from datetime import datetime\n        from flask import make_response\n        import csv\n        import io\n\n        # 获取参数\n        start_date = request.args.get('start_date')\n        end_date = request.args.get('end_date')\n        status = request.args.get('status')\n        format_type = request.args.get('format', 'txt')\n\n        if not start_date or not end_date:\n            return jsonify({'success': False, 'error': '请提供开始和结束日期'}), 400\n\n        try:\n            start_dt = datetime.strptime(start_date, '%Y-%m-%d')\n            end_dt = datetime.strptime(end_date, '%Y-%m-%d')\n            # 结束日期包含当天的所有时间\n            end_dt = end_dt.replace(hour=23, minute=59, second=59)\n        except ValueError:\n            return jsonify({'success': False, 'error': '日期格式错误，请使用YYYY-MM-DD格式'}), 400\n\n        # 构建查询 - 明确指定join条件\n        query = SupportTicket.query.join(User, SupportTicket.user_id == User.id).filter(\n            SupportTicket.created_at >= start_dt,\n            SupportTicket.created_at <= end_dt\n        )\n\n        if status:\n            query = query.filter(SupportTicket.status == status)\n\n        tickets = query.order_by(SupportTicket.created_at.asc()).all()\n\n        if format_type == 'txt':\n            # TXT格式 - 适合AI处理\n            output = io.StringIO()\n\n            # 写入AI指令模板\n            ai_template = \"\"\"请根据以下工单信息生成专业的客服回复，严格按照以下要求：\n\n【格式要求】\n1. 每行一个回复，格式为：工单ID|回复内容\n2. 工单ID必须与下方工单信息中的ID完全一致\n3. 回复内容要专业、友好、有针对性\n4. 不要包含标题行、说明文字或其他格式\n5. 直接输出可导入系统的格式\n\n【网站介绍】\n本站提供基础翻译和高级翻译服务。基础翻译包含常规文章内容，所有用户都可以阅读。高级翻译包含更详细的内容、原文对照、音频朗读等功能，需要VIP权限才能访问。VIP用户每月获得一定的阅读配额。\n\n【回复原则】\n- 充值/支付问题：请等待客服上线处理，我们会尽快为您解决充值相关问题\n\n- VIP会员问题：VIP权限激活需要1-3分钟，建议重新登录或清除浏览器缓存，如仍有问题请等待客服处理\n\n- 配额问题：基础翻译免费阅读，高级翻译需要消耗VIP配额。VIP用户每月有固定配额，可在个人中心查看剩余额度\n\n- 文章阅读问题：先记录问题，如果着急可以查看相关视频或原文（高级内容中有原文对照）\n\n- 技术故障：我们已收集您的反馈并转交技术团队，正在积极解决中，请耐心等待\n\n- 其他问题：请等待客服上线为您详细解答，或查看网站帮助文档\n\n【示例格式】\n123|您好，我们已经查看了您的账户，余额显示异常是由于系统同步延迟造成的。您的充值已经到账，请刷新页面查看。\n456|您好，感谢您购买VIP会员。请先退出账户重新登录，然后清除浏览器缓存。VIP权限需要几分钟时间同步。\n\n【工单信息】\n格式：工单ID|用户名|主题|内容|状态|创建时间\n\n\"\"\"\n            output.write(ai_template)\n\n            # 写入工单数据\n            for ticket in tickets:\n                # 格式：工单ID|用户名|主题|内容|状态|创建时间\n                content = ticket.content.replace('\\n', ' ').replace('\\r', ' ')\n                subject = ticket.subject.replace('\\n', ' ').replace('\\r', ' ')\n                line = f\"{ticket.id}|{ticket.user.username}|{subject}|{content}|{ticket.status}|{ticket.created_at.strftime('%Y-%m-%d %H:%M:%S')}\\n\"\n                output.write(line)\n\n            response = make_response(output.getvalue())\n            response.headers['Content-Type'] = 'text/plain; charset=utf-8'\n            response.headers['Content-Disposition'] = f'attachment; filename=tickets_{start_date}_to_{end_date}.txt'\n\n        else:  # CSV格式\n            output = io.StringIO()\n            writer = csv.writer(output)\n\n            # 写入标题行\n            writer.writerow(['工单ID', '用户名', '主题', '内容', '分类', '状态', '优先级', '创建时间', '最后更新'])\n\n            # 写入数据行\n            for ticket in tickets:\n                writer.writerow([\n                    ticket.id,\n                    ticket.user.username,\n                    ticket.subject,\n                    ticket.content,\n                    ticket.category or '',\n                    ticket.status,\n                    ticket.priority,\n                    ticket.created_at.strftime('%Y-%m-%d %H:%M:%S'),\n                    ticket.updated_at.strftime('%Y-%m-%d %H:%M:%S')\n                ])\n\n            response = make_response(output.getvalue())\n            response.headers['Content-Type'] = 'text/csv; charset=utf-8'\n            response.headers['Content-Disposition'] = f'attachment; filename=tickets_{start_date}_to_{end_date}.csv'\n\n        return response\n\n    except Exception as e:\n        logger.error(f\"导出工单失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'导出失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/tickets/import-replies', methods=['POST'])\n@login_required\n@staff_required\ndef import_ticket_replies():\n    \"\"\"导入工单回复API\"\"\"\n    try:\n        from models import SupportTicket, SupportMessage\n        from datetime import datetime\n\n        # 检查文件\n        if 'file' not in request.files:\n            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400\n\n        file = request.files['file']\n        if file.filename == '':\n            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400\n\n        if not file.filename.endswith('.txt'):\n            return jsonify({'success': False, 'error': '只支持TXT格式文件'}), 400\n\n        auto_close = request.form.get('auto_close') == 'true'\n\n        # 读取文件内容\n        try:\n            content = file.read().decode('utf-8')\n        except UnicodeDecodeError:\n            try:\n                content = file.read().decode('gbk')\n            except UnicodeDecodeError:\n                return jsonify({'success': False, 'error': '文件编码不支持，请使用UTF-8或GBK编码'}), 400\n\n        lines = content.strip().split('\\n')\n        processed = 0\n        success_count = 0\n        error_count = 0\n        errors = []\n\n        for line_num, line in enumerate(lines, 1):\n            line = line.strip()\n            if not line:\n                continue\n\n            # 跳过AI指令模板和说明文字\n            if (line.startswith('请根据以下工单信息') or\n                line.startswith('【') or\n                line.startswith('-') or\n                line.startswith('格式：') or\n                line.startswith('1.') or\n                line.startswith('2.') or\n                line.startswith('3.') or\n                line.startswith('4.') or\n                line.startswith('5.') or\n                '格式要求' in line or\n                '回复原则' in line or\n                '示例格式' in line or\n                '工单信息' in line or\n                not line or\n                line.isspace()):\n                continue\n\n            # 跳过原始工单数据行（包含多个|分隔符且格式为：ID|用户名|主题|内容|状态|时间）\n            parts_check = line.split('|')\n            if len(parts_check) >= 6:  # 原始工单数据有6个字段\n                # 检查最后一个字段是否像时间格式\n                last_field = parts_check[-1].strip()\n                if ('-' in last_field and ':' in last_field and len(last_field) >= 10):\n                    continue  # 跳过原始工单数据行\n\n            processed += 1\n\n            # 解析格式：工单ID|回复内容\n            parts = line.split('|', 1)\n            if len(parts) != 2:\n                # 检查是否是纯数字开头的有效回复行\n                if not (parts[0].strip().isdigit() if parts else False):\n                    # 不是有效的回复行，跳过但不计入错误\n                    processed -= 1\n                    continue\n                error_count += 1\n                errors.append(f\"第{line_num}行格式错误：{line}\")\n                continue\n\n            try:\n                ticket_id = int(parts[0])\n                reply_content = parts[1].strip()\n\n                if not reply_content:\n                    error_count += 1\n                    errors.append(f\"第{line_num}行回复内容为空\")\n                    continue\n\n                # 查找工单\n                ticket = SupportTicket.query.get(ticket_id)\n                if not ticket:\n                    error_count += 1\n                    errors.append(f\"第{line_num}行工单ID {ticket_id} 不存在\")\n                    continue\n\n                # 创建回复消息\n                message = SupportMessage(\n                    ticket_id=ticket.id,\n                    message=reply_content,\n                    is_staff_reply=True,\n                    staff_id=current_user.id,\n                    created_at=datetime.utcnow()\n                )\n                db.session.add(message)\n\n                # 更新工单状态\n                ticket.admin_reply = reply_content\n                ticket.admin_id = current_user.id\n                ticket.updated_at = datetime.utcnow()\n\n                if auto_close:\n                    ticket.status = 'closed'\n                    ticket.resolved_at = datetime.utcnow()\n                else:\n                    ticket.status = 'processing'\n\n                success_count += 1\n\n            except ValueError:\n                error_count += 1\n                errors.append(f\"第{line_num}行工单ID格式错误：{parts[0]}\")\n                continue\n            except Exception as e:\n                error_count += 1\n                errors.append(f\"第{line_num}行处理失败：{str(e)}\")\n                continue\n\n        # 提交数据库更改\n        try:\n            db.session.commit()\n        except Exception as e:\n            db.session.rollback()\n            return jsonify({'success': False, 'error': f'数据库保存失败: {str(e)}'}), 500\n\n        result = {\n            'success': True,\n            'processed': processed,\n            'success_count': success_count,\n            'error_count': error_count\n        }\n\n        if errors:\n            result['errors'] = errors[:10]  # 只返回前10个错误\n\n        return jsonify(result)\n\n    except Exception as e:\n        logger.error(f\"导入工单回复失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': f'导入失败: {str(e)}'}), 500\n\n# ==================== 媒体文件管理 API ====================\n\n@admin_bp.route('/api/articles/<int:article_id>/media', methods=['GET'])\n@login_required\ndef get_article_media(article_id):\n    \"\"\"获取文章的媒体文件列表\"\"\"\n    if not current_user.is_admin():\n        return jsonify({'success': False, 'error': '权限不足'}), 403\n\n    try:\n        # 验证文章存在\n        article = Article.query.get_or_404(article_id)\n\n        # 获取关联的媒体文件\n        media_files = MediaFile.query.filter_by(article_id=article_id).all()\n\n        # 按类型分组\n        audio_files = []\n        image_files = []\n\n        for media in media_files:\n            file_data = {\n                'id': media.id,\n                'filename': media.filename,\n                'original_filename': media.original_filename,\n                'file_type': media.file_type,\n                'file_category': media.file_category,\n                'file_size': media.file_size,\n                'url': media.url,\n                'created_at': media.created_at.isoformat() if media.created_at else None\n            }\n\n            if media.file_type == 'audio':\n                audio_files.append(file_data)\n            elif media.file_type == 'image':\n                image_files.append(file_data)\n\n        return jsonify({\n            'success': True,\n            'audio_files': audio_files,\n            'image_files': image_files\n        })\n\n    except Exception as e:\n        logger.error(f\"获取文章媒体文件失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/media', methods=['POST'])\n@login_required\ndef upload_article_media(article_id):\n    \"\"\"上传文章媒体文件\"\"\"\n    if not current_user.is_admin():\n        return jsonify({'success': False, 'error': '权限不足'}), 403\n\n    try:\n        # 验证文章存在\n        article = Article.query.get_or_404(article_id)\n\n        # 检查文件\n        if 'file' not in request.files:\n            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400\n\n        file = request.files['file']\n        if file.filename == '':\n            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400\n\n        file_type = request.form.get('file_type', 'image')\n        file_category = request.form.get('file_category', 'content')\n\n        # 验证文件类型\n        if file_type == 'audio':\n            allowed_extensions = {'.mp3', '.wav', '.m4a', '.aac', '.ogg'}\n        elif file_type == 'image':\n            allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}\n        else:\n            return jsonify({'success': False, 'error': '不支持的文件类型'}), 400\n\n        file_ext = os.path.splitext(file.filename)[1].lower()\n        if file_ext not in allowed_extensions:\n            return jsonify({'success': False, 'error': f'不支持的文件格式: {file_ext}'}), 400\n\n        # 生成唯一文件名\n        unique_filename = f\"{uuid.uuid4()}{file_ext}\"\n\n        # 这里应该上传到R2存储，暂时返回模拟数据\n        # TODO: 实现实际的R2上传逻辑\n        file_url = f\"https://your-r2-domain.com/{unique_filename}\"\n\n        # 创建媒体文件记录\n        media_file = MediaFile(\n            article_id=article_id,\n            filename=unique_filename,\n            original_filename=file.filename,\n            file_type=file_type,\n            file_category=file_category,\n            file_size=len(file.read()),\n            url=file_url,\n            r2_key=unique_filename,\n            created_at=datetime.utcnow()\n        )\n\n        db.session.add(media_file)\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '文件上传成功',\n            'file': {\n                'id': media_file.id,\n                'filename': media_file.filename,\n                'original_filename': media_file.original_filename,\n                'file_type': media_file.file_type,\n                'file_size': media_file.file_size,\n                'url': media_file.url\n            }\n        })\n\n    except Exception as e:\n        logger.error(f\"上传媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/media/<int:media_id>', methods=['DELETE'])\n@login_required\ndef delete_media_file(media_id):\n    \"\"\"删除媒体文件\"\"\"\n    if not current_user.is_admin():\n        return jsonify({'success': False, 'error': '权限不足'}), 403\n\n    try:\n        media_file = MediaFile.query.get_or_404(media_id)\n\n        # TODO: 从R2存储中删除实际文件\n        # delete_from_r2(media_file.r2_key)\n\n        db.session.delete(media_file)\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '文件删除成功'\n        })\n\n    except Exception as e:\n        logger.error(f\"删除媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': f'删除失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/audio-sync', methods=['PUT'])\n@login_required\ndef save_audio_sync_config(article_id):\n    \"\"\"保存音频同步配置\"\"\"\n    if not current_user.is_admin():\n        return jsonify({'success': False, 'error': '权限不足'}), 403\n\n    try:\n        article = Article.query.get_or_404(article_id)\n        data = request.get_json()\n\n        audio_sync_data = data.get('audio_sync_data')\n        has_audio_sync = data.get('has_audio_sync', False)\n\n        # 验证JSON格式\n        if audio_sync_data:\n            try:\n                import json\n                json.loads(audio_sync_data)\n            except json.JSONDecodeError:\n                return jsonify({'success': False, 'error': '音频同步数据格式错误'}), 400\n\n        # 更新文章\n        article.audio_sync_data = audio_sync_data\n        article.has_audio_sync = has_audio_sync\n        article.updated_at = datetime.utcnow()\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '音频同步配置保存成功'\n        })\n\n    except Exception as e:\n        logger.error(f\"保存音频同步配置失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': f'保存失败: {str(e)}'}), 500\n\n\n@admin_bp.route('/api/proxy-subtitle', methods=['GET'])\ndef proxy_subtitle():\n    \"\"\"代理获取字幕文件，解决跨域问题\"\"\"\n    try:\n        import requests\n        from flask import Response\n\n        # 获取要代理的字幕URL\n        subtitle_url = request.args.get('url')\n        if not subtitle_url:\n            return jsonify({'error': '缺少url参数'}), 400\n\n        logger.info(f\"代理获取字幕文件: {subtitle_url}\")\n\n        # 发起请求获取字幕文件\n        try:\n            response = requests.get(subtitle_url, timeout=10)\n\n            # 检查请求是否成功\n            if response.status_code != 200:\n                logger.warning(f\"无法获取字幕文件，状态码: {response.status_code}, URL: {subtitle_url}\")\n                return jsonify({'error': f'字幕文件不存在 (状态码: {response.status_code})'}), response.status_code\n\n            # 获取字幕内容\n            subtitle_content = response.text\n            logger.info(f\"成功获取字幕文件，大小: {len(subtitle_content)} 字符\")\n\n            # 创建响应并设置正确的内容类型\n            proxy_response = Response(subtitle_content, content_type='text/plain; charset=utf-8')\n\n            return proxy_response\n\n        except requests.Timeout:\n            logger.warning(f\"获取字幕文件超时: {subtitle_url}\")\n            return jsonify({'error': '字幕文件获取超时'}), 408\n\n        except requests.RequestException as e:\n            logger.error(f\"获取字幕文件失败: {str(e)}\")\n            return jsonify({'error': f'字幕文件获取失败: {str(e)}'}), 500\n\n    except Exception as e:\n        logger.error(f\"代理字幕文件失败: {str(e)}\")\n        return jsonify({'error': f'服务器错误: {str(e)}'}), 500\n\n\n\n\n# ==================== Vue3 管理后台认证API ====================\n\n@admin_bp.route('/api/auth/login', methods=['POST'])\ndef api_auth_login():\n    \"\"\"Vue3管理后台登录API\"\"\"\n    try:\n        from flask_login import login_user\n        from werkzeug.security import check_password_hash\n\n        data = request.get_json()\n        if not data or not data.get('username') or not data.get('password'):\n            return jsonify({'success': False, 'error': '用户名和密码不能为空'}), 400\n\n        username = data['username']\n        password = data['password']\n\n        # 查找用户\n        user = User.query.filter_by(username=username).first()\n        if not user:\n            return jsonify({'success': False, 'error': '用户名或密码错误'}), 401\n\n        # 验证密码\n        if not check_password_hash(user.password_hash, password):\n            return jsonify({'success': False, 'error': '用户名或密码错误'}), 401\n\n        # 检查用户权限\n        if not user.can_access_admin():\n            return jsonify({'success': False, 'error': '权限不足，无法访问管理后台'}), 403\n\n        # 登录用户\n        login_user(user, remember=True)\n\n        # 更新最后登录时间\n        user.last_login = datetime.now()\n        db.session.commit()\n\n        # 返回用户信息\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'is_admin': user.is_admin(),\n            'is_staff': user.can_access_admin(),\n            'role': user.role,\n            'membership_type': user.membership_type,\n            'created_at': user.created_at.isoformat() if user.created_at else None\n        }\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'token': 'flask-session',  # 使用Flask会话\n                'user': user_data\n            },\n            'message': '登录成功'\n        })\n\n    except Exception as e:\n        logger.error(f\"Vue3登录失败: {str(e)}\")\n        return jsonify({'success': False, 'error': '登录失败，请稍后重试'}), 500\n\n@admin_bp.route('/api/auth/logout', methods=['POST'])\n@login_required\ndef api_auth_logout():\n    \"\"\"Vue3管理后台登出API\"\"\"\n    try:\n        from flask_login import logout_user\n        logout_user()\n        return jsonify({'success': True, 'message': '登出成功'})\n    except Exception as e:\n        logger.error(f\"Vue3登出失败: {str(e)}\")\n        return jsonify({'success': False, 'error': '登出失败'}), 500\n\n@admin_bp.route('/api/auth/me')\n@login_required\n@staff_required\ndef api_auth_me():\n    \"\"\"获取当前用户信息 - 用于Vue3应用\"\"\"\n    try:\n        user_data = {\n            'id': current_user.id,\n            'username': current_user.username,\n            'email': current_user.email,\n            'is_admin': current_user.is_admin(),  # 调用方法而不是属性\n            'is_staff': current_user.can_access_admin(),  # 使用can_access_admin方法\n            'role': current_user.role,\n            'membership_type': current_user.membership_type,\n            'created_at': current_user.created_at.isoformat() if current_user.created_at else None\n        }\n\n        return jsonify({\n            'success': True,\n            'data': user_data\n        })\n    except Exception as e:\n        logger.error(f\"获取当前用户信息失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取用户信息失败: {str(e)}'}), 500\n\n# ==================== Vue3 管理后台 Dashboard API 路由 ====================\n\n@admin_bp.route('/api/admin/dashboard/stats')\n@login_required\n@staff_required\ndef api_dashboard_stats():\n    \"\"\"获取仪表盘统计数据\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 用户统计\n        total_users = User.query.count()\n        new_users_today = User.query.filter(\n            User.created_at >= datetime.now().date()\n        ).count()\n\n        # 文章统计\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter_by(status='published').count()\n\n        # 订单统计\n        total_purchases = Purchase.query.count() if Purchase else 0\n\n        # VIP用户统计\n        vip_users = User.query.filter(User.membership_type != 'free').count()\n\n        stats = {\n            'user_count': total_users,\n            'new_users_today': new_users_today,\n            'article_count': total_articles,\n            'published_articles': published_articles,\n            'purchase_count': total_purchases,\n            'vip_users': vip_users\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取仪表盘统计数据失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取统计数据失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/admin/dashboard/recent-users')\n@login_required\n@staff_required\ndef api_dashboard_recent_users():\n    \"\"\"获取最近注册用户\"\"\"\n    try:\n        limit = request.args.get('limit', 5, type=int)\n        recent_users = User.query.order_by(User.created_at.desc()).limit(limit).all()\n\n        users_data = []\n        for user in recent_users:\n            users_data.append({\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'created_at': user.created_at.isoformat() if user.created_at else None,\n                'is_active': getattr(user, 'is_active', True),\n                'membership_type': getattr(user, 'membership_type', 'free')\n            })\n\n        return jsonify({\n            'success': True,\n            'data': users_data\n        })\n    except Exception as e:\n        logger.error(f\"获取最近用户失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取最近用户失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/admin/dashboard/recent-articles')\n@login_required\n@staff_required\ndef api_dashboard_recent_articles():\n    \"\"\"获取最近发布文章\"\"\"\n    try:\n        limit = request.args.get('limit', 5, type=int)\n        recent_articles = Article.query.filter_by(status='published').order_by(\n            Article.created_at.desc()\n        ).limit(limit).all()\n\n        articles_data = []\n        for article in recent_articles:\n            articles_data.append({\n                'id': article.id,\n                'title': article.title,\n                'created_at': article.created_at.isoformat() if article.created_at else None,\n                'author_name': article.author.username if article.author else '未知',\n                'view_count': getattr(article, 'view_count', 0),\n                'status': article.status\n            })\n\n        return jsonify({\n            'success': True,\n            'data': articles_data\n        })\n    except Exception as e:\n        logger.error(f\"获取最近文章失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取最近文章失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/admin/dashboard')\n@login_required\n@staff_required\ndef api_dashboard_all():\n    \"\"\"获取完整仪表盘数据\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 获取统计数据\n        total_users = User.query.count()\n        new_users_today = User.query.filter(\n            User.created_at >= datetime.now().date()\n        ).count()\n\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter_by(status='published').count()\n        total_purchases = Purchase.query.count() if Purchase else 0\n        vip_users = User.query.filter(User.membership_type != 'free').count()\n\n        # 获取最近用户\n        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()\n        users_data = []\n        for user in recent_users:\n            users_data.append({\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'created_at': user.created_at.isoformat() if user.created_at else None,\n                'is_active': getattr(user, 'is_active', True),\n                'membership_type': getattr(user, 'membership_type', 'free')\n            })\n\n        # 获取最近文章\n        recent_articles = Article.query.filter_by(status='published').order_by(\n            Article.created_at.desc()\n        ).limit(5).all()\n        articles_data = []\n        for article in recent_articles:\n            articles_data.append({\n                'id': article.id,\n                'title': article.title,\n                'created_at': article.created_at.isoformat() if article.created_at else None,\n                'author_name': article.author.username if article.author else '未知',\n                'view_count': getattr(article, 'view_count', 0),\n                'status': article.status\n            })\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'stats': {\n                    'user_count': total_users,\n                    'new_users_today': new_users_today,\n                    'article_count': total_articles,\n                    'published_articles': published_articles,\n                    'purchase_count': total_purchases,\n                    'vip_users': vip_users\n                },\n                'recent_users': users_data,\n                'recent_articles': articles_data\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取完整仪表盘数据失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取仪表盘数据失败: {str(e)}'}), 500\n\n# ==================== Vue3 管理后台财务API ====================\n\n@admin_bp.route('/api/finance/stats')\n@login_required\n@staff_required\ndef api_finance_stats():\n    \"\"\"获取财务统计数据\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 计算总收入\n        total_revenue = Purchase.query.with_entities(\n            func.sum(Purchase.amount)\n        ).scalar() or 0\n\n        # 计算今日收入\n        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)\n        today_revenue = Purchase.query.filter(\n            Purchase.purchase_date >= today_start\n        ).with_entities(func.sum(Purchase.amount)).scalar() or 0\n\n        # VIP用户数\n        vip_users = UserFinance.query.filter(\n            UserFinance.vip_level > 0,\n            UserFinance.vip_expire_at > datetime.now()\n        ).count()\n\n        # 今日订单数\n        today_orders = Purchase.query.filter(\n            Purchase.purchase_date >= today_start\n        ).count()\n\n        # 待处理异常数（模拟数据）\n        pending_anomalies = 3\n\n        stats = {\n            'total_revenue': total_revenue,\n            'today_revenue': today_revenue,\n            'vip_users': vip_users,\n            'today_orders': today_orders,\n            'pending_anomalies': pending_anomalies\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取财务统计数据失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取财务统计失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/finance/orders')\n@login_required\n@staff_required\ndef api_finance_orders():\n    \"\"\"获取订单列表\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        search = request.args.get('search', '')\n\n        query = Purchase.query.join(User)\n\n        if search:\n            query = query.filter(\n                or_(\n                    Purchase.id.like(f'%{search}%'),\n                    User.username.like(f'%{search}%')\n                )\n            )\n\n        pagination = query.order_by(Purchase.purchase_date.desc()).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        orders_data = []\n        for purchase in pagination.items:\n            orders_data.append({\n                'id': purchase.id,\n                'user_id': purchase.user_id,\n                'username': purchase.user.username if purchase.user else '未知',\n                'product_type': purchase.product_type or 'VIP',\n                'amount': purchase.amount,\n                'payment_method': purchase.payment_method,\n                'status': 'success',  # Purchase表中的都是成功的\n                'created_at': purchase.purchase_date.isoformat() if purchase.purchase_date else None\n            })\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'data': orders_data,\n                'pagination': {\n                    'page': pagination.page,\n                    'pages': pagination.pages,\n                    'per_page': pagination.per_page,\n                    'total': pagination.total,\n                    'has_prev': pagination.has_prev,\n                    'has_next': pagination.has_next\n                }\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取订单列表失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取订单列表失败: {str(e)}'}), 500\n\n# ==================== 系统设置API ====================\n\n@admin_bp.route('/api/system/settings', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_system_settings():\n    \"\"\"获取系统设置API\"\"\"\n    try:\n        # 从数据库或配置文件获取系统设置\n        settings = {\n            # 基础设置\n            'site_name': app.config.get('SITE_NAME', '塔罗嗅嗅'),\n            'site_description': app.config.get('SITE_DESCRIPTION', '专业的塔罗牌占卜平台'),\n            'admin_email': app.config.get('ADMIN_EMAIL', '<EMAIL>'),\n            'contact_phone': app.config.get('CONTACT_PHONE', '************'),\n\n            # 用户设置\n            'allow_registration': app.config.get('ALLOW_REGISTRATION', True),\n            'require_email_verification': app.config.get('REQUIRE_EMAIL_VERIFICATION', True),\n            'default_user_quota': app.config.get('DEFAULT_USER_QUOTA', 100),\n            'max_login_attempts': app.config.get('MAX_LOGIN_ATTEMPTS', 5),\n\n            # 内容设置\n            'articles_per_page': app.config.get('ARTICLES_PER_PAGE', 20),\n            'auto_approve_articles': app.config.get('AUTO_APPROVE_ARTICLES', False),\n            'max_upload_size': app.config.get('MAX_UPLOAD_SIZE', 10),\n            'allowed_file_types': app.config.get('ALLOWED_FILE_TYPES', 'jpg,png,gif,pdf,doc,docx'),\n\n            # 安全设置\n            'enable_csrf_protection': app.config.get('WTF_CSRF_ENABLED', True),\n            'session_timeout': app.config.get('PERMANENT_SESSION_LIFETIME', 30),\n            'enable_rate_limiting': app.config.get('RATELIMIT_ENABLED', True),\n            'backup_frequency': app.config.get('BACKUP_FREQUENCY', 'daily'),\n\n            # 邮件设置\n            'smtp_server': app.config.get('MAIL_SERVER', 'smtp.gmail.com'),\n            'smtp_port': app.config.get('MAIL_PORT', 587),\n            'smtp_username': app.config.get('MAIL_USERNAME', ''),\n            'smtp_use_tls': app.config.get('MAIL_USE_TLS', True)\n        }\n\n        return jsonify({\n            'success': True,\n            'data': settings\n        })\n    except Exception as e:\n        logger.error(f\"获取系统设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/system/settings', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_system_settings():\n    \"\"\"更新系统设置API\"\"\"\n    try:\n        data = request.get_json()\n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n\n        # 这里应该将设置保存到数据库或配置文件\n        # 为了演示，我们只是记录日志\n        logger.info(f\"管理员 {current_user.username} 更新了系统设置\")\n\n        # 可以在这里添加设置验证逻辑\n        # 例如验证邮箱格式、端口号范围等\n\n        return jsonify({\n            'success': True,\n            'message': '系统设置更新成功'\n        })\n    except Exception as e:\n        logger.error(f\"更新系统设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# ==================== 价格设置API ====================\n\n@admin_bp.route('/api/system/prices', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_price_settings():\n    \"\"\"获取价格设置API\"\"\"\n    try:\n        # 从数据库或配置获取价格设置\n        prices = {\n            # 内容价格\n            'basic_content_price': 9.90,\n            'premium_content_price': 19.90,\n            'vip_content_price': 39.90,\n            'consultation_price': 99.00,\n\n            # 会员价格\n            'monthly_vip_price': 29.90,\n            'quarterly_vip_price': 79.90,\n            'yearly_vip_price': 299.90,\n\n            # 积分价格\n            'points_package_small': 9.90,\n            'points_small_amount': 100,\n            'points_package_large': 49.90,\n            'points_large_amount': 600,\n\n            # 折扣设置\n            'vip_discount': 0.85,\n            'bulk_discount': 0.90\n        }\n\n        return jsonify({\n            'success': True,\n            'data': prices\n        })\n    except Exception as e:\n        logger.error(f\"获取价格设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/system/prices', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_price_settings():\n    \"\"\"更新价格设置API\"\"\"\n    try:\n        data = request.get_json()\n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n\n        # 验证价格数据\n        for key, value in data.items():\n            if 'price' in key or 'discount' in key:\n                try:\n                    float(value)\n                except (ValueError, TypeError):\n                    return jsonify({'success': False, 'error': f'无效的价格值: {key}'}), 400\n\n        logger.info(f\"管理员 {current_user.username} 更新了价格设置\")\n\n        return jsonify({\n            'success': True,\n            'message': '价格设置更新成功'\n        })\n    except Exception as e:\n        logger.error(f\"更新价格设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# ==================== 上传配置API ====================\n\n@admin_bp.route('/api/uploader/config', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_uploader_config():\n    \"\"\"获取上传配置API\"\"\"\n    try:\n        config = {\n            'website_url': app.config.get('UPLOADER_WEBSITE_URL', 'http://0.0.0.0:5000'),\n            'username': app.config.get('UPLOADER_USERNAME', 'admin'),\n            'password': '',  # 出于安全考虑，不返回密码\n            'upload_path': app.config.get('UPLOADER_PATH', '/uploads'),\n            'batch_size': app.config.get('UPLOADER_BATCH_SIZE', 10),\n            'max_retries': app.config.get('UPLOADER_MAX_RETRIES', 3),\n            'timeout': app.config.get('UPLOADER_TIMEOUT', 30),\n            'concurrent_uploads': app.config.get('UPLOADER_CONCURRENT', 3),\n            'auto_create_tags': app.config.get('UPLOADER_AUTO_TAGS', True),\n            'auto_publish': app.config.get('UPLOADER_AUTO_PUBLISH', False),\n            'backup_files': app.config.get('UPLOADER_BACKUP', True),\n            'delete_after_upload': app.config.get('UPLOADER_DELETE_AFTER', False)\n        }\n\n        return jsonify({\n            'success': True,\n            'data': config\n        })\n    except Exception as e:\n        logger.error(f\"获取上传配置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/config', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_uploader_config():\n    \"\"\"更新上传配置API\"\"\"\n    try:\n        data = request.get_json()\n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n\n        # 验证配置数据\n        if 'website_url' in data and not data['website_url'].startswith(('http://', 'https://')):\n            return jsonify({'success': False, 'error': '无效的网站地址'}), 400\n\n        logger.info(f\"管理员 {current_user.username} 更新了上传配置\")\n\n        return jsonify({\n            'success': True,\n            'message': '上传配置更新成功'\n        })\n    except Exception as e:\n        logger.error(f\"更新上传配置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/test-connection', methods=['POST'])\n@login_required\n@admin_required\ndef api_test_uploader_connection():\n    \"\"\"测试上传连接API\"\"\"\n    try:\n        data = request.get_json()\n        website_url = data.get('website_url', '')\n        username = data.get('username', '')\n        password = data.get('password', '')\n\n        # 这里应该实现实际的连接测试逻辑\n        # 例如发送HTTP请求到目标服务器\n\n        # 模拟连接测试\n        import time\n        time.sleep(1)  # 模拟网络延迟\n\n        return jsonify({\n            'success': True,\n            'message': '连接测试成功'\n        })\n    except Exception as e:\n        logger.error(f\"连接测试失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/upload', methods=['POST'])\n@login_required\n@admin_required\ndef api_manual_upload():\n    \"\"\"手动上传文件API\"\"\"\n    try:\n        if 'files' not in request.files:\n            return jsonify({'success': False, 'error': '没有选择文件'}), 400\n\n        files = request.files.getlist('files')\n        if not files or files[0].filename == '':\n            return jsonify({'success': False, 'error': '没有选择文件'}), 400\n\n        upload_results = []\n        for file in files:\n            if file and file.filename:\n                # 这里应该实现实际的文件上传逻辑\n                # 例如保存到本地或云存储\n\n                upload_results.append({\n                    'filename': file.filename,\n                    'status': 'success',\n                    'message': '上传成功'\n                })\n\n        logger.info(f\"管理员 {current_user.username} 手动上传了 {len(files)} 个文件\")\n\n        return jsonify({\n            'success': True,\n            'data': upload_results,\n            'message': f'成功上传 {len(upload_results)} 个文件'\n        })\n    except Exception as e:\n        logger.error(f\"文件上传失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# ==================== 上传日志API ====================\n\n@admin_bp.route('/api/uploader/logs', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_upload_logs():\n    \"\"\"获取上传日志API\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        date_from = request.args.get('date_from', '')\n        date_to = request.args.get('date_to', '')\n        status = request.args.get('status', '')\n\n        # 模拟上传日志数据\n        logs = [\n            {\n                'id': 1,\n                'filename': 'tarot_reading_guide.pdf',\n                'file_size': 2048576,\n                'status': 'success',\n                'upload_time': '2025-08-03 14:30:25',\n                'duration': 2.5,\n                'error_message': None\n            },\n            {\n                'id': 2,\n                'filename': 'card_meanings.docx',\n                'file_size': 1024000,\n                'status': 'failed',\n                'upload_time': '2025-08-03 14:25:10',\n                'duration': None,\n                'error_message': '文件格式不支持'\n            }\n        ]\n\n        # 统计数据\n        stats = {\n            'success_count': 156,\n            'failed_count': 12,\n            'total_size': 2048576000,\n            'today_count': 23\n        }\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'logs': logs,\n                'stats': stats,\n                'pagination': {\n                    'page': page,\n                    'pages': 1,\n                    'per_page': per_page,\n                    'total': len(logs)\n                }\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取上传日志失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/logs/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_upload_logs_stats():\n    \"\"\"获取上传日志统计信息API\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 模拟上传日志统计数据（实际应该从上传日志表获取）\n        total_uploads = 1234\n        successful_uploads = 1156\n        failed_uploads = 78\n\n        # 时间统计\n        today = datetime.now().date()\n        week_ago = today - timedelta(days=7)\n\n        today_uploads = 23\n        week_uploads = 156\n\n        # 文件大小统计\n        total_file_size = 2048576000  # 2GB\n        avg_file_size = total_file_size // total_uploads if total_uploads > 0 else 0\n\n        # 文件类型分布\n        file_type_distribution = {\n            'pdf': 456,\n            'docx': 234,\n            'txt': 123,\n            'md': 89\n        }\n\n        # 上传趋势（最近7天）\n        upload_trend = []\n        for i in range(7):\n            date = today - timedelta(days=i)\n            # 模拟数据\n            count = 20 + (hash(str(date)) % 10)\n            success = count - (hash(str(date)) % 3)\n            failed = count - success\n\n            upload_trend.append({\n                'date': date.strftime('%Y-%m-%d'),\n                'count': count,\n                'success': success,\n                'failed': failed\n            })\n        upload_trend.reverse()\n\n        stats = {\n            'total_uploads': total_uploads,\n            'successful_uploads': successful_uploads,\n            'failed_uploads': failed_uploads,\n            'today_uploads': today_uploads,\n            'this_week_uploads': week_uploads,\n            'total_file_size': total_file_size,\n            'avg_file_size': avg_file_size,\n            'file_type_distribution': file_type_distribution,\n            'upload_trend': upload_trend\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取上传日志统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/logs', methods=['DELETE'])\n@login_required\n@admin_required\ndef api_clear_upload_logs():\n    \"\"\"清空上传日志API\"\"\"\n    try:\n        # 这里应该实现清空日志的逻辑\n        logger.info(f\"管理员 {current_user.username} 清空了上传日志\")\n\n        return jsonify({\n            'success': True,\n            'message': '上传日志已清空'\n        })\n    except Exception as e:\n        logger.error(f\"清空上传日志失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/logs/<int:log_id>', methods=['DELETE'])\n@login_required\n@admin_required\ndef api_delete_upload_log(log_id):\n    \"\"\"删除单个上传日志API\"\"\"\n    try:\n        # 这里应该实现删除单个日志的逻辑\n        logger.info(f\"管理员 {current_user.username} 删除了上传日志 {log_id}\")\n\n        return jsonify({\n            'success': True,\n            'message': '上传日志已删除'\n        })\n    except Exception as e:\n        logger.error(f\"删除上传日志失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# ==================== 数据统计API ====================\n\n@admin_bp.route('/api/statistics/overview', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_statistics_overview():\n    \"\"\"获取统计概览API\"\"\"\n    try:\n        date_from = request.args.get('date_from', '')\n        date_to = request.args.get('date_to', '')\n        period = request.args.get('period', 'month')\n\n        # 从数据库获取实际统计数据\n        from models import User, Article, Order\n\n        # 用户统计\n        total_users = User.query.count()\n        active_users = User.query.filter(User.last_login.isnot(None)).count()\n        vip_users = User.query.filter(User.is_vip == True).count()\n\n        # 文章统计\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter(Article.status == 'published').count()\n        draft_articles = Article.query.filter(Article.status == 'draft').count()\n\n        # 今日新增统计\n        from datetime import datetime, timedelta\n        today = datetime.now().date()\n        new_users_today = User.query.filter(User.created_at >= today).count()\n        new_articles_today = Article.query.filter(Article.created_at >= today).count()\n\n        # 收入统计（如果有订单表）\n        total_revenue = 234567  # 这里应该从订单表计算\n        today_revenue = 1234\n        month_revenue = 45678\n\n        stats = {\n            # 用户统计\n            'total_users': total_users,\n            'active_users': active_users,\n            'vip_users': vip_users,\n            'new_users_today': new_users_today,\n            'new_users_week': 312,  # 这里应该计算本周新增\n            'user_retention': 78.5,\n\n            # 内容统计\n            'total_articles': total_articles,\n            'published_articles': published_articles,\n            'draft_articles': draft_articles,\n            'new_articles_today': new_articles_today,\n            'total_views': 456789,  # 这里应该从阅读记录计算\n\n            # 财务统计\n            'total_revenue': total_revenue,\n            'vip_revenue': 156789,\n            'content_revenue': 77778,\n            'today_revenue': today_revenue,\n            'month_revenue': month_revenue,\n            'avg_order_value': 89.5,\n\n            # 系统统计\n            'total_visits': 789456,\n            'unique_visitors': 234567,\n            'page_views': 1234567,\n            'avg_session_duration': 12.5,\n            'bounce_rate': 35.2,\n            'avg_response_time': 245\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取统计概览失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/statistics/users', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_user_statistics():\n    \"\"\"获取用户统计API\"\"\"\n    try:\n        # 这里应该实现详细的用户统计逻辑\n        user_stats = {\n            'registration_trend': [],  # 注册趋势数据\n            'activity_distribution': [],  # 活跃度分布\n            'retention_analysis': []  # 留存分析\n        }\n\n        return jsonify({\n            'success': True,\n            'data': user_stats\n        })\n    except Exception as e:\n        logger.error(f\"获取用户统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/statistics/content', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_content_statistics():\n    \"\"\"获取内容统计API\"\"\"\n    try:\n        # 这里应该实现详细的内容统计逻辑\n        content_stats = {\n            'publish_trend': [],  # 发布趋势\n            'category_distribution': [],  # 分类分布\n            'popular_articles': []  # 热门文章\n        }\n\n        return jsonify({\n            'success': True,\n            'data': content_stats\n        })\n    except Exception as e:\n        logger.error(f\"获取内容统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/statistics/revenue', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_revenue_statistics():\n    \"\"\"获取收入统计API\"\"\"\n    try:\n        # 这里应该实现详细的收入统计逻辑\n        revenue_stats = {\n            'revenue_trend': [],  # 收入趋势\n            'payment_methods': [],  # 支付方式分布\n            'product_revenue': []  # 产品收入分析\n        }\n\n        return jsonify({\n            'success': True,\n            'data': revenue_stats\n        })\n    except Exception as e:\n        logger.error(f\"获取收入统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n", "modifiedCode": "#!/usr/bin/env python3\n# -*- coding: utf-8 -*-\n\"\"\"\nAdmin blueprint for tarot website.\nThis module contains routes for the admin interface, including article management.\n\"\"\"\n\nfrom flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, send_from_directory, session\nfrom flask_login import login_required, current_user\nfrom extensions import db\nfrom models import Article, Tag, Author, ArticleErrorReport, User, ReadingPreference, SystemSetting, Purchase, Order, Subscription, UserFinance, MediaFile, SupportTicket, SupportMessage\nfrom utils.decorators import admin_required, staff_required, customer_service_required, finance_required\nimport logging\nfrom datetime import date, datetime # 确保导入在不上他那个逼样。\nfrom werkzeug.utils import secure_filename\nfrom werkzeug.security import generate_password_hash\nimport re\nimport os\nimport uuid\n\n# 创建蓝图实例 - 使用admin作为蓝图名称，与模板中的url_for一致\nadmin_bp = Blueprint('admin', __name__, url_prefix='/admin')\nlogger = logging.getLogger(__name__)\n\ndef auto_detect_language(text):\n    \"\"\"自动检测文本语言\"\"\"\n    if not text or not text.strip():\n        return None\n    \n    # 计算中文字符比例\n    chinese_chars = len(re.findall(r'[\\u4e00-\\u9fff]', text))\n    total_chars = len(re.sub(r'\\s+', '', text))  # 去除空白字符\n    \n    if total_chars == 0:\n        return None\n    \n    chinese_ratio = chinese_chars / total_chars\n    \n    # 如果中文字符超过30%，认为是中文\n    if chinese_ratio > 0.3:\n        return 'zh'\n    \n    # 检测是否包含常见英文单词模式\n    english_words = len(re.findall(r'\\b[a-zA-Z]+\\b', text))\n    if english_words > 5:  # 包含超过5个英文单词\n        return 'en'\n    \n    # 默认返回中文（因为这是中文为主的塔罗网站）\n    return 'zh'\n\ndef auto_detect_publish_date(text, title):\n    \"\"\"自动检测发布日期\"\"\"\n    # 优先使用当前日期\n    current_date = date.today()\n    \n    # 可选：从标题或内容中提取日期信息\n    # 这里先简单返回当前日期，后续可以加入更复杂的日期提取逻辑\n    return current_date\n\n# Vue3管理后台路由 (暂时禁用，恢复原版Flask模板)\n# @admin_bp.route('/')\n# @admin_bp.route('/<path:path>')\n# @login_required\n# @staff_required\n# def vue_admin(path=''):\n#     \"\"\"Vue3管理后台 - SPA路由处理\"\"\"\n#     try:\n#         # 服务Vue3构建的index.html\n#         return send_from_directory('static/admin', 'index.html')\n#     except Exception as e:\n#         logger.error(f\"加载Vue3管理后台失败: {str(e)}\")\n#         return f\"管理后台加载失败: {str(e)}\", 500\n\n# 恢复原版Flask模板管理后台\n@admin_bp.route('/')\n@login_required\n@staff_required\ndef dashboard():\n    \"\"\"管理后台首页 - 原版Flask模板\"\"\"\n    try:\n        # 获取统计数据\n        from datetime import datetime, timedelta\n\n        # 用户统计\n        total_users = User.query.count()\n        new_users_today = User.query.filter(\n            User.created_at >= datetime.now().date()\n        ).count()\n\n        # 文章统计\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter_by(status='published').count()\n\n        # VIP用户统计\n        vip_users = User.query.filter(User.membership_type != 'free').count()\n\n        # 最近注册用户\n        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()\n\n        # 最近发布文章\n        recent_articles = Article.query.filter_by(status='published').order_by(\n            Article.created_at.desc()\n        ).limit(5).all()\n\n        stats = {\n            'user_count': total_users,\n            'new_users_today': new_users_today,\n            'article_count': total_articles,\n            'published_articles': published_articles,\n            'vip_users': vip_users\n        }\n\n        return render_template('admin/dashboard.html',\n                             stats=stats,\n                             recent_users=recent_users,\n                             recent_articles=recent_articles,\n                             now=datetime.now())\n    except Exception as e:\n        logger.error(f\"管理后台首页加载失败: {str(e)}\")\n        flash(f'加载失败: {str(e)}', 'error')\n        return render_template('admin/dashboard.html',\n                             stats={},\n                             recent_users=[],\n                             recent_articles=[],\n                             now=datetime.now())\n\n# ==================== Vue3管理后台独立路由 ====================\n\n# 静态资源路由 - 必须在SPA路由之前\n@admin_bp.route('/js/<path:filename>')\ndef vue_admin_js(filename):\n    \"\"\"Vue3管理后台 - JS文件服务\"\"\"\n    try:\n        return send_from_directory('static/admin/js', filename)\n    except Exception as e:\n        logger.error(f\"加载JS文件失败: {str(e)}\")\n        return f\"JS文件加载失败: {str(e)}\", 404\n\n@admin_bp.route('/css/<path:filename>')\ndef vue_admin_css(filename):\n    \"\"\"Vue3管理后台 - CSS文件服务\"\"\"\n    try:\n        return send_from_directory('static/admin/css', filename)\n    except Exception as e:\n        logger.error(f\"加载CSS文件失败: {str(e)}\")\n        return f\"CSS文件加载失败: {str(e)}\", 404\n\n\n\n\n\n# SPA路由 - 处理所有其他路径\n@admin_bp.route('/vue')\n@admin_bp.route('/vue/')\n@admin_bp.route('/vue/<path:path>')\n@login_required\n@staff_required\ndef vue_admin(path=''):\n    \"\"\"Vue3管理后台 - SPA路由处理\"\"\"\n    try:\n        # 服务Vue3构建的index.html\n        return send_from_directory('static/admin', 'index.html')\n    except Exception as e:\n        logger.error(f\"加载Vue3管理后台失败: {str(e)}\")\n        return f\"管理后台加载失败: {str(e)}\", 500\n\n# ==================== 原版Flask模板路由 ====================\n\n# 文章管理路由\n@admin_bp.route('/articles')\n@login_required\n@admin_required\ndef articles():\n    \"\"\"文章管理页面\"\"\"\n    page = request.args.get('page', 1, type=int)\n    per_page = request.args.get('per_page', 10, type=int) # 允许通过查询参数改变每页数量，默认为10\n    try:\n        articles_pagination = Article.query.order_by(Article.created_at.desc()).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n        return render_template('admin/articles.html', \n                               articles=articles_pagination.items, \n                               pagination=articles_pagination,\n                               search=request.args.get('search', ''),\n                               status=request.args.get('status', ''))\n    except Exception as e:\n        logger.error(f\"加载文章管理页面失败: {str(e)}\")\n        flash('加载文章列表失败，请稍后再试。', 'error')\n        return render_template('admin/articles.html', articles=[], pagination=None, search='', status='') # 传递空列表和None\n\n# 删除文章API路由\n@admin_bp.route('/api/articles/backup_delete/<int:article_id>', methods=['POST', 'DELETE'])\n@login_required\n@admin_required\ndef backup_delete_article(article_id):\n    logger.critical(f\"CRITICAL_ADMIN_DEBUG: backup_delete_article function was called for article ID: {article_id}\")\n    \"\"\"备份并删除文章API - 简化版实现\"\"\"\n    logger.info(f\"删除文章API被调用，文章ID: {article_id}\")\n    try:\n        article = Article.query.get_or_404(article_id)\n        # 这里可以添加备份逻辑\n        db.session.delete(article)\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": f\"文章 {article_id} 已成功删除\"})\n    except Exception as e:\n        logger.error(f\"删除文章失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n    \n# 标签管理\n@admin_bp.route('/tags')\n@login_required\n@staff_required\ndef tags():\n    \"\"\"标签管理页面\"\"\"\n    # 初始加载时，仍然可以通过这里传递数据，或者前端完全通过API获取\n    all_tags = Tag.query.order_by(Tag.name).all()\n    return render_template('admin/tags.html', tags=all_tags)\n\n# 创建标签 (旧的表单处理，将被新的API替代，暂时保留或后续移除)\n@admin_bp.route('/tag/new', methods=['POST'])\n@login_required\n@staff_required\ndef create_tag():\n    \"\"\"创建新标签\"\"\"\n    name = request.form.get('name')\n    if not name:\n        flash('标签名不能为空', 'error')\n        return redirect(url_for('admin.tags'))\n    \n    existing_tag = Tag.query.filter_by(name=name).first()\n    if existing_tag:\n        flash(f'标签 \"{name}\" 已存在', 'error')\n        return redirect(url_for('admin.tags'))\n    \n    tag = Tag(name=name)\n    db.session.add(tag)\n    try:\n        db.session.commit()\n        flash('标签创建成功', 'success')\n    except Exception as e:\n        db.session.rollback()\n        flash(f'标签创建失败: {str(e)}', 'error')\n    \n    return redirect(url_for('admin.tags'))\n\n# 删除标签 (旧的表单处理，将被新的API替代，暂时保留或后续移除)\n@admin_bp.route('/tag/<int:tag_id>/delete', methods=['POST'])\n@login_required\n@staff_required\ndef delete_tag(tag_id):\n    \"\"\"删除标签\"\"\"\n    tag = Tag.query.get_or_404(tag_id)\n    try:\n        # 在删除标签前，需要处理与文章的关联\n        # article_tags 是关联表，SQLAlchemy 会自动处理多对多关系中关联表的记录删除\n        # 如果有其他直接关联或需要特殊处理的逻辑，在这里添加\n        \n        db.session.delete(tag)\n        db.session.commit()\n        flash('标签删除成功', 'success')\n    except Exception as e:\n        db.session.rollback()\n        flash(f'标签删除失败: {str(e)}', 'error')\n    \n    return redirect(url_for('admin.tags'))\n\n# --- 新增标签管理API端点 ---\n\n@admin_bp.route('/api/readers', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_readers():\n    \"\"\"获取塔罗师列表 (API) - 用于文章编辑页面\"\"\"\n    try:\n        # 查询所有作者（塔罗师）\n        readers_query = Author.query.order_by(Author.name).all()\n\n        # 构建返回数据\n        readers_data = []\n        for reader in readers_query:\n            readers_data.append({\n                'id': reader.id,\n                'name': reader.name,\n                'email': getattr(reader, 'email', ''),\n                'bio': reader.description or '',\n                'avatar': getattr(reader, 'avatar', ''),\n                'status': 'active',  # 默认状态\n                'article_count': len(reader.articles) if hasattr(reader, 'articles') else 0\n            })\n\n        return jsonify({\"success\": True, \"data\": readers_data})\n    except Exception as e:\n        logger.error(f\"获取塔罗师列表API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_tags():\n    \"\"\"获取所有标签 (API)\"\"\"\n    try:\n        tags_query = Tag.query.order_by(Tag.name).all()\n        # 对于每个标签，计算文章数量\n        # 这种方式会在循环中对每个标签执行一次 len(tag.articles)，可能有效率问题\n        # 更优的方式是使用 sqlalchemy.func.count 和 group_by\n        # from sqlalchemy import func\n        # tags_with_counts = db.session.query(\n        # Tag, func.count(article_tags.c.article_id).label('articles_count')\n        # ).outerjoin(article_tags, Tag.id == article_tags.c.tag_id)\\\n        # .group_by(Tag.id).order_by(Tag.name).all()\n        #\n        # results = []\n        # for tag, count in tags_with_counts:\n        #     tag_data = tag.to_dict()\n        #     tag_data['articles_count'] = count\n        #     results.append(tag_data)\n        # return jsonify({\"success\": True, \"tags\": results})\n\n        # 暂时使用简单方式，后续可优化\n        return jsonify({\"success\": True, \"data\": [tag.to_dict(include_article_count=True) for tag in tags_query]})\n    except Exception as e:\n        logger.error(f\"获取标签列表API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags/stats', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_tags_stats():\n    \"\"\"获取标签统计信息API\"\"\"\n    try:\n        from sqlalchemy import func\n\n        # 基础统计\n        total_tags = Tag.query.count()\n\n        # 获取有文章的标签数量\n        used_tags = db.session.query(Tag.id).join(\n            Article.tags\n        ).distinct().count()\n\n        unused_tags = total_tags - used_tags\n\n        # 最常用标签\n        most_used_tags = db.session.query(\n            Tag.name,\n            func.count(Article.id).label('article_count')\n        ).join(Article.tags)\\\n         .group_by(Tag.id, Tag.name)\\\n         .order_by(func.count(Article.id).desc())\\\n         .limit(10).all()\n\n        most_used_tags_data = [\n            {'name': tag.name, 'article_count': tag.article_count}\n            for tag in most_used_tags\n        ]\n\n        # 标签使用分布\n        tag_usage_stats = db.session.query(\n            func.count(Article.id).label('article_count'),\n            func.count(Tag.id).label('tag_count')\n        ).select_from(Tag)\\\n         .outerjoin(Article.tags)\\\n         .group_by(Tag.id).all()\n\n        # 统计不同使用范围的标签数量\n        usage_ranges = {'1-5篇': 0, '6-10篇': 0, '11+篇': 0, '未使用': 0}\n        for stat in tag_usage_stats:\n            count = stat.article_count or 0\n            if count == 0:\n                usage_ranges['未使用'] += 1\n            elif count <= 5:\n                usage_ranges['1-5篇'] += 1\n            elif count <= 10:\n                usage_ranges['6-10篇'] += 1\n            else:\n                usage_ranges['11+篇'] += 1\n\n        tag_usage_distribution = [\n            {'range': range_name, 'count': count}\n            for range_name, count in usage_ranges.items()\n        ]\n\n        stats = {\n            'total_tags': total_tags,\n            'used_tags': used_tags,\n            'unused_tags': unused_tags,\n            'most_used_tags': most_used_tags_data,\n            'tag_usage_distribution': tag_usage_distribution\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取标签统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/tags', methods=['POST'])\n@login_required\n@staff_required\ndef api_create_tag():\n    \"\"\"创建新标签 (API)\"\"\"\n    data = request.get_json()\n    if not data or 'name' not in data or not data['name'].strip():\n        return jsonify({\"success\": False, \"error\": \"标签名不能为空\"}), 400\n    \n    name = data['name'].strip()\n    existing_tag = Tag.query.filter_by(name=name).first()\n    if existing_tag:\n        return jsonify({\"success\": False, \"error\": f\"标签 '{name}' 已存在\"}), 409 # 409 Conflict\n        \n    tag = Tag(name=name)\n    db.session.add(tag)\n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"标签创建成功\", \"tag\": tag.to_dict(include_article_count=True)}), 201\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"创建标签API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags/<int:tag_id>', methods=['PUT'])\n@login_required\n@staff_required\ndef api_update_tag(tag_id):\n    \"\"\"更新标签 (API)\"\"\"\n    tag = Tag.query.get_or_404(tag_id)\n    data = request.get_json()\n    \n    if not data or 'name' not in data or not data['name'].strip():\n        return jsonify({\"success\": False, \"error\": \"标签名不能为空\"}), 400\n        \n    new_name = data['name'].strip()\n    if new_name != tag.name:\n        existing_tag = Tag.query.filter(Tag.id != tag_id, Tag.name == new_name).first()\n        if existing_tag:\n            return jsonify({\"success\": False, \"error\": f\"标签 '{new_name}' 已存在\"}), 409 # 409 Conflict\n        tag.name = new_name\n        \n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"标签更新成功\", \"tag\": tag.to_dict(include_article_count=True)})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新标签API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags/<int:tag_id>', methods=['DELETE'])\n@login_required\n@staff_required\ndef api_delete_tag(tag_id):\n    \"\"\"删除标签 (API)\"\"\"\n    tag = Tag.query.get_or_404(tag_id)\n    try:\n        # SQLAlchemy 会自动处理 article_tags 关联表中的记录\n        db.session.delete(tag)\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"标签删除成功\"})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"删除标签API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/tags/batch_delete', methods=['POST'])\n@login_required\n@staff_required\ndef api_batch_delete_tags():\n    \"\"\"批量删除标签 (API)\"\"\"\n    data = request.get_json()\n    if not data or 'ids' not in data or not isinstance(data['ids'], list):\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据，需要一个包含ID列表的 'ids' 键。\"}), 400\n\n    ids_to_delete = data['ids']\n    if not ids_to_delete:\n        return jsonify({\"success\": False, \"error\": \"没有提供要删除的标签ID。\"}), 400\n\n    deleted_count = 0\n    errors = []\n\n    for tag_id in ids_to_delete:\n        try:\n            tag_id_int = int(tag_id) # 确保是整数\n            tag = Tag.query.get(tag_id_int)\n            if tag:\n                db.session.delete(tag)\n                deleted_count += 1\n            else:\n                errors.append(f\"ID {tag_id_int} 未找到对应的标签。\")\n        except ValueError:\n            errors.append(f\"无效的标签ID格式: '{tag_id}'\")\n        except Exception as e:\n            # 捕获删除单个标签时可能发生的其他DB相关错误\n            errors.append(f\"删除ID {tag_id} 时发生错误: {str(e)}\")\n            logger.error(f\"批量删除标签中，删除ID {tag_id} 失败: {str(e)}\")\n    \n    if errors and deleted_count > 0:\n        # 如果有部分成功部分失败，需要决定是否回滚\n        # 为了简单起见，我们先提交成功的，并报告错误\n        # 更严格的事务可以回滚所有： db.session.rollback(); return jsonify(...)\n        try:\n            db.session.commit()\n            message = f\"成功删除了 {deleted_count} 个标签。\"\n            if errors:\n                message += \" 但以下ID处理失败： \" + \"; \".join(errors)\n            return jsonify({\"success\": True, \"message\": message, \"errors\": errors if errors else None})\n        except Exception as e:\n            db.session.rollback()\n            logger.error(f\"批量删除标签提交事务时失败: {str(e)}\")\n            return jsonify({\"success\": False, \"error\": f\"提交批量删除操作时发生严重错误: {str(e)}\", \"details\": errors}), 500\n    elif errors: # 全部失败\n        db.session.rollback() # 确保没有任何更改被提交\n        return jsonify({\"success\": False, \"error\": \"所有选中的标签都删除失败。\", \"details\": errors}), 400\n    elif deleted_count > 0: # 全部成功\n        try:\n            db.session.commit()\n            return jsonify({\"success\": True, \"message\": f\"成功删除了 {deleted_count} 个标签。\"})\n        except Exception as e:\n            db.session.rollback()\n            logger.error(f\"批量删除标签提交事务时失败: {str(e)}\")\n            return jsonify({\"success\": False, \"error\": f\"提交批量删除操作时发生严重错误: {str(e)}\"}), 500\n    else: # ids_to_delete 为空或所有ID都无效但未引发异常（例如，全是未找到的ID）\n        return jsonify({\"success\": False, \"error\": \"没有有效的标签被删除。可能所有提供的ID都无效。\"}), 400\n\n# 添加占位 uploader_dashboard 路由\n@admin_bp.route('/uploader')\n@login_required\n@staff_required\ndef uploader_dashboard():\n    \"\"\"上传管理面板 (占位)\"\"\"\n    flash('上传管理功能正在建设中...', 'info')\n    return redirect(url_for('admin.dashboard'))\n\n# 添加占位 create_article 路由\n@admin_bp.route('/articles/create', methods=['GET', 'POST'])\n@login_required\n@staff_required\ndef create_article():\n    \"\"\"创建文章页面 (占位)\"\"\"\n    # flash('创建新文章功能正在建设中...', 'info') # 改为直接渲染模板\n    all_readers = Author.query.all()\n    all_tags = Tag.query.order_by(Tag.name).all() # 新增：获取所有标签\n    return render_template('admin/article_upload.html', \n                           article=None, \n                           all_readers=all_readers, \n                           all_tags=all_tags) # 新增：传递all_tags到模板\n\n# 添加占位 edit_article 路由\n@admin_bp.route('/articles/<int:article_id>/edit', methods=['GET', 'POST'])\n@login_required\n@staff_required\ndef edit_article(article_id):\n    \"\"\"编辑文章页面\"\"\"\n    article = Article.query.get_or_404(article_id)\n    all_readers = Author.query.order_by(Author.name).all() # 按名称排序\n    all_tags = Tag.query.order_by(Tag.name).all() # 按名称排序\n    # # 简单的表单处理示例 (如果需要POST请求)\n    # if request.method == 'POST':\n    #     # 处理表单提交逻辑\n    #     # article.title = request.form.get('title')\n    #     # db.session.commit()\n    #     # flash('文章更新成功!', 'success')\n    #     # return redirect(url_for('admin.articles'))\n    #     pass # 暂时不处理POST，只关注GET请求渲染\n\n    return render_template('admin/article_upload.html', article=article, all_readers=all_readers, all_tags=all_tags)\n\n# --- 新增API端点 ---\n\n@admin_bp.route('/api/articles', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_articles():\n    \"\"\"获取文章列表API (管理员) - 支持通过作者名称或ID、标签名称或ID进行查询\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        search = request.args.get('search', '')\n        status_filter = request.args.get('status', '')\n        author_filter = request.args.get('author', '')  # 支持作者名称或ID\n        tag_filter = request.args.get('tag', '')        # 支持标签名称或ID\n        \n        query = Article.query.order_by(Article.created_at.desc())\n        \n        # 搜索标题\n        if search:\n            query = query.filter(Article.title.ilike(f'%{search}%'))\n        \n        # 状态过滤\n        if status_filter:\n            query = query.filter(Article.status == status_filter)\n        \n        # 作者过滤（支持名称或ID）\n        if author_filter:\n            if author_filter.isdigit():\n                # 按ID过滤（reader_id）\n                query = query.filter(Article.reader_id == int(author_filter))\n            else:\n                # 按名称过滤，需要join Author表\n                query = query.join(Author, Article.reader_id == Author.id).filter(Author.name.ilike(f'%{author_filter}%'))\n        \n        # 标签过滤（支持名称或ID）\n        if tag_filter:\n            if tag_filter.isdigit():\n                # 按标签ID过滤\n                query = query.join(Article.tags).filter(Tag.id == int(tag_filter))\n            else:\n                # 按标签名称过滤\n                query = query.join(Article.tags).filter(Tag.name.ilike(f'%{tag_filter}%'))\n\n        pagination = query.paginate(page=page, per_page=per_page, error_out=False)\n        articles_data = []\n        \n        for article in pagination.items:\n            # 使用增强的to_dict方法，返回完整的作者和标签信息\n            article_dict = article.to_dict(\n                include_content=False,\n                include_premium_content=False,\n                include_author_details=True,\n                include_tag_details=True\n            )\n            articles_data.append(article_dict)\n        \n        return jsonify({\n            \"success\": True,\n            \"articles\": articles_data,\n            \"pagination\": {\n                \"total\": pagination.total,\n                \"page\": pagination.page,\n                \"per_page\": pagination.per_page,\n                \"pages\": pagination.pages,\n                \"has_next\": pagination.has_next,\n                \"has_prev\": pagination.has_prev\n            },\n            \"filters\": {\n                \"search\": search,\n                \"status\": status_filter,\n                \"author\": author_filter,\n                \"tag\": tag_filter\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取管理员文章列表失败: {str(e)}\", exc_info=True)\n        return jsonify({'success': False, 'error': f'获取文章列表失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/articles/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_articles_stats():\n    \"\"\"获取文章统计信息API\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 基础统计\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter(Article.status == 'published').count()\n        draft_articles = Article.query.filter(Article.status == 'draft').count()\n        pending_articles = Article.query.filter(Article.status == 'pending').count()\n\n        # 时间统计\n        today = datetime.now().date()\n        week_ago = today - timedelta(days=7)\n        month_ago = today - timedelta(days=30)\n\n        today_published = Article.query.filter(\n            Article.status == 'published',\n            Article.created_at >= today\n        ).count()\n\n        week_published = Article.query.filter(\n            Article.status == 'published',\n            Article.created_at >= week_ago\n        ).count()\n\n        month_published = Article.query.filter(\n            Article.status == 'published',\n            Article.created_at >= month_ago\n        ).count()\n\n        # 状态分布\n        status_distribution = {\n            'published': published_articles,\n            'draft': draft_articles,\n            'pending': pending_articles\n        }\n\n        # 作者分布\n        author_distribution = []\n        try:\n            from sqlalchemy import func\n            author_stats = db.session.query(\n                Author.name,\n                func.count(Article.id).label('count')\n            ).join(Article, Article.reader_id == Author.id)\\\n             .group_by(Author.id, Author.name)\\\n             .order_by(func.count(Article.id).desc())\\\n             .limit(10).all()\n\n            author_distribution = [\n                {'name': stat.name, 'count': stat.count}\n                for stat in author_stats\n            ]\n        except Exception as e:\n            logger.warning(f\"获取作者分布失败: {str(e)}\")\n\n        stats = {\n            'total_articles': total_articles,\n            'published_articles': published_articles,\n            'draft_articles': draft_articles,\n            'pending_articles': pending_articles,\n            'today_published': today_published,\n            'this_week_published': week_published,\n            'this_month_published': month_published,\n            'status_distribution': status_distribution,\n            'author_distribution': author_distribution\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取文章统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/articles', methods=['POST'])\n@login_required\n@staff_required\ndef api_create_article():\n    data = request.get_json()\n    if not data:\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据\"}), 400\n\n    title = data.get('title')\n    content = data.get('content')\n    status = data.get('status', 'draft')\n    reader_id_str = data.get('reader_id') \n    premium_content = data.get('premium_content')\n    content_type = data.get('content_type', 'normal')\n    price_str = data.get('price')\n    premium_price_str = data.get('premium_price')\n    cover_url = data.get('cover_url')\n\n    # 获取其他可选字段\n    publish_date_str = data.get('publish_date')\n    language_code = data.get('language_code')\n    tags_data = data.get('tags', []) # 可以是ID数组或名称数组\n    author_id = data.get('author_id') # 新增：支持直接传递author_id\n    premium_content_chinese = data.get('premium_content_chinese')  # 新增：高级翻译纯中文内容\n    original_id = data.get('original_id')  # 新增：YouTube视频ID\n\n    if not title:\n        return jsonify({\"success\": False, \"error\": \"文章标题不能为空\"}), 400\n\n    # 检查original_id是否重复\n    if original_id:\n        existing_article = Article.query.filter_by(original_id=original_id).first()\n        if existing_article:\n            return jsonify({\"success\": False, \"error\": f\"Article with original_id '{original_id}' already exists (ID: {existing_article.id})\"}), 409\n\n    try:\n        # 改进：支持塔罗师名称和ID\n        reader_id = None\n        if reader_id_str and str(reader_id_str).strip() not in [\"\", \"None\", \"null\"]:\n            # 尝试作为ID处理\n            if str(reader_id_str).isdigit():\n                reader_id = int(reader_id_str)\n            else:\n                # 作为名称处理，查找对应的塔罗师\n                author_by_name = Author.query.filter_by(name=str(reader_id_str).strip()).first()\n                if author_by_name:\n                    reader_id = author_by_name.id\n                    logger.info(f\"通过名称 '{reader_id_str}' 找到塔罗师ID: {reader_id}\")\n                else:\n                    logger.warning(f\"未找到名称为 '{reader_id_str}' 的塔罗师\")\n        \n        # 改进：支持传递author_id，如果没有则使用当前用户\n        final_author_id = author_id if author_id else current_user.id\n        \n        # 智能处理语言代码\n        final_language_code = None\n        if language_code and language_code.strip():\n            final_language_code = language_code\n            logger.info(f\"使用用户提供的语言代码: {language_code}\")\n        else:\n            # 自动检测语言\n            detected_language = auto_detect_language(f\"{title} {content}\")\n            if detected_language:\n                final_language_code = detected_language\n                logger.info(f\"自动检测语言为: {detected_language}\")\n        \n        new_article = Article(\n            title=title,\n            content=content,\n            status=status,\n            reader_id=None, # 先设为None，后续验证后再赋值\n            author_id=final_author_id,\n            premium_content=premium_content,\n            premium_content_chinese=premium_content_chinese,  # 新增：高级翻译纯中文内容\n            original_id=original_id,  # 新增：YouTube视频ID\n            content_type=content_type,\n            has_premium=bool(premium_content and premium_content.strip()),\n            cover_url=cover_url,\n            language_code=final_language_code\n        )\n\n        # 智能处理发布日期\n        if publish_date_str and publish_date_str.strip():\n            try:\n                new_article.publish_date = date.fromisoformat(publish_date_str)\n                logger.info(f\"使用用户提供的发布日期: {publish_date_str}\")\n            except ValueError:\n                logger.warning(f\"创建文章时，提供的发布日期格式无效: '{publish_date_str}'，将使用自动检测\")\n                new_article.publish_date = auto_detect_publish_date(content, title)\n        else:\n            # 自动检测发布日期\n            new_article.publish_date = auto_detect_publish_date(content, title)\n            logger.info(f\"自动设置发布日期为: {new_article.publish_date}\")\n\n        # 设置价格：如果提供了价格则使用，否则使用统一默认价格\n        if price_str is not None:\n            try:\n                new_article.price = int(float(price_str) * 100)\n            except ValueError:\n                return jsonify({\"success\": False, \"error\": \"基础内容价格格式无效\"}), 400\n        else:\n            # 使用统一的默认价格：¥12.00\n            new_article.price = 1200\n\n        if premium_price_str is not None:\n            try:\n                new_article.premium_price = int(float(premium_price_str) * 100)\n            except ValueError:\n                return jsonify({\"success\": False, \"error\": \"高级内容价格格式无效\"}), 400\n        else:\n            # 使用统一的默认价格：¥20.00\n            new_article.premium_price = 2000\n        \n        # 验证并设置塔罗师ID\n        if reader_id:\n            author_exists = Author.query.get(reader_id)\n            if author_exists:\n                new_article.reader_id = reader_id\n            else:\n                logger.warning(f\"创建文章时，尝试关联无效的塔罗师ID: {reader_id} 到新文章 '{title}'\")\n\n        # 改进：处理标签关联 - 支持ID和名称混合\n        if tags_data:\n            for tag_item in tags_data:\n                tag = None\n                \n                # 判断是ID还是名称\n                if isinstance(tag_item, int) or (isinstance(tag_item, str) and tag_item.isdigit()):\n                    # 作为ID处理\n                    tag_id = int(tag_item)\n                    tag = Tag.query.get(tag_id)\n                    if not tag:\n                        logger.warning(f\"创建文章 '{title}' 时，标签ID {tag_id} 不存在\")\n                elif isinstance(tag_item, str):\n                    # 作为名称处理\n                    tag_name = tag_item.strip()\n                    if tag_name:\n                        tag = Tag.query.filter_by(name=tag_name).first()\n                        if not tag:\n                            # 自动创建新标签\n                            tag = Tag(name=tag_name)\n                            db.session.add(tag)\n                            logger.info(f\"自动创建新标签: '{tag_name}'\")\n                \n                if tag and tag not in new_article.tags:\n                    new_article.tags.append(tag)\n\n        db.session.add(new_article)\n        db.session.flush() # 获取 new_article.id 用于标签关联\n\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"文章创建成功\", \"article\": new_article.to_dict(include_content=False, include_premium_content=True, include_author_details=False, include_tag_details=True)}), 201\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"创建文章API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"创建文章失败: {str(e)}\"}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>', methods=['PUT'])\n@login_required\n@staff_required\ndef api_update_article(article_id):\n    article = Article.query.get_or_404(article_id)\n    data = request.get_json()\n\n    if not data:\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据\"}), 400\n\n    # 根据前端JS的逻辑，它可能会为不同表单的保存发送不同的payload\n    # 例如，一个payload只包含title和content，另一个只包含status和reader_id\n    \n    if 'title' in data:\n        article.title = data.get('title', article.title)\n    if 'content' in data:\n        article.content = data.get('content', article.content)\n\n    if 'premium_content' in data:\n        article.premium_content = data.get('premium_content', article.premium_content)\n        article.has_premium = bool(article.premium_content and article.premium_content.strip())\n\n    if 'status' in data:\n        article.status = data.get('status', article.status)\n    \n    # 改进：支持塔罗师名称和ID\n    if 'reader_id' in data:\n        reader_id_str = data.get('reader_id')\n        if reader_id_str is None or reader_id_str == \"\" or str(reader_id_str).lower() == 'none':\n            article.reader_id = None\n        else:\n            reader_id_val = None\n            \n            # 判断是ID还是名称\n            if str(reader_id_str).isdigit():\n                # 作为ID处理\n                reader_id_val = int(reader_id_str)\n                if reader_id_val == 0:\n                     article.reader_id = None\n                else:\n                    author_exists = Author.query.get(reader_id_val)\n                    if author_exists:\n                        article.reader_id = reader_id_val\n                    else:\n                        logger.warning(f\"更新文章 {article_id} 时，塔罗师ID {reader_id_val} 不存在\")\n                        article.reader_id = None\n            else:\n                # 作为名称处理\n                author_by_name = Author.query.filter_by(name=str(reader_id_str).strip()).first()\n                if author_by_name:\n                    article.reader_id = author_by_name.id\n                    logger.info(f\"更新文章 {article_id} 时，通过名称 '{reader_id_str}' 找到塔罗师ID: {author_by_name.id}\")\n                else:\n                    logger.warning(f\"更新文章 {article_id} 时，未找到名称为 '{reader_id_str}' 的塔罗师\")\n                    article.reader_id = None\n    \n    if 'content_type' in data:\n        article.content_type = data.get('content_type', article.content_type)\n\n    if 'price' in data:\n        price_str = data.get('price')\n        if price_str is not None:\n            try:\n                 article.price = int(float(price_str) * 100)\n            except ValueError:\n                return jsonify({\"success\": False, \"error\": \"基础内容价格格式无效\"}), 400\n    \n    if 'premium_price' in data:\n        premium_price_str = data.get('premium_price')\n        if premium_price_str is not None:\n            try:\n                 article.premium_price = int(float(premium_price_str) * 100)\n            except ValueError:\n                return jsonify({\"success\": False, \"error\": \"高级内容价格格式无效\"}), 400\n            \n    if 'cover_url' in data:\n        article.cover_url = data.get('cover_url', article.cover_url)\n        # 如果URL被清空，也清空本地上传的封面记录（如果模型中有相关字段）\n        # if not article.cover_url or not article.cover_url.strip():\n        #     article.cover_image = None \n            \n    # 智能处理发布日期\n    if 'publish_date' in data:\n        publish_date_str = data.get('publish_date')\n        if publish_date_str and publish_date_str.strip():\n            try:\n                article.publish_date = date.fromisoformat(publish_date_str)\n                logger.info(f\"更新文章 {article_id}，使用用户提供的发布日期: {publish_date_str}\")\n            except ValueError:\n                logger.warning(f\"更新文章 {article_id} 时，提供的发布日期格式无效: '{publish_date_str}'，将使用自动检测\")\n                article.publish_date = auto_detect_publish_date(data.get('content', article.content), data.get('title', article.title))\n        else:\n            # 如果传来空字符串，进行自动检测而不是清空\n            if publish_date_str == \"\":\n                article.publish_date = auto_detect_publish_date(data.get('content', article.content), data.get('title', article.title))\n                logger.info(f\"更新文章 {article_id}，自动设置发布日期为: {article.publish_date}\")\n            else:\n                article.publish_date = None # 只有明确传null才清空\n\n    # 智能处理语言代码\n    if 'language_code' in data:\n        language_code = data.get('language_code')\n        if language_code and language_code.strip():\n            article.language_code = language_code\n            logger.info(f\"更新文章 {article_id}，使用用户提供的语言代码: {language_code}\")\n        else:\n            # 如果没有提供语言代码，自动检测\n            content_for_detection = data.get('content', article.content)\n            title_for_detection = data.get('title', article.title)\n            detected_language = auto_detect_language(f\"{title_for_detection} {content_for_detection}\")\n            if detected_language:\n                article.language_code = detected_language\n                logger.info(f\"更新文章 {article_id}，自动检测语言为: {detected_language}\")\n            else:\n                article.language_code = None\n\n    # 改进：处理标签 - 支持ID和名称混合\n    if 'tags' in data:\n        tags_data = data.get('tags', [])\n        article.tags.clear() # 先清除旧的标签关联\n        if tags_data:\n            for tag_item in tags_data:\n                tag = None\n                \n                # 判断是ID还是名称\n                if isinstance(tag_item, int) or (isinstance(tag_item, str) and tag_item.isdigit()):\n                    # 作为ID处理\n                    tag_id = int(tag_item)\n                    tag = Tag.query.get(tag_id)\n                    if not tag:\n                        logger.warning(f\"更新文章 {article_id} 时，标签ID {tag_id} 不存在\")\n                elif isinstance(tag_item, str):\n                    # 作为名称处理\n                    tag_name = tag_item.strip()\n                    if tag_name:\n                        tag = Tag.query.filter_by(name=tag_name).first()\n                        if not tag:\n                            # 自动创建新标签\n                            tag = Tag(name=tag_name)\n                            db.session.add(tag)\n                            logger.info(f\"更新文章 {article_id} 时，自动创建新标签: '{tag_name}'\")\n                \n                if tag and tag not in article.tags:\n                        article.tags.append(tag)\n            \n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"文章更新成功\", \"article\": article.to_dict(include_content=True, include_premium_content=True, include_author_details=True, include_tag_details=True)})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新文章 {article_id} API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"更新文章失败: {str(e)}\"}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/premium', methods=['PUT'])\n@login_required\n@staff_required\ndef update_article_premium_content(article_id):\n    \"\"\"更新文章高级内容\"\"\"\n    logger.info(f\"收到更新文章高级内容请求，文章ID: {article_id}\")\n    article = Article.query.get_or_404(article_id)\n    data = request.get_json()\n    logger.info(f\"请求数据: {data}\")\n\n    if not data:\n        logger.warning(f\"更新文章 {article_id} 高级内容失败: 无效的请求数据\")\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据\"}), 400\n\n    # 更新高级内容\n    premium_content = data.get('premium_content')\n    logger.info(f\"更新文章 {article_id} 高级内容，内容长度: {len(str(premium_content)) if premium_content else 0}\")\n    \n    # 保存原始值用于对比\n    original_premium_content = article.premium_content\n    original_has_premium = article.has_premium\n    \n    article.premium_content = premium_content\n    article.has_premium = bool(premium_content and premium_content.strip())\n    \n    # 记录变化\n    logger.info(f\"高级内容变化: '{original_premium_content or 'None'}' -> '{premium_content or 'None'}'\")\n    logger.info(f\"has_premium变化: {original_has_premium} -> {article.has_premium}\")\n\n    try:\n        db.session.commit()\n        logger.info(f\"更新文章 {article_id} 高级内容成功\")\n        result = article.to_dict(include_content=True, include_premium_content=True, include_author_details=True, include_tag_details=True)\n        logger.info(f\"返回数据: has_premium={result.get('has_premium')}, premium_content长度={len(result.get('premium_content', '')) if result.get('premium_content') else 0}\")\n        return jsonify({\"success\": True, \"message\": \"高级内容更新成功\", \"article\": result})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新文章 {article_id} 高级内容失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"更新高级内容失败: {str(e)}\"}), 500\n\n# --- 结束高级内容API ---\n\n@admin_bp.route('/api/articles/<int:article_id>/price', methods=['PUT'])\n@login_required\n@finance_required\ndef update_article_price(article_id):\n    \"\"\"更新文章价格设置\"\"\"\n    article = Article.query.get_or_404(article_id)\n    data = request.get_json()\n\n    if not data:\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据\"}), 400\n\n    if 'price' in data:\n        try:\n            article.price = int(data['price'])\n        except (ValueError, TypeError):\n            return jsonify({\"success\": False, \"error\": \"基础内容价格格式无效\"}), 400\n\n    if 'premium_price' in data:\n        try:\n            article.premium_price = int(data['premium_price'])\n        except (ValueError, TypeError):\n            return jsonify({\"success\": False, \"error\": \"高级内容价格格式无效\"}), 400\n\n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"价格设置更新成功\", \"article\": article.to_dict(include_author_details=True, include_tag_details=True)})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新文章 {article_id} 价格失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"更新价格失败: {str(e)}\"}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/cover-url', methods=['PUT'])\n@login_required\n@staff_required\ndef update_article_cover_url(article_id):\n    \"\"\"更新文章封面URL\"\"\"\n    article = Article.query.get_or_404(article_id)\n    data = request.get_json()\n\n    if not data or 'cover_url' not in data:\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据，需要提供cover_url\"}), 400\n\n    article.cover_url = data['cover_url']\n\n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"封面URL更新成功\", \"article\": article.to_dict(include_author_details=True, include_tag_details=True)})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新文章 {article_id} 封面URL失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"更新封面URL失败: {str(e)}\"}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/cover', methods=['DELETE'])\n@login_required\n@staff_required\ndef delete_article_cover(article_id):\n    \"\"\"删除文章封面\"\"\"\n    article = Article.query.get_or_404(article_id)\n\n    # 清空封面相关字段\n    article.cover_url = None\n    # 如果有本地存储的封面图片，这里也需要清空\n    # article.cover_image = None\n\n    try:\n        db.session.commit()\n        return jsonify({\"success\": True, \"message\": \"封面已移除\"})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"删除文章 {article_id} 封面失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": f\"移除封面失败: {str(e)}\"}), 500\n\n# --- 塔罗师管理 (Authors) ---\n@admin_bp.route('/authors')\n@login_required\n@admin_required\ndef authors():\n    \"\"\"塔罗师管理页面\"\"\"\n    return render_template('admin/authors.html')\n\n@admin_bp.route('/api/authors', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_authors():\n    \"\"\"获取塔罗师列表API - 支持分页和搜索\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        search = request.args.get('search', '')\n\n        query = Author.query\n\n        # 搜索过滤\n        if search:\n            query = query.filter(Author.name.ilike(f'%{search}%'))\n\n        # 分页查询\n        pagination = query.order_by(Author.name).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        authors_data = []\n        for author in pagination.items:\n            author_dict = author.to_dict(include_article_count=True)\n            authors_data.append(author_dict)\n\n        return jsonify({\n            \"success\": True,\n            \"data\": {\n                \"data\": authors_data,\n                \"pagination\": {\n                    \"page\": pagination.page,\n                    \"pages\": pagination.pages,\n                    \"per_page\": pagination.per_page,\n                    \"total\": pagination.total,\n                    \"has_prev\": pagination.has_prev,\n                    \"has_next\": pagination.has_next\n                }\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取塔罗师列表API失败: {str(e)}\")\n        return jsonify({\"success\": False, \"error\": str(e)}), 500\n\n@admin_bp.route('/api/authors', methods=['POST'])\n@login_required\n@admin_required\ndef api_create_author():\n    \"\"\"创建塔罗师API\"\"\"\n    try:\n        data = request.get_json()\n        if not data or 'name' not in data:\n            return jsonify({'success': False, 'error': '缺少必要参数name'}), 400\n\n        name = data['name'].strip()\n        if not name:\n            return jsonify({'success': False, 'error': '塔罗师名称不能为空'}), 400\n\n        # 检查是否已存在同名塔罗师\n        existing_author = Author.query.filter_by(name=name).first()\n        if existing_author:\n            return jsonify({'success': False, 'error': f'塔罗师 \"{name}\" 已存在'}), 409\n\n        # 创建新塔罗师\n        author = Author(\n            name=name,\n            description=data.get('description', ''),\n            specialty=data.get('specialty', '塔罗占卜'),\n            experience_years=data.get('experience_years', 0),\n            contact_info=data.get('contact_info', ''),\n            is_active=data.get('is_active', True)\n        )\n\n        db.session.add(author)\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '塔罗师创建成功',\n            'data': author.to_dict(include_article_count=True)\n        }), 201\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"创建塔罗师API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/<int:author_id>', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_author(author_id):\n    \"\"\"获取塔罗师详情API\"\"\"\n    try:\n        author = Author.query.get_or_404(author_id)\n        return jsonify({\n            'success': True,\n            'data': author.to_dict(include_article_count=True)\n        })\n    except Exception as e:\n        logger.error(f\"获取塔罗师详情API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/<int:author_id>', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_author(author_id):\n    \"\"\"更新塔罗师API\"\"\"\n    try:\n        author = Author.query.get_or_404(author_id)\n        data = request.get_json()\n\n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n\n        # 更新名称\n        if 'name' in data:\n            new_name = data['name'].strip()\n            if not new_name:\n                return jsonify({'success': False, 'error': '塔罗师名称不能为空'}), 400\n\n            # 检查名称是否与其他塔罗师冲突\n            if new_name != author.name:\n                existing_author = Author.query.filter(\n                    Author.id != author_id,\n                    Author.name == new_name\n                ).first()\n                if existing_author:\n                    return jsonify({'success': False, 'error': f'塔罗师 \"{new_name}\" 已存在'}), 409\n\n            author.name = new_name\n\n        # 更新其他字段\n        if 'description' in data:\n            author.description = data['description']\n        if 'specialty' in data:\n            author.specialty = data['specialty']\n        if 'experience_years' in data:\n            author.experience_years = data['experience_years']\n        if 'contact_info' in data:\n            author.contact_info = data['contact_info']\n        if 'is_active' in data:\n            author.is_active = data['is_active']\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '塔罗师更新成功',\n            'data': author.to_dict(include_article_count=True)\n        })\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新塔罗师API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/<int:author_id>', methods=['DELETE'])\n@login_required\n@admin_required\ndef api_delete_author(author_id):\n    \"\"\"删除塔罗师API\"\"\"\n    try:\n        author = Author.query.get_or_404(author_id)\n\n        # 检查是否有关联的文章\n        if hasattr(author, 'articles') and author.articles:\n            return jsonify({\n                'success': False,\n                'error': f'无法删除塔罗师 \"{author.name}\"，因为还有 {len(author.articles)} 篇关联文章'\n            }), 400\n\n        db.session.delete(author)\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '塔罗师删除成功'\n        })\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"删除塔罗师API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/batch_delete', methods=['POST'])\n@login_required\n@admin_required\ndef api_batch_delete_authors():\n    \"\"\"批量删除塔罗师API\"\"\"\n    try:\n        data = request.get_json()\n        if not data or 'ids' not in data or not isinstance(data['ids'], list):\n            return jsonify({'success': False, 'error': '无效的请求数据，需要一个包含ID列表的 \"ids\" 键'}), 400\n\n        ids_to_delete = data['ids']\n        if not ids_to_delete:\n            return jsonify({'success': False, 'error': '没有提供要删除的塔罗师ID'}), 400\n\n        # 查找要删除的塔罗师\n        authors_to_delete = Author.query.filter(Author.id.in_(ids_to_delete)).all()\n\n        if not authors_to_delete:\n            return jsonify({'success': False, 'error': '没有找到要删除的塔罗师'}), 404\n\n        # 检查是否有关联的文章\n        authors_with_articles = []\n        for author in authors_to_delete:\n            if hasattr(author, 'articles') and author.articles:\n                authors_with_articles.append(f'{author.name}({len(author.articles)}篇文章)')\n\n        if authors_with_articles:\n            return jsonify({\n                'success': False,\n                'error': f'以下塔罗师还有关联文章，无法删除：{\", \".join(authors_with_articles)}'\n            }), 400\n\n        # 执行删除\n        for author in authors_to_delete:\n            db.session.delete(author)\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': f'成功删除 {len(authors_to_delete)} 个塔罗师'\n        })\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"批量删除塔罗师API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/authors/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_authors_stats():\n    \"\"\"获取塔罗师统计信息API\"\"\"\n    try:\n        from sqlalchemy import func\n\n        # 基础统计\n        total_authors = Author.query.count()\n        featured_authors = Author.query.filter(Author.featured == True).count()\n\n        # 有文章的塔罗师数量\n        active_authors = db.session.query(Author.id).join(\n            Article, Article.reader_id == Author.id\n        ).distinct().count()\n\n        # 总文章数\n        total_articles_by_authors = Article.query.filter(\n            Article.reader_id.isnot(None)\n        ).count()\n\n        # 平均每个塔罗师的文章数\n        avg_articles_per_author = (\n            total_articles_by_authors / total_authors\n            if total_authors > 0 else 0\n        )\n\n        # 顶级塔罗师（按文章数排序）\n        top_authors = db.session.query(\n            Author.name,\n            func.count(Article.id).label('article_count'),\n            func.sum(Article.view_count).label('total_views')\n        ).join(Article, Article.reader_id == Author.id)\\\n         .group_by(Author.id, Author.name)\\\n         .order_by(func.count(Article.id).desc())\\\n         .limit(10).all()\n\n        top_authors_data = []\n        for author in top_authors:\n            top_authors_data.append({\n                'name': author.name,\n                'article_count': author.article_count,\n                'total_views': author.total_views or 0\n            })\n\n        # 塔罗师表现数据\n        author_performance = []\n        for author in top_authors:\n            # 计算平均评分（这里使用模拟数据，实际应该从评分表获取）\n            rating = 4.5 + (hash(author.name) % 6) / 10  # 模拟评分 4.5-5.0\n            author_performance.append({\n                'name': author.name,\n                'articles': author.article_count,\n                'views': author.total_views or 0,\n                'rating': round(rating, 1)\n            })\n\n        stats = {\n            'total_authors': total_authors,\n            'active_authors': active_authors,\n            'featured_authors': featured_authors,\n            'total_articles_by_authors': total_articles_by_authors,\n            'avg_articles_per_author': round(avg_articles_per_author, 1),\n            'top_authors': top_authors_data,\n            'author_performance': author_performance\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取塔罗师统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n\n\n\n\n\n\n@admin_bp.route('/api/article-buyers/<int:article_id>', methods=['GET'])\n@login_required\n@admin_required\ndef get_article_buyers(article_id):\n    \"\"\"获取文章购买者列表API\"\"\"\n    try:\n        # 获取文章信息\n        article = Article.query.get_or_404(article_id)\n\n        # 获取购买记录 - 只查询已完成的购买\n        purchases = Purchase.query.filter_by(\n            article_id=article_id,\n            status='completed'\n        ).all()\n\n        # 如果没有购买记录，返回空列表\n        if not purchases:\n            return jsonify({\n                'success': True,\n                'article': {\n                    'id': article.id,\n                    'title': article.title,\n                    'status': article.status,\n                    'created_at': article.created_at.isoformat() if article.created_at else None\n                },\n                'buyers': [],\n                'total_count': 0\n            })\n\n        # 整理购买者信息 - 基于优化后的Purchase表\n        buyers = []\n        for purchase in purchases:\n            # 获取用户信息\n            user = User.query.get(purchase.user_id) if purchase.user_id else None\n\n            # 获取支付摘要（兼容当前数据库结构）\n            try:\n                payment_summary = purchase.get_payment_summary()\n            except:\n                # 如果新字段不存在，使用默认值\n                payment_summary = {\n                    'composition': 'unknown',\n                    'cash': {'amount': 0, 'currency': 'CNY', 'percentage': 0},\n                    'points': {'amount': 0, 'value': 0, 'rate': 0, 'percentage': 0},\n                    'quota': {'amount': 0, 'type': '', 'value': 0, 'percentage': 0},\n                    'total': {'value': 0, 'paid': 0, 'saved': 0}\n                }\n\n            # 查找积分使用记录（用于获取余额变化信息）\n            points_record = None\n            points_balance_before = None\n            points_balance_after = None\n\n            if purchase.user_id and purchase.article_id and purchase.points_used > 0:\n                points_record = PointsRecord.query.filter_by(\n                    user_id=purchase.user_id,\n                    type='used',\n                    order_id=f'ART{purchase.article_id}'\n                ).first()\n\n                if points_record and hasattr(points_record, 'balance_before'):\n                    points_balance_before = points_record.balance_before\n                    points_balance_after = points_record.balance_after\n\n            # 获取用户当前财务状态\n            user_finance = UserFinance.query.get(purchase.user_id) if purchase.user_id else None\n\n            # 安全获取字段值（兼容当前数据库结构）\n            def safe_get_attr(obj, attr, default=0):\n                try:\n                    return getattr(obj, attr, default)\n                except:\n                    return default\n\n            # 计算基本信息\n            cash_amount = safe_get_attr(purchase, 'cash_amount', purchase.amount or 0)\n            points_used = safe_get_attr(purchase, 'points_used', 0)\n            points_value = safe_get_attr(purchase, 'points_value', 0)\n            quota_used = safe_get_attr(purchase, 'quota_used', 0)\n            quota_type = safe_get_attr(purchase, 'quota_type', purchase.content_type)\n            quota_value = safe_get_attr(purchase, 'quota_value', 0)\n            total_value = safe_get_attr(purchase, 'total_value', cash_amount + points_value + quota_value)\n            total_amount = safe_get_attr(purchase, 'total_amount', cash_amount)\n            payment_composition = safe_get_attr(purchase, 'payment_composition', 'unknown')\n\n            # 如果没有配额价值但有配额使用，计算配额价值\n            if quota_value == 0 and quota_used > 0:\n                if purchase.content_type == 'premium':\n                    quota_value = quota_used * 2999  # 高级内容29.99元\n                else:\n                    quota_value = quota_used * 999   # 基础内容9.99元\n                total_value = cash_amount + points_value + quota_value\n\n            # 确定支付构成和用户状态\n            if payment_composition == 'unknown':\n                if quota_used > 0 and cash_amount == 0 and points_used == 0:\n                    # 纯配额支付\n                    if quota_type == 'premium':\n                        payment_composition = '高级配额'\n                    else:\n                        payment_composition = '基础配额'\n                elif points_used > 0 and quota_used == 0 and cash_amount == 0:\n                    payment_composition = '积分支付'\n                elif cash_amount > 0 and quota_used == 0 and points_used == 0:\n                    payment_composition = '现金支付'\n                elif quota_used > 0 and (cash_amount > 0 or points_used > 0):\n                    payment_composition = '混合支付'\n                else:\n                    payment_composition = '自动赠送'\n\n            # 确定获得方式\n            acquisition_method = '购买获得'\n            if quota_used > 0 and cash_amount == 0 and points_used == 0:\n                acquisition_method = '配额消耗'\n            elif user and hasattr(user, 'is_admin') and user.is_admin:\n                acquisition_method = '管理员权限'\n\n            # 计算节省金额（仅在有实际支付时显示）\n            total_saved = points_value + quota_value\n            savings_percentage = round(total_saved / total_value * 100, 2) if total_value > 0 else 0\n\n            # 获取用户VIP等级\n            user_vip_level = 0\n            if user_finance:\n                user_vip_level = getattr(user_finance, 'vip_level', 0)\n\n            buyer = {\n                # 基础信息\n                'purchase_id': purchase.id,\n                'user_id': purchase.user_id,\n                'username': user.username if user else '未知用户',\n                'email': user.email if user and user.email else '未提供',\n                'content_type': purchase.content_type,\n                'purchase_time': purchase.purchase_date.isoformat() if purchase.purchase_date else None,\n\n                # 支付信息（简化显示）\n                'amount_paid': cash_amount / 100 if cash_amount else 0,  # 转换为元\n                'payment_method': purchase.payment_method or '未知',\n                'transaction_id': purchase.transaction_id or '',\n\n                # 积分消费详情（仅在有积分消费时显示）\n                'points_used': points_used if points_used > 0 else 0,\n                'points_balance_before': points_balance_before,\n                'points_balance_after': points_balance_after,\n                'points_record_id': points_record.id if points_record else None,\n\n                # 配额消费详情（仅在有配额消费时显示）\n                'quota_used': quota_used if quota_used > 0 else 0,\n                'quota_type': quota_type if quota_used > 0 else '',\n                'exchange_used': quota_used > 0,\n                'quota_record_id': None,\n\n                # 购买时用户状态\n                'vip_level_at_purchase': user_vip_level,\n\n                # 消费方式分析（简化）\n                'cost_type': payment_composition,\n                'cost_amount': total_value / 100 if total_value else 0,  # 转换为元\n                'consumption_record_id': purchase.id,\n\n                # 支付状态\n                'is_free_purchase': total_amount == 0 and (points_used > 0 or quota_used > 0),\n                'is_mixed_payment': payment_composition == 'mixed',\n\n                # 用户状态\n                'vip_level_at_purchase': user_finance.vip_level if user_finance else 0,\n\n                # 向后兼容字段\n                'amount_paid': total_amount / 100.0,  # 转换为元\n                'price': total_amount / 100.0,  # 前端期望的字段\n                'cost_type': payment_composition,\n                'cost_amount': total_saved if total_amount == 0 else total_amount,\n\n                # 完整的支付摘要\n                'payment_summary': payment_summary,\n\n                # 调试信息\n                'debug_info': {\n                    'purchase_amount': purchase.amount,\n                    'calculated_total_value': total_value,\n                    'calculated_total_amount': total_amount,\n                    'purchase_status': purchase.status,\n                    'has_points_record': points_record is not None,\n                    'user_finance_exists': user_finance is not None,\n                    'has_new_fields': hasattr(purchase, 'cash_amount')\n                }\n            }\n            buyers.append(buyer)\n\n        return jsonify({\n            'success': True,\n            'article': {\n                'id': article.id,\n                'title': article.title,\n                'status': article.status,\n                'created_at': article.created_at.isoformat() if article.created_at else None\n            },\n            'buyers': buyers,\n            'total_count': len(buyers)\n        })\n\n    except Exception as e:\n        logger.error(f\"获取文章购买者列表失败: {str(e)}\", exc_info=True)\n        return jsonify({\n            'success': False,\n            'error': f'获取文章购买者列表失败: {str(e)}'\n        }), 500\n\n@admin_bp.route('/api/debug-article-records/<int:article_id>', methods=['GET'])\n@login_required\n@admin_required\ndef debug_article_records(article_id):\n    \"\"\"调试API - 查看文章相关的所有记录\"\"\"\n    try:\n        # 获取所有购买记录\n        purchases = Purchase.query.filter_by(article_id=article_id).all()\n\n        debug_data = {\n            'article_id': article_id,\n            'purchases': [],\n            'all_points_records': [],\n            'all_quota_records': [],\n            'all_consumption_records': []\n        }\n\n        # 购买记录\n        for purchase in purchases:\n            debug_data['purchases'].append({\n                'id': purchase.id,\n                'user_id': purchase.user_id,\n                'amount': purchase.amount,\n                'content_type': purchase.content_type,\n                'purchase_date': purchase.purchase_date.isoformat() if purchase.purchase_date else None,\n                'status': purchase.status,\n                'quota_used': getattr(purchase, 'quota_used', 'N/A'),\n                'quota_type': getattr(purchase, 'quota_type', 'N/A')\n            })\n\n        # 获取所有用户的积分记录（包含这篇文章）\n        user_ids = [p.user_id for p in purchases]\n        if user_ids:\n            points_records = PointsRecord.query.filter(\n                PointsRecord.user_id.in_(user_ids),\n                PointsRecord.order_id.like(f'%{article_id}%')\n            ).all()\n\n            for record in points_records:\n                debug_data['all_points_records'].append({\n                    'id': record.id,\n                    'user_id': record.user_id,\n                    'amount': record.amount,\n                    'type': record.type,\n                    'order_id': record.order_id,\n                    'created_at': record.created_at.isoformat() if record.created_at else None\n                })\n\n        # 获取所有配额记录\n        if user_ids:\n            quota_records = QuotaUsageRecord.query.filter(\n                QuotaUsageRecord.user_id.in_(user_ids),\n                QuotaUsageRecord.article_id == article_id\n            ).all()\n\n            for record in quota_records:\n                debug_data['all_quota_records'].append({\n                    'id': record.id,\n                    'user_id': record.user_id,\n                    'article_id': record.article_id,\n                    'quota_type': record.quota_type,\n                    'amount_used': record.amount_used,\n                    'exchange_used': record.exchange_used,\n                    'vip_level_at_time': record.vip_level_at_time,\n                    'created_at': record.created_at.isoformat() if record.created_at else None\n                })\n\n        # 获取所有消费记录\n        if user_ids:\n            consumption_records = UserConsumptionRecord.query.filter(\n                UserConsumptionRecord.user_id.in_(user_ids),\n                UserConsumptionRecord.article_id == article_id\n            ).all()\n\n            for record in consumption_records:\n                debug_data['all_consumption_records'].append({\n                    'id': record.id,\n                    'user_id': record.user_id,\n                    'article_id': record.article_id,\n                    'content_type': record.content_type,\n                    'cost_type': record.cost_type,\n                    'cost_amount': record.cost_amount,\n                    'created_at': record.created_at.isoformat() if record.created_at else None\n                })\n\n        return jsonify({\n            'success': True,\n            'debug_data': debug_data\n        })\n\n    except Exception as e:\n        logger.error(f\"调试API失败: {str(e)}\", exc_info=True)\n        return jsonify({\n            'success': False,\n            'error': f'调试API失败: {str(e)}'\n        }), 500\n\n@admin_bp.route('/api/articles/batch_delete', methods=['POST'])\n@login_required\n@admin_required\ndef api_batch_delete_articles():\n    \"\"\"批量删除文章 (API)\"\"\"\n    data = request.get_json()\n    if not data or 'ids' not in data or not isinstance(data['ids'], list):\n        return jsonify({\"success\": False, \"error\": \"无效的请求数据，需要一个包含ID列表的 'ids' 键。\"}), 400\n\n    ids_to_delete = data['ids']\n    if not ids_to_delete:\n        return jsonify({\"success\": False, \"error\": \"没有提供要删除的文章ID。\"}), 400\n\n    deleted_count = 0\n    errors = []\n\n    for article_id_str in ids_to_delete:\n        try:\n            article_id = int(article_id_str) # 确保是整数\n            article = Article.query.get(article_id)\n            if article:\n                # 这里可以集成更复杂的删除逻辑，比如调用一个负责删除文章及其关联数据（如图片）的函数\n                # 目前，我们直接删除文章对象，与 backup_delete_article 的核心逻辑一致\n                db.session.delete(article)\n                deleted_count += 1\n                logger.info(f\"批量删除：文章 ID {article_id} 已标记为删除。\")\n            else:\n                errors.append(f\"ID {article_id} 未找到对应的文章。\")\n                logger.warning(f\"批量删除：未找到文章 ID {article_id}。\")\n        except ValueError:\n            errors.append(f\"无效的文章ID格式: '{article_id_str}'\")\n            logger.warning(f\"批量删除：无效的文章ID格式 '{article_id_str}'\")\n        except Exception as e:\n            # 捕获删除单个文章时可能发生的其他DB相关错误\n            db.session.rollback() # 单个错误时，回滚当前更改，但继续处理下一个\n            errors.append(f\"删除ID {article_id_str} 时发生数据库错误: {str(e)}\")\n            logger.error(f\"批量删除文章中，删除ID {article_id_str} 失败: {str(e)}\")\n    \n    if not errors and deleted_count > 0:\n        # 全部成功\n        try:\n            db.session.commit()\n            logger.info(f\"批量删除：成功删除 {deleted_count} 篇文章并已提交事务。\")\n            return jsonify({\"success\": True, \"message\": f\"成功删除了 {deleted_count} 篇文章。\"})\n        except Exception as e:\n            db.session.rollback()\n            logger.error(f\"批量删除文章提交事务时失败: {str(e)}\")\n            return jsonify({\"success\": False, \"error\": f\"提交批量删除操作时发生严重错误: {str(e)}\"}), 500\n    elif errors:\n        # 如果有任何错误，我们选择回滚所有本次批量操作中已标记删除的内容，\n        # 以保证原子性，或者至少不提交部分成功的删除。\n        # 注意：上面循环中对单个错误的 rollback() 可能已经回滚了部分。\n        # 这里确保最终的 commit 不会发生，或者如果需要，显式地回滚整个事务。\n        # 根据具体需求，也可以选择提交已成功的，并报告失败的。\n        # 当前策略：如果任何一个失败，则不提交任何删除，并报告所有错误。\n        db.session.rollback() # 确保整体回滚\n        message = f\"批量删除操作中有 {len(errors)} 个错误，所有更改已回滚。\"\n        logger.warning(f\"批量删除文章操作失败，错误数量: {len(errors)}. 详情: {'; '.join(errors)}\")\n        return jsonify({\"success\": False, \"message\": message, \"error\": \"部分或全部文章删除失败。\", \"details\": errors}), 400 # 或 207 Multi-Status\n    elif deleted_count == 0 and not errors:\n         # 没有选中任何有效的、存在的文章\n        logger.info(\"批量删除：没有有效的文章被删除（可能ID列表为空或所有ID都无效）。\")\n        return jsonify({\"success\": False, \"message\": \"没有有效的文章被删除。\"}), 400\n    else: # deleted_count > 0 and not errors (这种情况被第一个if分支处理)\n        # 逻辑上这个else分支不应该被达到，但作为保险\n        db.session.rollback()\n        logger.error(\"批量删除文章时出现未预期的逻辑分支。\")\n        return jsonify({\"success\": False, \"error\": \"批量删除过程中发生未知错误。\"}), 500\n\n\n\n# 管理员阅读模式路由\n@admin_bp.route('/reading/<int:reading_id>')\n@login_required\n@admin_required\ndef admin_reading(reading_id):\n    \"\"\"管理员阅读模式视图 - 用于测试和管理阅读模式功能\"\"\"\n    try:\n        # 获取文章内容，与普通阅读视图逻辑类似\n        from models import Article, ReadingPreference, Author\n        from flask_login import current_user\n\n        reading = Article.query.get_or_404(reading_id)\n\n        # 获取作者信息\n        author_name = \"未知\"\n        reader = None\n        if reading.reader_id:\n            reader = Author.query.get(reading.reader_id)\n            if reader:\n                author_name = reader.name\n\n        # 获取用户阅读偏好\n        reading_preference = None\n        if current_user.is_authenticated:\n            reading_preference = ReadingPreference.query.filter_by(user_id=current_user.id).first()\n\n        # 返回模板，传递完整的变量（参考reading.py的实现）\n        return render_template('article_reading.html',\n                            article=reading,\n                            reading=reading,\n                            author_name=author_name,\n                            reader=reader,\n                            reading_preference=reading_preference,\n                            reading_preferences=reading_preference,  # 兼容性\n                            is_admin=True,\n                            # 管理员有完整访问权限\n                            has_basic_access=True,\n                            has_premium_access=True,\n                            can_access_basic=True,\n                            can_access_premium=True,\n                            # 其他必要变量\n                            basic_message=\"\",\n                            premium_message=\"\",\n                            basic_price=0,\n                            premium_price=0,\n                            is_bookmarked=False)\n    \n    except Exception as e:\n        logger.error(f\"管理员阅读模式页面错误: {str(e)}\")\n        return render_template('error.html', error=f\"加载管理员阅读模式时出错，请稍后再试。错误: {str(e)}\")\n\n@admin_bp.route('/settings')\n@login_required\n@admin_required\ndef system_settings():\n    \"\"\"显示系统设置页面\"\"\"\n    try:\n        # 获取所有设置\n        settings = SystemSetting.query.all()\n        settings_dict = {s.key: s.value for s in settings}\n        \n        return render_template('admin/settings.html', settings=settings_dict)\n    except Exception as e:\n        logger.error(f\"加载系统设置页面失败: {str(e)}\")\n        flash('加载系统设置时出错', 'error')\n        return redirect(url_for('admin.dashboard'))\n\n@admin_bp.route('/api/settings', methods=['GET'])\n@login_required\n@admin_required\ndef get_settings():\n    \"\"\"获取系统设置\"\"\"\n    try:\n        settings = SystemSetting.query.all()\n        settings_dict = {s.key: s.value for s in settings}\n        \n        return jsonify({\n            'success': True,\n            'settings': settings_dict\n        })\n    except Exception as e:\n        logger.error(f\"获取系统设置API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/settings', methods=['POST'])\n@login_required\n@admin_required\ndef update_settings():\n    \"\"\"更新系统设置\"\"\"\n    try:\n        data = request.get_json()\n        \n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n        \n        # 遍历所有设置键值对进行更新或创建\n        for key, value in data.items():\n            # 查找现有设置\n            setting = SystemSetting.query.filter_by(key=key).first()\n            \n            if setting:\n                # 更新现有设置\n                setting.value = value\n            else:\n                # 创建新设置\n                new_setting = SystemSetting(key=key, value=value)\n                db.session.add(new_setting)\n        \n        db.session.commit()\n        return jsonify({'success': True, 'message': '系统设置已更新'})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新系统设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# --- 工单管理 ---\n@admin_bp.route('/tickets')\n@login_required\n@staff_required\ndef tickets():\n    \"\"\"工单管理页面\"\"\"\n    try:\n        from models import SupportTicket\n        from datetime import datetime\n\n        page = request.args.get('page', 1, type=int)\n        per_page = 20\n        query = SupportTicket.query.order_by(SupportTicket.created_at.desc())\n\n        # 获取筛选参数\n        status = request.args.get('status', '')\n        priority = request.args.get('priority', '')\n        category = request.args.get('category', '')\n        start_date = request.args.get('start_date', '')\n        end_date = request.args.get('end_date', '')\n\n        # 状态筛选\n        if status:\n            query = query.filter(SupportTicket.status == status)\n\n        # 优先级筛选\n        if priority:\n            query = query.filter(SupportTicket.priority == priority)\n\n        # 分类筛选\n        if category:\n            query = query.filter(SupportTicket.category == category)\n\n        # 时间筛选\n        if start_date:\n            try:\n                start_dt = datetime.strptime(start_date, '%Y-%m-%d')\n                query = query.filter(SupportTicket.created_at >= start_dt)\n            except ValueError:\n                flash('开始日期格式错误', 'error')\n\n        if end_date:\n            try:\n                end_dt = datetime.strptime(end_date, '%Y-%m-%d')\n                # 结束日期包含当天的所有时间\n                end_dt = end_dt.replace(hour=23, minute=59, second=59)\n                query = query.filter(SupportTicket.created_at <= end_dt)\n            except ValueError:\n                flash('结束日期格式错误', 'error')\n\n        pagination = query.paginate(page=page, per_page=per_page)\n        tickets = pagination.items\n\n        return render_template('admin/ticket_list.html',\n                              tickets=tickets,\n                              pagination=pagination,\n                              status=status,\n                              priority=priority,\n                              category=category,\n                              start_date=start_date,\n                              end_date=end_date)\n    except Exception as e:\n        logger.error(f\"加载工单管理页面失败: {str(e)}\")\n        flash('加载工单管理页面时出错', 'error')\n        return redirect(url_for('admin.dashboard'))\n\n@admin_bp.route('/tickets/<int:ticket_id>')\n@login_required\n@staff_required\ndef ticket_detail(ticket_id):\n    \"\"\"工单详情页面\"\"\"\n    try:\n        from models import SupportTicket, TicketLog\n        ticket = SupportTicket.query.get_or_404(ticket_id)\n        logs = TicketLog.query.filter_by(ticket_id=ticket_id).order_by(TicketLog.created_at.asc()).all()\n\n        return render_template('admin/ticket_detail.html', ticket=ticket, logs=logs)\n    except Exception as e:\n        logger.error(f\"加载工单详情页面失败: {str(e)}\")\n        flash('加载工单详情时出错', 'error')\n        return redirect(url_for('admin.tickets'))\n\n@admin_bp.route('/api/tickets', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_tickets():\n    \"\"\"获取工单列表API\"\"\"\n    try:\n        from models import SupportTicket\n\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        status = request.args.get('status', 'all')\n        category = request.args.get('category', 'all')\n        priority = request.args.get('priority', 'all')\n\n        query = SupportTicket.query\n\n        if status != 'all':\n            query = query.filter_by(status=status)\n\n        if category != 'all':\n            query = query.filter_by(category_primary=category)\n\n        if priority != 'all':\n            query = query.filter_by(priority=priority)\n\n        tickets = query.order_by(SupportTicket.created_at.desc()).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        ticket_list = []\n        for ticket in tickets.items:\n            # 获取用户VIP信息 - 仅管理员可见\n            vip_level = \"普通用户\"\n            if current_user.can_access_finance() and ticket.user.finance and ticket.user.finance.vip_level > 0:\n                vip_levels = {1: \"初级VIP\", 2: \"高级VIP\"}\n                vip_level = vip_levels.get(ticket.user.finance.vip_level, \"VIP用户\")\n\n            ticket_data = {\n                'id': ticket.id,\n                'ticket_number': ticket.get_ticket_number(),\n                'subject': ticket.subject,\n                'content': ticket.content,\n                'category_primary': ticket.category_primary,\n                'category_secondary': ticket.category_secondary,\n                'status': ticket.status,\n                'priority': ticket.priority,\n                'created_at': ticket.created_at.isoformat(),\n                'updated_at': ticket.updated_at.isoformat(),\n                'resolved_at': ticket.resolved_at.isoformat() if ticket.resolved_at else None,\n                'admin_reply': ticket.admin_reply,\n                'user': {\n                    'id': ticket.user.id,\n                    'username': ticket.user.username,\n                    'email': ticket.user.email,\n                    'vip_level': vip_level\n                },\n                'admin': {\n                    'id': ticket.admin.id,\n                    'username': ticket.admin.username\n                } if ticket.admin else None,\n                'staff': {\n                    'id': ticket.staff.id,\n                    'username': ticket.staff.username\n                } if ticket.staff else None\n            }\n            ticket_list.append(ticket_data)\n\n        return jsonify({\n            'success': True,\n            'tickets': ticket_list,\n            'pagination': {\n                'page': tickets.page,\n                'pages': tickets.pages,\n                'per_page': tickets.per_page,\n                'total': tickets.total,\n                'has_next': tickets.has_next,\n                'has_prev': tickets.has_prev\n            }\n        })\n\n    except Exception as e:\n        logger.error(f\"获取工单列表API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/tickets/stats', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_tickets_stats():\n    \"\"\"获取工单统计信息API\"\"\"\n    try:\n        from models import SupportTicket\n        from datetime import datetime, timedelta\n        from sqlalchemy import func\n\n        # 基础统计\n        total_tickets = SupportTicket.query.count()\n        open_tickets = SupportTicket.query.filter(SupportTicket.status == 'open').count()\n        closed_tickets = SupportTicket.query.filter(SupportTicket.status == 'closed').count()\n\n        # 时间统计\n        today = datetime.now().date()\n        today_created = SupportTicket.query.filter(\n            func.date(SupportTicket.created_at) == today\n        ).count()\n\n        today_closed = SupportTicket.query.filter(\n            SupportTicket.status == 'closed',\n            func.date(SupportTicket.updated_at) == today\n        ).count()\n\n        # 状态分布\n        status_stats = db.session.query(\n            SupportTicket.status,\n            func.count(SupportTicket.id).label('count')\n        ).group_by(SupportTicket.status).all()\n\n        status_distribution = {stat.status: stat.count for stat in status_stats}\n\n        # 优先级分布\n        priority_stats = db.session.query(\n            SupportTicket.priority,\n            func.count(SupportTicket.id).label('count')\n        ).group_by(SupportTicket.priority).all()\n\n        priority_distribution = {stat.priority: stat.count for stat in priority_stats}\n\n        # 分类分布\n        category_stats = db.session.query(\n            SupportTicket.category,\n            func.count(SupportTicket.id).label('count')\n        ).group_by(SupportTicket.category).all()\n\n        category_distribution = {stat.category: stat.count for stat in category_stats}\n\n        # 员工表现（模拟数据）\n        staff_performance = [\n            {'staff': '客服A', 'assigned': 23, 'resolved': 20, 'avg_time': 18.5},\n            {'staff': '客服B', 'assigned': 19, 'resolved': 17, 'avg_time': 22.3}\n        ]\n\n        stats = {\n            'total_tickets': total_tickets,\n            'open_tickets': open_tickets,\n            'closed_tickets': closed_tickets,\n            'today_created': today_created,\n            'today_closed': today_closed,\n            'avg_response_time': 2.5,  # 模拟数据\n            'avg_resolution_time': 24.5,  # 模拟数据\n            'status_distribution': status_distribution,\n            'priority_distribution': priority_distribution,\n            'category_distribution': category_distribution,\n            'staff_performance': staff_performance\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取工单统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/tickets/<int:ticket_id>/reply', methods=['POST'])\n@login_required\n@staff_required\ndef api_reply_ticket(ticket_id):\n    \"\"\"回复工单API\"\"\"\n    try:\n        from models import SupportTicket, TicketLog\n        from utils.customer_service import send_ticket_notification, update_ticket_txt_log, create_ticket_log\n\n        ticket = SupportTicket.query.get_or_404(ticket_id)\n        data = request.get_json()\n\n        if not data.get('reply'):\n            return jsonify({'success': False, 'error': '回复内容不能为空'}), 400\n\n        # 更新工单\n        ticket.admin_reply = data.get('reply')\n        ticket.admin_id = current_user.id\n        ticket.status = data.get('status', 'processing')\n        ticket.updated_at = datetime.utcnow()\n\n        if data.get('status') == 'resolved':\n            ticket.resolved_at = datetime.utcnow()\n\n        # 记录操作日志\n        create_ticket_log(\n            ticket_id=ticket.id,\n            action_type='replied',\n            action_by=current_user.id,\n            action_details=f\"回复: {data.get('reply')[:50]}...\"\n        )\n\n        db.session.commit()\n\n        # 发送邮件通知用户\n        try:\n            send_ticket_notification(ticket, 'replied')\n        except Exception as e:\n            logger.error(f\"发送回复邮件通知失败: {e}\")\n\n        # 更新TXT日志\n        try:\n            update_ticket_txt_log(ticket, '客服回复', data.get('reply'))\n        except Exception as e:\n            logger.error(f\"更新TXT日志失败: {e}\")\n\n        return jsonify({'success': True, 'message': '回复成功'})\n\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"回复工单失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\ndef get_vip_status_indicator(user_id):\n    \"\"\"获取VIP状态指示器\"\"\"\n    try:\n        from vip_status_indicator import VIPStatusIndicator\n        return VIPStatusIndicator.check_vip_status(user_id)\n    except Exception as e:\n        return {\n            'status': 'red',\n            'message': '检查失败',\n            'details': str(e)\n        }\n\ndef get_vip_status_summary():\n    \"\"\"获取VIP状态汇总\"\"\"\n    try:\n        from vip_status_indicator import VIPStatusIndicator\n        return VIPStatusIndicator.get_status_summary()\n    except Exception as e:\n        return {\n            'total_vip_users': 0,\n            'green': 0,\n            'yellow': 0,\n            'red': 0,\n            'health_rate': 0\n        }\n\n@admin_bp.route('/users')\n@login_required\n@staff_required\ndef users_dashboard():\n    \"\"\"显示用户管理页面 - 简化版本\"\"\"\n    try:\n        # 获取基本用户列表\n        page = request.args.get('page', 1, type=int)\n        per_page = 20\n\n        # 简单查询，避免复杂的join操作\n        users_query = User.query.order_by(User.created_at.desc())\n\n        # 基本搜索\n        search = request.args.get('search', '')\n        if search:\n            users_query = users_query.filter(User.username.contains(search))\n\n        pagination = users_query.paginate(page=page, per_page=per_page, error_out=False)\n        users = pagination.items\n\n        # 使用财务计算系统获取准确数据\n        from services.finance_calculator import FinanceCalculator\n\n        # 构建用户行HTML\n        user_rows = []\n        for user in users:\n            role_map = {'admin': '管理员', 'staff': '员工', 'customer_service': '客服', 'user': '普通用户'}\n            role_text = role_map.get(user.role, user.role)\n\n            if user.role == 'admin':\n                role_class = 'bg-red-900 text-red-300'\n            elif user.role == 'staff':\n                role_class = 'bg-blue-900 text-blue-300'\n            else:\n                role_class = 'bg-gray-900 text-gray-300'\n\n            created_time = user.created_at.strftime('%Y-%m-%d') if user.created_at else '未知'\n\n            # 使用财务计算系统获取准确的财务信息\n            if user.role in ['admin', 'superadmin']:\n                # 管理员显示无限权限\n                finance_info = f'''\n                <div class=\"bg-gray-800 p-3 rounded text-xs\">\n                    <div class=\"mb-2\"><span class=\"px-2 py-1 rounded bg-red-900 text-red-300 font-bold\">管理员</span></div>\n                    <div class=\"grid grid-cols-2 gap-1\">\n                        <div>余额: <span class=\"text-green-400 font-bold\">∞</span></div>\n                        <div>积分: <span class=\"text-blue-400 font-bold\">∞</span></div>\n                        <div>基础: <span class=\"text-yellow-400 font-bold\">∞</span></div>\n                        <div>高级: <span class=\"text-purple-400 font-bold\">∞</span></div>\n                    </div>\n                </div>\n                '''\n            else:\n                # 普通用户使用财务计算系统\n                finance_summary = FinanceCalculator.get_user_finance_summary(user.id)\n                if finance_summary:\n                    vip_level = finance_summary['vip_level']\n                    vip_text = f\"VIP{vip_level}\" if vip_level > 0 else \"免费\"\n                    vip_class = \"bg-yellow-900 text-yellow-300\" if vip_level > 0 else \"bg-gray-700 text-gray-300\"\n\n                    # VIP状态指示器（专门用于检查购买记录是否正常）\n                    vip_status = get_vip_status_indicator(user.id) if vip_level > 0 else {'status': 'gray', 'message': '非VIP'}\n                    if vip_level > 0:\n                        status_light = \"🟢\" if vip_status['status'] == 'green' else \"🟡\" if vip_status['status'] == 'yellow' else \"🔴\"\n                    else:\n                        status_light = \"\"  # 非VIP用户不显示状态灯\n\n                    # VIP到期时间\n                    vip_expire = \"\"\n                    if finance_summary['vip_expire_at']:\n                        expire_date = finance_summary['vip_expire_at'].strftime('%m-%d')\n                        vip_expire = f\"<br><small class='text-gray-400'>到期:{expire_date}</small>\"\n\n                    # 财务数据\n                    balance = finance_summary['balance_yuan']\n                    points = finance_summary['points_balance']\n                    quota = finance_summary['quota_info']\n\n                    # 数据一致性状态（保留原有的）\n                    integrity = finance_summary['data_integrity']\n                    integrity_icon = \"🔴\" if integrity['status'] == 'inconsistent' else \"🟢\"\n\n                    # 构建状态指示器说明\n                    status_indicators = \"\"\n                    if vip_level > 0:\n                        # 只显示VIP购买记录状态指示器，移除重复的指示器\n                        status_indicators = f'<span class=\"ml-1\" title=\"VIP购买记录状态：{vip_status[\"message\"]}\">{status_light}</span>'\n\n                    finance_info = f'''\n                    <div class=\"bg-gray-800 p-3 rounded text-xs\">\n                        <div class=\"mb-2\">\n                            <span class=\"px-2 py-1 rounded {vip_class} font-bold\">{vip_text}</span>\n                            {status_indicators}\n                            {vip_expire}\n                        </div>\n                        <div class=\"grid grid-cols-2 gap-1\">\n                            <div>余额: <span class=\"text-green-400 font-bold\">¥{balance:.2f}</span></div>\n                            <div>积分: <span class=\"text-blue-400 font-bold\">{points}</span></div>\n                            <div>基础: <span class=\"text-yellow-400 font-bold\">{quota['basic_used']}/{'∞' if quota['basic_limit'] >= 999999 else quota['basic_limit']}</span></div>\n                            <div>高级: <span class=\"text-purple-400 font-bold\">{quota['premium_used']}/{'∞' if quota['premium_limit'] >= 999999 else quota['premium_limit']}</span></div>\n                        </div>\n                        {f'<div class=\"mt-1 text-xs text-gray-400\">🚦 {vip_status[\"message\"]}</div>' if vip_status['status'] != 'green' and vip_level > 0 else ''}\n                    </div>\n                    '''\n                else:\n                    finance_info = '<div class=\"text-xs text-red-500 bg-gray-800 p-2 rounded\">❌ 财务数据错误</div>'\n\n            user_row = f'''\n            <tr class=\"border-b border-gray-800 hover:bg-gray-800\">\n                <td class=\"py-3 px-2 text-center font-mono\">{user.id}</td>\n                <td class=\"py-3 px-3\">\n                    <div class=\"font-medium text-white\">{user.username}</div>\n                    <div class=\"text-xs text-gray-400\">{user.email or \"未设置邮箱\"}</div>\n                </td>\n                <td class=\"py-3 px-2 text-center\">\n                    <span class=\"px-2 py-1 rounded text-xs {role_class} font-bold\">\n                        {role_text}\n                    </span>\n                </td>\n                <td class=\"py-3 px-3\">{finance_info}</td>\n                <td class=\"py-3 px-2 text-center text-xs text-gray-400\">{created_time}</td>\n                <td class=\"py-3 px-2 text-center\">\n                    <a href=\"/admin/users/{user.id}/finance\"\n                       class=\"px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 font-bold\">\n                        详情\n                    </a>\n                </td>\n            </tr>\n            '''\n            user_rows.append(user_row)\n\n        # 获取VIP状态汇总\n        vip_summary = get_vip_status_summary()\n\n        # 分页链接\n        prev_link = f'<a href=\"?page={pagination.prev_num}&search={search}\" class=\"px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700\">上一页</a>' if pagination.has_prev else '<span class=\"px-3 py-1 bg-gray-800 text-gray-500 rounded\">上一页</span>'\n        next_link = f'<a href=\"?page={pagination.next_num}&search={search}\" class=\"px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700\">下一页</a>' if pagination.has_next else '<span class=\"px-3 py-1 bg-gray-800 text-gray-500 rounded\">下一页</span>'\n\n        # 返回简化的HTML页面\n        return f'''\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>用户管理 - 塔罗解读</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        body {{ background-color: #121212; color: #e0e0e0; }}\n        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"container mx-auto px-4 py-8\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h1 class=\"text-3xl font-bold text-yellow-400\">👥 用户管理</h1>\n            <a href=\"/admin/\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                ← 返回管理后台\n            </a>\n        </div>\n\n        <!-- VIP状态汇总面板 -->\n        <div class=\"card rounded-lg p-4 mb-6\">\n            <h3 class=\"text-lg font-semibold text-yellow-400 mb-3\">🚦 VIP购买记录状态监控</h3>\n            <div class=\"flex space-x-6 mb-3\">\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🟢</span>\n                    <span>正常: {vip_summary['green']}</span>\n                </div>\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🟡</span>\n                    <span>警告: {vip_summary['yellow']}</span>\n                </div>\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🔴</span>\n                    <span>异常: {vip_summary['red']}</span>\n                </div>\n                <div class=\"ml-auto\">\n                    <span class=\"text-sm text-gray-400\">VIP健康率: {vip_summary['health_rate']:.1f}%</span>\n                </div>\n            </div>\n            <div class=\"text-xs text-gray-400\">\n                <strong>红绿灯说明：</strong>\n                🟢 VIP用户且有对应购买记录 |\n                🟡 VIP用户但数据不完整 |\n                🔴 VIP用户但完全没有购买记录（需要处理）\n            </div>\n        </div>\n\n        <div class=\"card rounded-lg p-6\">\n            <div class=\"mb-4\">\n                <form method=\"GET\" class=\"flex gap-4\">\n                    <input type=\"text\" name=\"search\" value=\"{search}\"\n                           placeholder=\"搜索用户名...\"\n                           class=\"px-3 py-2 bg-gray-700 text-white rounded border border-gray-600\">\n                    <button type=\"submit\" class=\"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700\">\n                        搜索\n                    </button>\n                </form>\n            </div>\n\n            <div class=\"overflow-x-auto\">\n                <table class=\"w-full table-fixed\">\n                    <thead>\n                        <tr class=\"border-b border-gray-700 bg-gray-900\">\n                            <th class=\"w-16 text-center py-3 px-2 text-yellow-400 font-bold\">ID</th>\n                            <th class=\"w-48 text-left py-3 px-3 text-yellow-400 font-bold\">用户信息</th>\n                            <th class=\"w-20 text-center py-3 px-2 text-yellow-400 font-bold\">角色</th>\n                            <th class=\"w-64 text-left py-3 px-3 text-yellow-400 font-bold\">💰 财务状况</th>\n                            <th class=\"w-24 text-center py-3 px-2 text-yellow-400 font-bold\">注册</th>\n                            <th class=\"w-20 text-center py-3 px-2 text-yellow-400 font-bold\">操作</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        {\"\".join(user_rows)}\n                    </tbody>\n                </table>\n            </div>\n\n            <div class=\"mt-6 flex justify-between items-center\">\n                <div class=\"text-gray-400\">\n                    共 {pagination.total} 个用户，第 {pagination.page} / {pagination.pages} 页\n                </div>\n                <div class=\"flex gap-2\">\n                    {prev_link}\n                    {next_link}\n                </div>\n            </div>\n        </div>\n    </div>\n</body>\n</html>\n        '''\n\n    except Exception as e:\n        logger.error(f\"加载用户管理页面失败: {str(e)}\")\n        import traceback\n        return f'''\n<!DOCTYPE html>\n<html>\n<head><title>用户管理错误</title></head>\n<body style=\"background: #121212; color: white; padding: 20px;\">\n    <h1>用户管理页面错误</h1>\n    <p>错误信息: {str(e)}</p>\n    <pre>{traceback.format_exc()}</pre>\n    <a href=\"/admin/\" style=\"color: #60a5fa;\">返回管理后台</a>\n</body>\n</html>\n        '''\n\n@admin_bp.route('/users/<int:user_id>/finance')\n@login_required\n@staff_required\ndef user_finance_detail(user_id):\n    \"\"\"用户财务详情页面 - 显示所有原始单据和余额计算过程\"\"\"\n    try:\n        from sqlalchemy.orm import joinedload\n        from models import Purchase, Order, UserFinance, UserConsumptionRecord, Article\n        from collections import defaultdict\n        from datetime import datetime\n\n        # 获取用户信息\n        user = User.query.options(joinedload(User.finance)).get_or_404(user_id)\n\n        # 获取所有购买记录（原始单据）- 只查询存在的字段\n        purchases = db.session.query(Purchase.id, Purchase.user_id, Purchase.article_id,\n                                    Purchase.amount, Purchase.content_type, Purchase.purchase_date,\n                                    Purchase.payment_method, Purchase.transaction_id)\\\n                             .filter_by(user_id=user_id)\\\n                             .order_by(Purchase.purchase_date.desc()).all()\n\n        # 获取所有订单记录\n        orders = Order.query.filter_by(user_id=user_id)\\\n                           .order_by(Order.created_at.desc()).all()\n\n        # 获取所有消费记录\n        consumption_records = UserConsumptionRecord.query.filter_by(user_id=user_id)\\\n                                                         .order_by(UserConsumptionRecord.created_at.desc()).all()\n\n        # 构建详细的财务流水记录\n        finance_transactions = []\n\n        # 添加购买记录（收入）\n        for purchase in purchases:\n            article = Article.query.get(purchase.article_id) if purchase.article_id else None\n            if purchase.article_id:\n                description = f'购买文章: {article.title if article else \"已删除文章\"}'\n            else:\n                # VIP购买或其他类型\n                if purchase.amount == 12800:\n                    description = '购买VIP1个月'\n                elif purchase.amount == 140800:\n                    description = '购买VIP1年'\n                else:\n                    description = f'购买VIP ¥{purchase.amount/100:.2f}'\n\n            finance_transactions.append({\n                'type': 'purchase',\n                'date': purchase.purchase_date,\n                'description': description,\n                'content_type': purchase.content_type,\n                'payment_method': purchase.payment_method,\n                'amount': purchase.amount,\n                'transaction_id': purchase.transaction_id,\n                'status': 'completed'\n            })\n\n        # 添加消费记录（支出）\n        for record in consumption_records:\n            article = Article.query.get(record.article_id) if record.article_id else None\n            finance_transactions.append({\n                'type': 'consumption',\n                'date': record.created_at,\n                'description': f'消费: {article.title if article else \"系统消费\"}',\n                'content_type': record.content_type,\n                'cost_type': record.cost_type,\n                'amount': -record.cost_amount,  # 负数表示支出\n                'status': 'completed'\n            })\n\n        # 按时间排序\n        finance_transactions.sort(key=lambda x: x['date'] if x['date'] else datetime.min, reverse=True)\n\n        # 计算余额变化过程\n        running_balance = 0\n        if user.finance:\n            running_balance = user.finance.balance or 0\n\n        # 从最新记录开始，反向计算每笔交易后的余额\n        for i, transaction in enumerate(finance_transactions):\n            transaction['balance_after'] = running_balance\n            running_balance -= transaction['amount']\n            transaction['balance_before'] = running_balance\n\n        # 反转列表，使其按时间正序显示\n        finance_transactions.reverse()\n\n        # 计算统计数据\n        finance_stats = {\n            'total_purchases': len(purchases),\n            'total_spent': sum(p.amount for p in purchases),\n            'basic_purchases': len([p for p in purchases if p.content_type == 'basic']),\n            'premium_purchases': len([p for p in purchases if p.content_type == 'premium']),\n            'cash_payments': len([p for p in purchases if p.payment_method in ['alipay', 'wechat', 'mock']]),\n            'quota_payments': len([p for p in purchases if p.payment_method == 'quota']),\n            'points_payments': len([p for p in purchases if p.payment_method == 'points']),\n            'total_consumption': sum(r.cost_amount for r in consumption_records),\n            'current_balance': user.finance.balance if user.finance else 0\n        }\n\n        # 按月统计\n        monthly_stats = defaultdict(lambda: {'purchases': 0, 'consumption': 0, 'net': 0})\n\n        for transaction in finance_transactions:\n            if transaction['date']:\n                month_key = transaction['date'].strftime('%Y-%m')\n                if transaction['type'] == 'purchase':\n                    monthly_stats[month_key]['purchases'] += transaction['amount']\n                else:\n                    monthly_stats[month_key]['consumption'] += abs(transaction['amount'])\n                monthly_stats[month_key]['net'] = monthly_stats[month_key]['purchases'] - monthly_stats[month_key]['consumption']\n\n        # 转换为列表\n        monthly_data = []\n        for month, stats in sorted(monthly_stats.items(), reverse=True):\n            monthly_data.append({\n                'month': month,\n                'purchases': stats['purchases'],\n                'consumption': stats['consumption'],\n                'net': stats['net']\n            })\n\n        # 构建财务交易记录HTML\n        transaction_rows = []\n        for transaction in finance_transactions:\n            transaction_date = transaction['date'].strftime('%Y-%m-%d %H:%M') if transaction['date'] else '未知'\n            amount_class = 'text-green-400' if transaction['amount'] > 0 else 'text-red-400'\n            # 正确转换金额：分 -> 元\n            amount_yuan = transaction['amount'] / 100.0\n            amount_text = f\"+¥{amount_yuan:.2f}\" if amount_yuan > 0 else f\"-¥{abs(amount_yuan):.2f}\"\n\n            transaction_row = f'''\n            <tr class=\"border-b border-gray-800 hover:bg-gray-800\">\n                <td class=\"py-3 px-4\">{transaction_date}</td>\n                <td class=\"py-3 px-4\">{transaction['type']}</td>\n                <td class=\"py-3 px-4\">{transaction['description']}</td>\n                <td class=\"py-3 px-4\">{transaction.get('content_type', '-')}</td>\n                <td class=\"py-3 px-4\">{transaction.get('payment_method', transaction.get('cost_type', '-'))}</td>\n                <td class=\"py-3 px-4 {amount_class}\">{amount_text}</td>\n                <td class=\"py-3 px-4\">¥{transaction.get('balance_before', 0)/100:.2f}</td>\n                <td class=\"py-3 px-4\">¥{transaction.get('balance_after', 0)/100:.2f}</td>\n                <td class=\"py-3 px-4\">{transaction.get('transaction_id', '-')}</td>\n            </tr>\n            '''\n            transaction_rows.append(transaction_row)\n\n        # 构建月度统计HTML\n        monthly_rows = []\n        for month_data in monthly_data:\n            net_class = 'text-green-400' if month_data['net'] >= 0 else 'text-red-400'\n            monthly_row = f'''\n            <tr class=\"border-b border-gray-800\">\n                <td class=\"py-2 px-4\">{month_data['month']}</td>\n                <td class=\"py-2 px-4 text-green-400\">¥{month_data['purchases']/100:.2f}</td>\n                <td class=\"py-2 px-4 text-red-400\">¥{month_data['consumption']/100:.2f}</td>\n                <td class=\"py-2 px-4 {net_class}\">¥{month_data['net']/100:.2f}</td>\n            </tr>\n            '''\n            monthly_rows.append(monthly_row)\n\n        # 构建UserFinance信息HTML\n        finance_info_html = \"\"\n        if user.finance:\n            vip_level = user.finance.vip_level or 0\n            vip_expire = user.finance.vip_expire_at or \"N/A\"\n            balance = (user.finance.balance or 0) / 100\n            total_spent = (user.finance.total_spent or 0) / 100\n            points_balance = user.finance.points_balance or 0\n            basic_quota_used = user.finance.basic_quota_used or 0\n            premium_quota_used = user.finance.premium_quota_used or 0\n            quota_reset_date = user.finance.quota_reset_date or \"N/A\"\n\n            finance_info_html = f'''\n                <p><strong>VIP等级:</strong> {vip_level}</p>\n                <p><strong>VIP到期时间:</strong> {vip_expire}</p>\n                <p><strong>余额:</strong> ¥{balance:.2f}</p>\n                <p><strong>总消费金额:</strong> ¥{total_spent:.2f}</p>\n                <p><strong>积分余额:</strong> {points_balance}</p>\n                <p><strong>基础配额已用:</strong> {basic_quota_used}</p>\n                <p><strong>高级配额已用:</strong> {premium_quota_used}</p>\n                <p><strong>配额重置日期:</strong> {quota_reset_date}</p>\n            '''\n\n            # 添加状态分析\n            if vip_level > 0 and total_spent > 0:\n                finance_info_html += f'''\n                    <div class=\"mt-2 p-2 bg-green-800 rounded\">\n                        <p class=\"text-green-300\">✅ <strong>发现购买痕迹！</strong></p>\n                        <p class=\"text-green-300\">用户是VIP且total_spent > 0，说明确实有消费记录</p>\n                        <p class=\"text-green-300\">应该为这¥{total_spent:.2f}的消费创建Purchase记录</p>\n                    </div>\n                '''\n            elif vip_level > 0 and total_spent == 0:\n                finance_info_html += '''\n                    <div class=\"mt-2 p-2 bg-red-800 rounded\">\n                        <p class=\"text-red-300\">🚨 <strong>异常：VIP但total_spent=0</strong></p>\n                        <p class=\"text-red-300\">可能是管理员手动设置的VIP或数据异常</p>\n                    </div>\n                '''\n        else:\n            finance_info_html = '<p class=\"text-red-300\">❌ 该用户没有UserFinance记录</p>'\n\n        # 获取Order数量\n        order_count = Order.query.filter_by(user_id=user.id).count()\n\n        # 返回简化的HTML页面\n        return f'''\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>用户财务详情 - {user.username}</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        body {{ background-color: #121212; color: #e0e0e0; }}\n        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"container mx-auto px-4 py-8\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h1 class=\"text-3xl font-bold text-yellow-400\">💰 {user.username} 的财务详情</h1>\n            <a href=\"/admin/users\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                ← 返回用户管理\n            </a>\n        </div>\n\n        <!-- UserFinance详细信息 -->\n        <div class=\"card p-4 rounded-lg mb-6 bg-blue-900 border border-blue-700\">\n            <h3 class=\"text-lg font-semibold text-blue-400 mb-2\">🔍 UserFinance表详细信息</h3>\n            <div class=\"text-sm\">\n                {finance_info_html}\n                <hr class=\"my-2 border-blue-600\">\n                <p><strong>Purchase记录数:</strong> {len(purchases)}</p>\n                <p><strong>Order记录数:</strong> {order_count}</p>\n            </div>\n        </div>\n\n\n\n        <!-- 财务统计概览 -->\n        <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n            <div class=\"card p-4 rounded-lg\">\n                <h3 class=\"text-lg font-semibold text-yellow-400 mb-2\">总购买次数</h3>\n                <p class=\"text-2xl font-bold\">{finance_stats['total_purchases']}</p>\n            </div>\n            <div class=\"card p-4 rounded-lg\">\n                <h3 class=\"text-lg font-semibold text-green-400 mb-2\">总购买金额</h3>\n                <p class=\"text-2xl font-bold text-green-400\">¥{finance_stats['total_spent']/100:.2f}</p>\n            </div>\n            <div class=\"card p-4 rounded-lg\">\n                <h3 class=\"text-lg font-semibold text-red-400 mb-2\">总消费金额</h3>\n                <p class=\"text-2xl font-bold text-red-400\">¥{finance_stats['total_consumption']/100:.2f}</p>\n            </div>\n            <div class=\"card p-4 rounded-lg\">\n                <h3 class=\"text-lg font-semibold text-blue-400 mb-2\">当前余额</h3>\n                <p class=\"text-2xl font-bold text-blue-400\">¥{finance_stats['current_balance']/100:.2f}</p>\n            </div>\n        </div>\n\n        <!-- 支付方式统计 -->\n        <div class=\"card p-6 rounded-lg mb-6\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">支付方式统计</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">现金支付</p>\n                    <p class=\"text-2xl font-bold text-green-400\">{finance_stats['cash_payments']}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">配额支付</p>\n                    <p class=\"text-2xl font-bold text-blue-400\">{finance_stats['quota_payments']}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">积分支付</p>\n                    <p class=\"text-2xl font-bold text-purple-400\">{finance_stats['points_payments']}</p>\n                </div>\n            </div>\n        </div>\n\n        <!-- 月度统计 -->\n        <div class=\"card p-6 rounded-lg mb-6\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">月度财务统计</h2>\n            <div class=\"overflow-x-auto\">\n                <table class=\"w-full\">\n                    <thead>\n                        <tr class=\"border-b border-gray-700\">\n                            <th class=\"text-left py-2 px-4\">月份</th>\n                            <th class=\"text-left py-2 px-4\">购买金额</th>\n                            <th class=\"text-left py-2 px-4\">消费金额</th>\n                            <th class=\"text-left py-2 px-4\">净收支</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        {\"\".join(monthly_rows)}\n                    </tbody>\n                </table>\n            </div>\n        </div>\n\n        <!-- 详细财务流水 -->\n        <div class=\"card p-6 rounded-lg\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">详细财务流水（原始单据）</h2>\n            <div class=\"overflow-x-auto\">\n                <table class=\"w-full text-sm\">\n                    <thead>\n                        <tr class=\"border-b border-gray-700\">\n                            <th class=\"text-left py-2 px-4\">时间</th>\n                            <th class=\"text-left py-2 px-4\">类型</th>\n                            <th class=\"text-left py-2 px-4\">描述</th>\n                            <th class=\"text-left py-2 px-4\">内容类型</th>\n                            <th class=\"text-left py-2 px-4\">支付方式</th>\n                            <th class=\"text-left py-2 px-4\">金额</th>\n                            <th class=\"text-left py-2 px-4\">交易前余额</th>\n                            <th class=\"text-left py-2 px-4\">交易后余额</th>\n                            <th class=\"text-left py-2 px-4\">交易ID</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        {\"\".join(transaction_rows)}\n                    </tbody>\n                </table>\n            </div>\n        </div>\n    </div>\n</body>\n</html>\n        '''\n\n    except Exception as e:\n        logger.error(f\"加载用户财务详情失败: {str(e)}\")\n        import traceback\n        return f'''\n<!DOCTYPE html>\n<html>\n<head><title>财务详情错误</title></head>\n<body style=\"background: #121212; color: white; padding: 20px;\">\n    <h1>财务详情页面错误</h1>\n    <p>用户ID: {user_id}</p>\n    <p>错误信息: {str(e)}</p>\n    <pre>{traceback.format_exc()}</pre>\n    <a href=\"/admin/users\" style=\"color: #60a5fa;\">返回用户管理</a>\n</body>\n</html>\n        '''\n\n@admin_bp.route('/diagnostic')\ndef link_diagnostic():\n    \"\"\"链接跳转诊断页面\"\"\"\n    return render_template('admin/link_diagnostic.html')\n\n@admin_bp.route('/users/<int:user_id>/fix-finance', methods=['POST'])\n@login_required\n@staff_required\ndef fix_user_finance(user_id):\n    \"\"\"修复用户财务数据\"\"\"\n    try:\n        from services.finance_calculator import AdminFinanceTools\n        success, message = AdminFinanceTools.fix_user_finance_data(user_id)\n\n        if success:\n            return f'''\n            <div style=\"background: #121212; color: white; padding: 20px; font-family: Arial;\">\n                <h2 style=\"color: #4ade80;\">✅ 财务数据修复成功</h2>\n                <p>用户ID: {user_id}</p>\n                <p>修复结果: {message}</p>\n                <a href=\"/admin/users\" style=\"color: #60a5fa; text-decoration: none;\">← 返回用户管理</a>\n            </div>\n            '''\n        else:\n            return f'''\n            <div style=\"background: #121212; color: white; padding: 20px; font-family: Arial;\">\n                <h2 style=\"color: #ef4444;\">❌ 财务数据修复失败</h2>\n                <p>用户ID: {user_id}</p>\n                <p>错误信息: {message}</p>\n                <a href=\"/admin/users\" style=\"color: #60a5fa; text-decoration: none;\">← 返回用户管理</a>\n            </div>\n            '''\n    except Exception as e:\n        return f'''\n        <div style=\"background: #121212; color: white; padding: 20px; font-family: Arial;\">\n            <h2 style=\"color: #ef4444;\">❌ 修复过程出错</h2>\n            <p>用户ID: {user_id}</p>\n            <p>错误信息: {str(e)}</p>\n            <a href=\"/admin/users\" style=\"color: #60a5fa; text-decoration: none;\">← 返回用户管理</a>\n        </div>\n        '''\n\n@admin_bp.route('/data-repair')\n@login_required\n@admin_required\ndef data_repair_dashboard():\n    \"\"\"数据修复控制台\"\"\"\n    try:\n        from services.data_repair import DataValidator\n\n        # 获取数据验证结果\n        validation_result = DataValidator.validate_all_users()\n\n        return f'''\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>数据修复控制台</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        body {{ background-color: #121212; color: #e0e0e0; }}\n        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"container mx-auto px-4 py-8\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h1 class=\"text-3xl font-bold text-red-400\">🔧 数据修复控制台</h1>\n            <a href=\"/admin/\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                ← 返回管理后台\n            </a>\n        </div>\n\n        <!-- 数据验证概览 -->\n        <div class=\"card p-6 rounded-lg mb-6\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">📊 数据完整性概览</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">总用户数</p>\n                    <p class=\"text-3xl font-bold text-blue-400\">{validation_result['total_users'] if validation_result else 0}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">数据正常</p>\n                    <p class=\"text-3xl font-bold text-green-400\">{validation_result['valid_users'] if validation_result else 0}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">数据异常</p>\n                    <p class=\"text-3xl font-bold text-red-400\">{validation_result['invalid_users'] if validation_result else 0}</p>\n                </div>\n                <div class=\"text-center\">\n                    <p class=\"text-lg font-semibold\">完整性率</p>\n                    <p class=\"text-3xl font-bold text-purple-400\">{validation_result['validation_rate']*100:.1f}%</p>\n                </div>\n            </div>\n        </div>\n\n        <!-- 修复操作 -->\n        <div class=\"card p-6 rounded-lg mb-6\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">🛠️ 数据修复操作</h2>\n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <button onclick=\"runRepair('purchase_fields')\"\n                        class=\"px-4 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 font-bold\">\n                    修复Purchase字段缺失\n                </button>\n                <button onclick=\"runRepair('consumption_records')\"\n                        class=\"px-4 py-3 bg-green-600 text-white rounded hover:bg-green-700 font-bold\">\n                    补充消费记录\n                </button>\n                <button onclick=\"runRepair('quota_records')\"\n                        class=\"px-4 py-3 bg-purple-600 text-white rounded hover:bg-purple-700 font-bold\">\n                    创建配额使用记录\n                </button>\n                <button onclick=\"runRepair('points_records')\"\n                        class=\"px-4 py-3 bg-yellow-600 text-white rounded hover:bg-yellow-700 font-bold\">\n                    修复积分记录\n                </button>\n            </div>\n            <div class=\"mt-4\">\n                <button onclick=\"runRepair('full_repair')\"\n                        class=\"w-full px-4 py-3 bg-red-600 text-white rounded hover:bg-red-700 font-bold\">\n                    🚨 运行完整修复（谨慎操作）\n                </button>\n            </div>\n        </div>\n\n        <!-- 修复结果 -->\n        <div id=\"repair-result\" class=\"card p-6 rounded-lg\" style=\"display: none;\">\n            <h2 class=\"text-xl font-semibold text-yellow-400 mb-4\">修复结果</h2>\n            <div id=\"repair-content\" class=\"text-sm\"></div>\n        </div>\n    </div>\n\n    <script>\n        function runRepair(repairType) {{\n            const resultDiv = document.getElementById('repair-result');\n            const contentDiv = document.getElementById('repair-content');\n\n            resultDiv.style.display = 'block';\n            contentDiv.innerHTML = '<p class=\"text-blue-400\">正在执行修复操作，请稍候...</p>';\n\n            fetch('/admin/data-repair/run', {{\n                method: 'POST',\n                headers: {{\n                    'Content-Type': 'application/json',\n                }},\n                body: JSON.stringify({{repair_type: repairType}})\n            }})\n            .then(response => response.json())\n            .then(data => {{\n                if (data.success) {{\n                    contentDiv.innerHTML = '<div class=\"text-green-400\"><h3>✅ 修复成功</h3><pre>' +\n                                         JSON.stringify(data.results, null, 2) + '</pre></div>';\n                }} else {{\n                    contentDiv.innerHTML = '<div class=\"text-red-400\"><h3>❌ 修复失败</h3><p>' +\n                                         data.error + '</p></div>';\n                }}\n            }})\n            .catch(error => {{\n                contentDiv.innerHTML = '<div class=\"text-red-400\"><h3>❌ 请求失败</h3><p>' +\n                                     error.message + '</p></div>';\n            }});\n        }}\n    </script>\n</body>\n</html>\n        '''\n\n    except Exception as e:\n        return f'''\n        <div style=\"background: #121212; color: white; padding: 20px;\">\n            <h1>数据修复控制台错误</h1>\n            <p>错误信息: {str(e)}</p>\n            <a href=\"/admin/\" style=\"color: #60a5fa;\">返回管理后台</a>\n        </div>\n        '''\n\n@admin_bp.route('/data-repair/run', methods=['POST'])\n@login_required\n@admin_required\ndef run_data_repair():\n    \"\"\"执行数据修复操作\"\"\"\n    try:\n        from services.data_repair import DataRepairTool\n\n        data = request.get_json()\n        repair_type = data.get('repair_type')\n\n        if repair_type == 'purchase_fields':\n            success, message = DataRepairTool.repair_purchase_missing_fields()\n            return jsonify({'success': success, 'results': [message]})\n\n        elif repair_type == 'consumption_records':\n            success, message = DataRepairTool.repair_missing_consumption_records()\n            return jsonify({'success': success, 'results': [message]})\n\n        elif repair_type == 'quota_records':\n            success, message = DataRepairTool.create_missing_quota_records()\n            return jsonify({'success': success, 'results': [message]})\n\n        elif repair_type == 'points_records':\n            success, message = DataRepairTool.repair_points_records()\n            return jsonify({'success': success, 'results': [message]})\n\n        elif repair_type == 'full_repair':\n            results = DataRepairTool.run_full_repair()\n            return jsonify({'success': True, 'results': results})\n\n        else:\n            return jsonify({'success': False, 'error': '未知的修复类型'})\n\n    except Exception as e:\n        logger.error(f\"执行数据修复失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)})\n\n\n\n@admin_bp.route('/vip-status')\n@login_required\n@staff_required\ndef vip_status_page():\n    \"\"\"VIP状态监控页面\"\"\"\n    try:\n        # 获取VIP状态指示器\n        vip_status_data = get_vip_status_summary()\n        all_vip_status = []\n\n        # 获取所有VIP用户\n        vip_user_ids = db.session.query(UserFinance.user_id).filter(UserFinance.vip_level > 0).all()\n        vip_user_ids = [uid[0] for uid in vip_user_ids]\n\n        for user_id in vip_user_ids:\n            user = User.query.get(user_id)\n            if user:\n                status = get_vip_status_indicator(user_id)\n                all_vip_status.append({\n                    'user': user,\n                    'status': status\n                })\n\n        # 生成HTML\n        user_rows = []\n        for item in all_vip_status:\n            user = item['user']\n            status = item['status']\n\n            # 状态指示灯\n            if status['status'] == 'green':\n                light = '🟢'\n                status_class = 'text-green-400'\n            elif status['status'] == 'yellow':\n                light = '🟡'\n                status_class = 'text-yellow-400'\n            else:\n                light = '🔴'\n                status_class = 'text-red-400'\n\n            # 获取财务信息\n            finance = UserFinance.query.filter_by(user_id=user.id).first()\n            if finance:\n                vip_info = f\"VIP{finance.vip_level}\"\n                balance = finance.balance / 100 if finance.balance else 0\n                total_spent = finance.total_spent / 100 if finance.total_spent else 0\n                finance_text = f\"余额: ¥{balance:.2f} | 消费: ¥{total_spent:.2f}\"\n            else:\n                vip_info = \"无财务记录\"\n                finance_text = \"N/A\"\n\n            user_row = f'''\n            <tr class=\"border-b border-gray-700 hover:bg-gray-800\">\n                <td class=\"py-3 px-4 text-center\">{light}</td>\n                <td class=\"py-3 px-4\">\n                    <div class=\"font-medium\">{user.username}</div>\n                    <div class=\"text-xs text-gray-400\">{user.email or \"无邮箱\"}</div>\n                </td>\n                <td class=\"py-3 px-4 text-center\">\n                    <span class=\"px-2 py-1 bg-yellow-800 text-yellow-300 rounded text-xs\">{vip_info}</span>\n                </td>\n                <td class=\"py-3 px-4 text-xs\">{finance_text}</td>\n                <td class=\"py-3 px-4 {status_class} text-sm\">{status['message']}</td>\n                <td class=\"py-3 px-4 text-xs text-gray-400\">{status.get('details', '')}</td>\n            </tr>\n            '''\n            user_rows.append(user_row)\n\n        return f'''\n<!DOCTYPE html>\n<html lang=\"zh\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>VIP状态监控</title>\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <style>\n        body {{ background-color: #121212; color: #e0e0e0; }}\n        .card {{ background-color: #1e1e1e; border: 1px solid #2a2a2a; }}\n    </style>\n</head>\n<body class=\"min-h-screen\">\n    <div class=\"container mx-auto px-4 py-8\">\n        <div class=\"flex justify-between items-center mb-6\">\n            <h1 class=\"text-3xl font-bold text-yellow-400\">🚦 VIP状态监控</h1>\n            <a href=\"/admin/\" class=\"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700\">\n                ← 返回管理后台\n            </a>\n        </div>\n\n        <!-- VIP状态汇总 -->\n        <div class=\"card rounded-lg p-4 mb-6\">\n            <h3 class=\"text-lg font-semibold text-yellow-400 mb-3\">📊 VIP状态概览</h3>\n            <div class=\"flex space-x-6\">\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🟢</span>\n                    <span>正常: {vip_status_data.get('green', 0)}</span>\n                </div>\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🟡</span>\n                    <span>警告: {vip_status_data.get('yellow', 0)}</span>\n                </div>\n                <div class=\"flex items-center\">\n                    <span class=\"text-lg mr-2\">🔴</span>\n                    <span>异常: {vip_status_data.get('red', 0)}</span>\n                </div>\n                <div class=\"ml-auto\">\n                    <span class=\"text-sm text-gray-400\">健康率: {vip_status_data.get('health_rate', 0):.1f}%</span>\n                </div>\n            </div>\n            <div class=\"mt-2 text-sm text-gray-400\">\n                说明: 🟢正常 🟡需注意 🔴需处理\n            </div>\n        </div>\n\n        <!-- VIP用户详情 -->\n        <div class=\"card rounded-lg p-6\">\n            <h3 class=\"text-lg font-semibold text-yellow-400 mb-4\">VIP用户详情</h3>\n            <div class=\"overflow-x-auto\">\n                <table class=\"w-full\">\n                    <thead>\n                        <tr class=\"border-b border-gray-700 bg-gray-900\">\n                            <th class=\"text-center py-3 px-4 text-yellow-400\">状态</th>\n                            <th class=\"text-left py-3 px-4 text-yellow-400\">用户</th>\n                            <th class=\"text-center py-3 px-4 text-yellow-400\">VIP等级</th>\n                            <th class=\"text-left py-3 px-4 text-yellow-400\">财务信息</th>\n                            <th class=\"text-left py-3 px-4 text-yellow-400\">状态说明</th>\n                            <th class=\"text-left py-3 px-4 text-yellow-400\">详情</th>\n                        </tr>\n                    </thead>\n                    <tbody>\n                        {\"\".join(user_rows)}\n                    </tbody>\n                </table>\n            </div>\n\n            <div class=\"mt-4 text-center text-gray-400\">\n                总VIP用户: {len(all_vip_status)} | 刷新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n            </div>\n        </div>\n    </div>\n</body>\n</html>\n        '''\n\n    except Exception as e:\n        return f'''\n        <div style=\"background: #121212; color: white; padding: 20px;\">\n            <h1>VIP状态监控错误</h1>\n            <p>错误信息: {str(e)}</p>\n            <a href=\"/admin/\" style=\"color: #60a5fa;\">返回管理后台</a>\n        </div>\n        '''\n\n@admin_bp.route('/test-clean')\n@login_required\n@staff_required\ndef test_clean_page():\n    \"\"\"完全干净的测试页面\"\"\"\n    return '''\n<!DOCTYPE html>\n<html>\n<head>\n    <title>干净测试页面</title>\n    <style>\n        body { font-family: Arial, sans-serif; padding: 20px; background: #1a1a1a; color: white; }\n        .test-link {\n            display: inline-block;\n            padding: 15px 30px;\n            margin: 10px;\n            background: #4CAF50;\n            color: white;\n            text-decoration: none;\n            border-radius: 5px;\n            font-size: 16px;\n        }\n        .test-link:hover { background: #45a049; }\n        .log { background: #333; padding: 10px; margin: 10px 0; border-radius: 5px; }\n    </style>\n</head>\n<body>\n    <h1>🧪 干净的链接测试页面</h1>\n    <p>这个页面没有任何复杂的JavaScript或CSS，用于测试基本的链接跳转。</p>\n\n    <div>\n        <h2>测试链接：</h2>\n        <a href=\"/admin/users\" class=\"test-link\">👥 用户管理 (普通链接)</a>\n        <a href=\"/admin/\" class=\"test-link\">🏠 管理后台首页</a>\n        <a href=\"/admin/articles\" class=\"test-link\">📄 文章管理</a>\n    </div>\n\n    <div>\n        <h2>强制跳转测试：</h2>\n        <button class=\"test-link\" onclick=\"window.location.href='/admin/users'\">🚀 强制跳转到用户管理</button>\n        <button class=\"test-link\" onclick=\"window.open('/admin/users', '_blank')\">🪟 新窗口打开用户管理</button>\n    </div>\n\n    <div id=\"log\" class=\"log\">\n        <h3>测试日志：</h3>\n        <div id=\"log-content\">页面加载完成，等待测试...</div>\n    </div>\n\n    <script>\n        function log(message) {\n            const logContent = document.getElementById('log-content');\n            const timestamp = new Date().toLocaleTimeString();\n            logContent.innerHTML += '<br>[' + timestamp + '] ' + message;\n        }\n\n        // 监听所有点击事件\n        document.addEventListener('click', function(e) {\n            if (e.target.tagName === 'A') {\n                log('点击链接: ' + e.target.href);\n                log('链接文本: ' + e.target.textContent);\n\n                // 检查是否被阻止\n                setTimeout(function() {\n                    if (window.location.href === e.target.href) {\n                        log('✅ 跳转成功');\n                    } else {\n                        log('❌ 跳转失败，当前URL: ' + window.location.href);\n                    }\n                }, 100);\n            } else if (e.target.tagName === 'BUTTON') {\n                log('点击按钮: ' + e.target.textContent);\n            }\n        });\n\n        // 监听页面跳转\n        window.addEventListener('beforeunload', function() {\n            log('页面即将跳转...');\n        });\n\n        log('JavaScript加载完成');\n    </script>\n</body>\n</html>\n    '''\n\n@admin_bp.route('/api/users', methods=['GET'])\n@login_required\n@admin_required\ndef get_users():\n    \"\"\"获取用户列表\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        \n        # 支持按角色和状态过滤\n        role = request.args.get('role')\n        status = request.args.get('status')\n        \n        users_query = User.query\n        \n        if role:\n            users_query = users_query.filter(User.role == role)\n            \n        if status:\n            users_query = users_query.filter(User.status == status)\n            \n        # 按创建时间倒序排列\n        users_query = users_query.order_by(User.created_at.desc())\n        \n        # 执行分页查询\n        pagination = users_query.paginate(page=page, per_page=per_page)\n        \n        # 格式化结果 - 包含完整的用户数据管理信息\n        users_data = []\n        for user in pagination.items:\n            # 获取用户财务信息\n            finance_info = {\n                'balance': 0,\n                'total_spent': 0,\n                'vip_level': 0,\n                'vip_expire_at': None,\n                'basic_quota_used': 0,\n                'premium_quota_used': 0,\n                'quota_reset_date': None,\n                'remaining_quotas': {'basic_remaining': 0, 'premium_remaining': 0, 'can_exchange': False, 'exchange_available': 0}\n            }\n\n            # 仅管理员可以看到财务信息\n            if user.finance and current_user.can_access_finance():\n                finance_info.update({\n                    'balance': user.finance.balance,\n                    'total_spent': user.finance.total_spent,\n                    'vip_level': user.finance.vip_level,\n                    'vip_expire_at': user.finance.vip_expire_at.strftime('%Y-%m-%d %H:%M:%S') if user.finance.vip_expire_at else None,\n                    'basic_quota_used': user.finance.basic_quota_used,\n                    'premium_quota_used': user.finance.premium_quota_used,\n                    'quota_reset_date': user.finance.quota_reset_date.strftime('%Y-%m-%d') if user.finance.quota_reset_date else None,\n                    'remaining_quotas': user.finance.get_remaining_quotas()\n                })\n\n            # 统计用户行为数据 - 使用安全的查询方式\n            try:\n                articles_count = user.articles.count() if hasattr(user, 'articles') and user.articles else 0\n            except:\n                articles_count = 0\n\n            try:\n                bookmarks_count = user.bookmarks.count() if hasattr(user, 'bookmarks') and user.bookmarks else 0\n            except:\n                bookmarks_count = 0\n\n            try:\n                favorites_count = user.favorites.count() if hasattr(user, 'favorites') and user.favorites else 0\n            except:\n                favorites_count = 0\n\n            try:\n                support_tickets_count = user.support_tickets.count() if hasattr(user, 'support_tickets') and user.support_tickets else 0\n            except:\n                support_tickets_count = 0\n\n            try:\n                # 使用数据库查询而不是关系属性\n                from models import ParagraphBookmark\n                paragraph_bookmarks_count = ParagraphBookmark.query.filter_by(user_id=user.id).count()\n            except:\n                paragraph_bookmarks_count = 0\n\n            user_stats = {\n                'articles_count': articles_count,\n                'bookmarks_count': bookmarks_count,\n                'favorites_count': favorites_count,\n                'support_tickets_count': support_tickets_count,\n                'paragraph_bookmarks_count': paragraph_bookmarks_count\n            }\n\n            users_data.append({\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'role': user.role,\n                'email_verified': user.email_verified,\n                'last_login': user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else None,\n                'created_at': user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else None,\n                'finance': finance_info,\n                'stats': user_stats\n            })\n        \n        return jsonify({\n            'success': True,\n            'users': users_data,\n            'total': pagination.total,\n            'pages': pagination.pages,\n            'current_page': pagination.page\n        })\n    except Exception as e:\n        logger.error(f\"获取用户列表API失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_users_stats():\n    \"\"\"获取用户统计信息API\"\"\"\n    try:\n        from datetime import datetime, timedelta\n        from sqlalchemy import func\n\n        # 基础统计\n        total_users = User.query.count()\n        active_users = User.query.filter(User.last_login.isnot(None)).count()\n        vip_users = User.query.filter(User.is_vip == True).count()\n\n        # 时间统计\n        today = datetime.now().date()\n        week_ago = today - timedelta(days=7)\n        month_ago = today - timedelta(days=30)\n\n        today_registered = User.query.filter(User.created_at >= today).count()\n        week_registered = User.query.filter(User.created_at >= week_ago).count()\n        month_registered = User.query.filter(User.created_at >= month_ago).count()\n\n        # 角色分布\n        role_stats = db.session.query(\n            User.role,\n            func.count(User.id).label('count')\n        ).group_by(User.role).all()\n\n        role_distribution = {stat.role: stat.count for stat in role_stats}\n\n        # 会员类型分布\n        membership_stats = db.session.query(\n            User.membership_type,\n            func.count(User.id).label('count')\n        ).group_by(User.membership_type).all()\n\n        membership_distribution = {\n            stat.membership_type or 'free': stat.count\n            for stat in membership_stats\n        }\n\n        # 注册趋势（最近7天）\n        registration_trend = []\n        for i in range(7):\n            date = today - timedelta(days=i)\n            count = User.query.filter(\n                func.date(User.created_at) == date\n            ).count()\n            registration_trend.append({\n                'date': date.strftime('%Y-%m-%d'),\n                'count': count\n            })\n        registration_trend.reverse()\n\n        stats = {\n            'total_users': total_users,\n            'active_users': active_users,\n            'vip_users': vip_users,\n            'today_registered': today_registered,\n            'this_week_registered': week_registered,\n            'this_month_registered': month_registered,\n            'role_distribution': role_distribution,\n            'membership_distribution': membership_distribution,\n            'registration_trend': registration_trend\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取用户统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>', methods=['GET'])\n@login_required\n@admin_required\ndef get_user(user_id):\n    \"\"\"获取单个用户详细信息\"\"\"\n    try:\n        from sqlalchemy.orm import joinedload\n        user = User.query.options(joinedload(User.finance)).filter_by(id=user_id).first()\n\n        if not user:\n            return jsonify({'success': False, 'error': '用户不存在'}), 404\n\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'role': user.role,\n            'email_verified': user.email_verified,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'last_login': user.last_login.isoformat() if user.last_login else None,\n            'finance': None\n        }\n\n        # 添加财务信息 - 仅管理员可见\n        if user.finance and current_user.can_access_finance():\n            user_data['finance'] = {\n                'vip_level': user.finance.vip_level,\n                'vip_expire_at': user.finance.vip_expire_at.isoformat() if user.finance.vip_expire_at else None,\n                'basic_quota': user.finance.basic_quota,\n                'basic_quota_used': user.finance.basic_quota_used,\n                'premium_quota': user.finance.premium_quota,\n                'premium_quota_used': user.finance.premium_quota_used,\n                'balance': float(user.finance.balance) if user.finance.balance else 0.0\n            }\n\n        return jsonify({'success': True, 'user': user_data})\n\n    except Exception as e:\n        logger.error(f\"获取用户信息失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>/details', methods=['GET'])\n@login_required\n@admin_required\ndef get_user_details(user_id):\n    \"\"\"获取用户详细信息，包括消费记录、推广信息等\"\"\"\n    try:\n        from sqlalchemy.orm import joinedload\n        from models import UserConsumptionRecord, UserReferralRecord, UserPointsRecord, Article\n\n        # 获取用户基本信息和财务信息\n        user = User.query.options(joinedload(User.finance)).filter_by(id=user_id).first()\n\n        if not user:\n            return jsonify({'success': False, 'error': '用户不存在'}), 404\n\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'role': user.role,\n            'email_verified': user.email_verified,\n            'created_at': user.created_at.isoformat() if user.created_at else None,\n            'last_login': user.last_login.isoformat() if user.last_login else None,\n            'finance': None,\n            'consumption_records': [],\n            'referral_rewards': [],\n            'referred_records': []\n        }\n\n        # 添加财务信息 - 仅管理员可见\n        if user.finance and current_user.can_access_finance():\n            user_data['finance'] = {\n                'vip_level': user.finance.vip_level,\n                'vip_expire_at': user.finance.vip_expire_at.isoformat() if user.finance.vip_expire_at else None,\n                'basic_quota': user.finance.basic_quota,\n                'basic_quota_used': user.finance.basic_quota_used,\n                'premium_quota': user.finance.premium_quota,\n                'premium_quota_used': user.finance.premium_quota_used,\n                'balance': float(user.finance.balance) if user.finance.balance else 0.0,\n                'points_balance': user.finance.points_balance,\n                'referral_code': user.finance.referral_code,\n                'referred_by': user.finance.referred_by,\n                'last_quota_reset': user.finance.last_quota_reset.isoformat() if user.finance.last_quota_reset else None\n            }\n\n        # 获取购买记录统计（Purchase表）\n        from models import Purchase\n        purchases = Purchase.query.filter_by(user_id=user_id).all()\n\n        # 购买统计\n        purchase_stats = {\n            'total_purchases': len(purchases),\n            'basic_purchases': len([p for p in purchases if p.content_type == 'basic']),\n            'premium_purchases': len([p for p in purchases if p.content_type == 'premium']),\n            'total_spent': sum(p.amount for p in purchases),\n            'cash_purchases': len([p for p in purchases if p.payment_method in ['alipay', 'wechat', 'mock']]),\n            'quota_purchases': len([p for p in purchases if p.payment_method == 'quota']),\n            'points_purchases': len([p for p in purchases if p.payment_method == 'points'])\n        }\n        user_data['purchase_stats'] = purchase_stats\n\n        # 最近购买记录（最多10条）\n        recent_purchases = Purchase.query.filter_by(user_id=user_id)\\\n                                        .order_by(Purchase.created_at.desc())\\\n                                        .limit(10).all()\n\n        user_data['recent_purchases'] = []\n        for purchase in recent_purchases:\n            article = Article.query.get(purchase.article_id)\n            user_data['recent_purchases'].append({\n                'id': purchase.id,\n                'article_title': article.title if article else '文章已删除',\n                'content_type': purchase.content_type,\n                'amount': purchase.amount,\n                'payment_method': purchase.payment_method,\n                'transaction_id': purchase.transaction_id,\n                'created_at': purchase.created_at.isoformat() if purchase.created_at else None\n            })\n\n        # 获取最近的消费记录（最多10条）\n        consumption_records = UserConsumptionRecord.query.filter_by(user_id=user_id)\\\n            .join(Article, UserConsumptionRecord.article_id == Article.id)\\\n            .order_by(UserConsumptionRecord.created_at.desc())\\\n            .limit(10).all()\n\n        for record in consumption_records:\n            user_data['consumption_records'].append({\n                'id': record.id,\n                'article_id': record.article_id,\n                'article_title': record.article.title if record.article else None,\n                'content_type': record.content_type,\n                'cost_type': record.cost_type,\n                'cost_amount': record.cost_amount,\n                'created_at': record.created_at.isoformat()\n            })\n\n        # 获取推广奖励记录\n        referral_rewards = UserReferralRecord.query.filter_by(referrer_id=user_id)\\\n            .order_by(UserReferralRecord.created_at.desc()).all()\n\n        for reward in referral_rewards:\n            user_data['referral_rewards'].append({\n                'id': reward.id,\n                'referred_id': reward.referred_id,\n                'reward_type': reward.reward_type,\n                'reward_amount': reward.reward_amount,\n                'trigger_event': reward.trigger_event,\n                'created_at': reward.created_at.isoformat()\n            })\n\n        # 获取被推广记录\n        referred_records = UserReferralRecord.query.filter_by(referred_id=user_id)\\\n            .order_by(UserReferralRecord.created_at.desc()).all()\n\n        for record in referred_records:\n            user_data['referred_records'].append({\n                'id': record.id,\n                'referrer_id': record.referrer_id,\n                'reward_type': record.reward_type,\n                'reward_amount': record.reward_amount,\n                'trigger_event': record.trigger_event,\n                'created_at': record.created_at.isoformat()\n            })\n\n        return jsonify({'success': True, 'user': user_data})\n\n    except Exception as e:\n        logger.error(f\"获取用户详细信息失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>', methods=['PUT'])\n@login_required\n@admin_required\ndef update_user(user_id):\n    \"\"\"更新用户信息\"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n        data = request.get_json()\n        \n        # 不允许修改超级管理员状态\n        if user.role == 'superadmin' and current_user.role != 'superadmin':\n            return jsonify({'success': False, 'error': '您没有权限修改超级管理员用户'}), 403\n        \n        # 不允许用户修改自己的角色（防止权限降级）\n        if user.id == current_user.id and 'role' in data and data['role'] != user.role:\n            return jsonify({'success': False, 'error': '不能修改自己的角色'}), 403\n        \n        if 'username' in data:\n            # 检查用户名是否已被使用\n            if data['username'] != user.username:\n                existing_user = User.query.filter_by(username=data['username']).first()\n                if existing_user:\n                    return jsonify({'success': False, 'error': '此用户名已被使用'}), 400\n            user.username = data['username']\n            \n        if 'email' in data:\n            # 检查邮箱是否已被使用\n            if data['email'] != user.email:\n                existing_user = User.query.filter_by(email=data['email']).first()\n                if existing_user:\n                    return jsonify({'success': False, 'error': '此邮箱已被使用'}), 400\n            user.email = data['email']\n            \n        if 'role' in data and current_user.role == 'superadmin':\n            user.role = data['role']\n            \n        if 'status' in data:\n            user.status = data['status']\n            \n        db.session.commit()\n        return jsonify({'success': True, 'message': '用户信息已更新'})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新用户 {user_id} 失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>/reset_password', methods=['POST'])\n@login_required\n@admin_required\ndef reset_user_password(user_id):\n    \"\"\"重置用户密码\"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n        data = request.get_json()\n        \n        # 超级管理员密码只能由超级管理员重置\n        if user.role == 'superadmin' and current_user.role != 'superadmin':\n            return jsonify({'success': False, 'error': '您没有权限重置超级管理员密码'}), 403\n        \n        new_password = data.get('new_password')\n        if not new_password or len(new_password) < 8:\n            return jsonify({'success': False, 'error': '新密码长度必须至少为8个字符'}), 400\n            \n        # 设置新密码\n        user.password = generate_password_hash(new_password)\n        \n        db.session.commit()\n        return jsonify({'success': True, 'message': '用户密码已重置'})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"重置用户 {user_id} 密码失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users', methods=['POST'])\n@login_required\n@admin_required\ndef create_user():\n    \"\"\"创建新用户\"\"\"\n    try:\n        data = request.get_json()\n        \n        username = data.get('username')\n        email = data.get('email')\n        password = data.get('password')\n        role = data.get('role', 'user')\n        \n        # 验证必填字段\n        if not username or not email or not password:\n            return jsonify({'success': False, 'error': '用户名、邮箱和密码都是必需的'}), 400\n            \n        # 验证密码长度\n        if len(password) < 8:\n            return jsonify({'success': False, 'error': '密码长度必须至少为8个字符'}), 400\n            \n        # 检查用户名是否已被使用\n        existing_user = User.query.filter_by(username=username).first()\n        if existing_user:\n            return jsonify({'success': False, 'error': '此用户名已被使用'}), 400\n            \n        # 检查邮箱是否已被使用\n        existing_user = User.query.filter_by(email=email).first()\n        if existing_user:\n            return jsonify({'success': False, 'error': '此邮箱已被使用'}), 400\n            \n        # 创建新用户\n        new_user = User(\n            username=username,\n            email=email,\n            password=generate_password_hash(password),\n            role=role,\n            status='active'\n        )\n        \n        db.session.add(new_user)\n        db.session.commit()\n        \n        return jsonify({\n            'success': True, \n            'message': '用户创建成功',\n            'user': {\n                'id': new_user.id,\n                'username': new_user.username,\n                'email': new_user.email,\n                'role': new_user.role,\n                'status': new_user.status\n            }\n        }), 201\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"创建用户失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>', methods=['DELETE'])\n@login_required\n@admin_required\ndef delete_user(user_id):\n    \"\"\"删除用户\"\"\"\n    try:\n        user = User.query.get_or_404(user_id)\n        \n        # 不允许删除超级管理员\n        if user.role == 'superadmin':\n            return jsonify({'success': False, 'error': '不能删除超级管理员用户'}), 403\n            \n        # 不允许删除自己\n        if user.id == current_user.id:\n            return jsonify({'success': False, 'error': '不能删除当前登录的用户账户'}), 403\n            \n        db.session.delete(user)\n        db.session.commit()\n        return jsonify({'success': True, 'message': '用户已删除'})\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"删除用户 {user_id} 失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/<int:user_id>/vip', methods=['PUT'])\n@login_required\n@finance_required\ndef update_user_vip(user_id):\n    \"\"\"更新用户VIP状态和配额\"\"\"\n    try:\n        from models import UserFinance\n        from datetime import datetime, timedelta\n\n        user = User.query.get_or_404(user_id)\n        data = request.get_json()\n\n        # 确保用户有财务记录\n        if not user.finance:\n            user_finance = UserFinance(user_id=user.id)\n            db.session.add(user_finance)\n            db.session.flush()  # 获取ID但不提交\n        else:\n            user_finance = user.finance\n\n        # 更新VIP等级\n        if 'vip_level' in data:\n            vip_level = int(data['vip_level'])\n            if vip_level not in [0, 1, 2]:\n                return jsonify({'success': False, 'error': 'VIP等级必须是0、1或2'}), 400\n            user_finance.vip_level = vip_level\n\n            # 如果设置为VIP，自动设置过期时间（如果没有提供）\n            if vip_level > 0 and 'vip_expire_at' not in data:\n                # 默认设置为1个月后过期\n                user_finance.vip_expire_at = datetime.utcnow() + timedelta(days=30)\n\n        # 更新VIP过期时间\n        if 'vip_expire_at' in data and data['vip_expire_at']:\n            try:\n                user_finance.vip_expire_at = datetime.strptime(data['vip_expire_at'], '%Y-%m-%d %H:%M:%S')\n            except ValueError:\n                try:\n                    user_finance.vip_expire_at = datetime.strptime(data['vip_expire_at'], '%Y-%m-%d')\n                except ValueError:\n                    return jsonify({'success': False, 'error': 'VIP过期时间格式错误，请使用YYYY-MM-DD或YYYY-MM-DD HH:MM:SS'}), 400\n\n        # 重置配额\n        if data.get('reset_quota', False):\n            user_finance.basic_quota_used = 0\n            user_finance.premium_quota_used = 0\n            user_finance.quota_reset_date = datetime.utcnow()\n\n        # 更新配额总量\n        if 'basic_quota' in data:\n            user_finance.basic_quota = max(0, int(data['basic_quota']))\n\n        if 'premium_quota' in data:\n            user_finance.premium_quota = max(0, int(data['premium_quota']))\n\n        # 手动调整配额使用量\n        if 'basic_quota_used' in data:\n            user_finance.basic_quota_used = max(0, int(data['basic_quota_used']))\n\n        if 'premium_quota_used' in data:\n            user_finance.premium_quota_used = max(0, int(data['premium_quota_used']))\n\n        # 更新余额\n        if 'balance' in data:\n            user_finance.balance = float(data['balance'])\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '用户VIP状态已更新',\n            'finance': {\n                'vip_level': user_finance.vip_level,\n                'vip_expire_at': user_finance.vip_expire_at.strftime('%Y-%m-%d %H:%M:%S') if user_finance.vip_expire_at else None,\n                'basic_quota': user_finance.basic_quota,\n                'basic_quota_used': user_finance.basic_quota_used,\n                'premium_quota': user_finance.premium_quota,\n                'premium_quota_used': user_finance.premium_quota_used,\n                'balance': float(user_finance.balance) if user_finance.balance else 0.0\n            }\n        })\n    except Exception as e:\n        db.session.rollback()\n        logger.error(f\"更新用户 {user_id} VIP状态失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# --- 公告管理 ---\n@admin_bp.route('/announcements')\n@login_required\n@admin_required\ndef announcement_list():\n    \"\"\"公告管理页面\"\"\"\n    try:\n        return render_template('admin/announcement_list.html')\n    except Exception as e:\n        logger.error(f\"加载公告管理页面失败: {str(e)}\")\n        flash('加载公告管理页面失败', 'error')\n        return redirect(url_for('admin.dashboard'))\n\n@admin_bp.route('/api/announcements', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_announcements():\n    \"\"\"获取公告列表API\"\"\"\n    try:\n        from models import Announcement\n        announcements = Announcement.query.order_by(Announcement.created_at.desc()).all()\n\n        result = []\n        for announcement in announcements:\n            result.append({\n                'id': announcement.id,\n                'title': announcement.title,\n                'content': announcement.content,\n                'priority': announcement.priority,\n                'is_active': announcement.is_active,\n                'start_date': announcement.start_date.isoformat() if announcement.start_date else None,\n                'end_date': announcement.end_date.isoformat() if announcement.end_date else None,\n                'created_at': announcement.created_at.isoformat() if announcement.created_at else None\n            })\n\n        return jsonify({'success': True, 'data': result})\n    except Exception as e:\n        logger.error(f\"获取公告列表失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_announcements_stats():\n    \"\"\"获取公告统计信息API\"\"\"\n    try:\n        from models import Announcement\n        from datetime import datetime, timedelta\n        from sqlalchemy import func\n\n        # 基础统计\n        total_announcements = Announcement.query.count()\n\n        # 活跃公告（未过期的）\n        now = datetime.now()\n        active_announcements = Announcement.query.filter(\n            Announcement.is_active == True,\n            Announcement.end_date >= now\n        ).count()\n\n        # 过期公告\n        expired_announcements = Announcement.query.filter(\n            Announcement.end_date < now\n        ).count()\n\n        # 时间统计\n        today = datetime.now().date()\n        week_ago = today - timedelta(days=7)\n\n        today_created = Announcement.query.filter(\n            func.date(Announcement.created_at) == today\n        ).count()\n\n        week_created = Announcement.query.filter(\n            Announcement.created_at >= week_ago\n        ).count()\n\n        # 公告类型分布（这里使用模拟数据，实际应该从数据库字段获取）\n        announcement_types = {\n            'system': total_announcements // 3,\n            'promotion': total_announcements // 3,\n            'maintenance': total_announcements - (total_announcements // 3) * 2\n        }\n\n        # 浏览统计（模拟数据，实际应该从浏览记录表获取）\n        total_views = total_announcements * 1028  # 模拟总浏览量\n        avg_views_per_announcement = 1028 if total_announcements > 0 else 0\n\n        stats = {\n            'total_announcements': total_announcements,\n            'active_announcements': active_announcements,\n            'expired_announcements': expired_announcements,\n            'today_created': today_created,\n            'this_week_created': week_created,\n            'announcement_types': announcement_types,\n            'view_stats': {\n                'total_views': total_views,\n                'avg_views_per_announcement': avg_views_per_announcement\n            }\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取公告统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements', methods=['POST'])\n@login_required\n@admin_required\ndef api_create_announcement():\n    \"\"\"创建公告API\"\"\"\n    try:\n        from models import Announcement\n        data = request.get_json()\n\n        announcement = Announcement(\n            title=data.get('title'),\n            content=data.get('content'),\n            priority=data.get('priority', 'normal'),\n            is_active=data.get('is_active', True),\n            start_date=datetime.strptime(data.get('start_date'), '%Y-%m-%dT%H:%M') if data.get('start_date') else None,\n            end_date=datetime.strptime(data.get('end_date'), '%Y-%m-%dT%H:%M') if data.get('end_date') else None\n        )\n\n        db.session.add(announcement)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '公告创建成功'})\n    except Exception as e:\n        logger.error(f\"创建公告失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_announcement(announcement_id):\n    \"\"\"获取单个公告API\"\"\"\n    try:\n        from models import Announcement\n        announcement = Announcement.query.get_or_404(announcement_id)\n\n        result = {\n            'id': announcement.id,\n            'title': announcement.title,\n            'content': announcement.content,\n            'priority': announcement.priority,\n            'is_active': announcement.is_active,\n            'start_date': announcement.start_date.isoformat() if announcement.start_date else None,\n            'end_date': announcement.end_date.isoformat() if announcement.end_date else None,\n            'created_at': announcement.created_at.isoformat() if announcement.created_at else None\n        }\n\n        return jsonify({'success': True, 'data': result})\n    except Exception as e:\n        logger.error(f\"获取公告失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_announcement(announcement_id):\n    \"\"\"更新公告API\"\"\"\n    try:\n        from models import Announcement\n        announcement = Announcement.query.get_or_404(announcement_id)\n        data = request.get_json()\n\n        announcement.title = data.get('title', announcement.title)\n        announcement.content = data.get('content', announcement.content)\n        announcement.priority = data.get('priority', announcement.priority)\n        announcement.is_active = data.get('is_active', announcement.is_active)\n\n        if data.get('start_date'):\n            announcement.start_date = datetime.strptime(data.get('start_date'), '%Y-%m-%dT%H:%M')\n        if data.get('end_date'):\n            announcement.end_date = datetime.strptime(data.get('end_date'), '%Y-%m-%dT%H:%M')\n\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '公告更新成功'})\n    except Exception as e:\n        logger.error(f\"更新公告失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/announcements/<int:announcement_id>', methods=['DELETE'])\n@login_required\n@admin_required\ndef api_delete_announcement(announcement_id):\n    \"\"\"删除公告API\"\"\"\n    try:\n        from models import Announcement\n        announcement = Announcement.query.get_or_404(announcement_id)\n\n        db.session.delete(announcement)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '公告删除成功'})\n    except Exception as e:\n        logger.error(f\"删除公告失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# --- 积分管理 ---\n@admin_bp.route('/points')\n@login_required\n@admin_required\ndef points_management():\n    \"\"\"积分管理页面\"\"\"\n    try:\n        return render_template('admin/points_management.html')\n    except Exception as e:\n        logger.error(f\"加载积分管理页面失败: {str(e)}\")\n        flash('加载积分管理页面失败', 'error')\n        return redirect(url_for('admin.dashboard'))\n\n@admin_bp.route('/api/points/transactions', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_point_transactions():\n    \"\"\"获取积分交易记录API\"\"\"\n    try:\n        from models import PointTransaction\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n\n        transactions = PointTransaction.query.order_by(PointTransaction.created_at.desc()).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        result = []\n        for transaction in transactions.items:\n            result.append({\n                'id': transaction.id,\n                'user_id': transaction.user_id,\n                'username': transaction.user.username if transaction.user else '未知用户',\n                'points': transaction.points,\n                'transaction_type': transaction.transaction_type,\n                'description': transaction.description,\n                'created_at': transaction.created_at.isoformat() if transaction.created_at else None\n            })\n\n        return jsonify({\n            'success': True,\n            'data': result,\n            'pagination': {\n                'page': transactions.page,\n                'pages': transactions.pages,\n                'per_page': transactions.per_page,\n                'total': transactions.total\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取积分交易记录失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/points/adjust', methods=['POST'])\n@login_required\n@admin_required\ndef api_adjust_user_points():\n    \"\"\"调整用户积分API\"\"\"\n    try:\n        from models import PointTransaction\n        data = request.get_json()\n\n        user_id = data.get('user_id')\n        points = data.get('points')\n        description = data.get('description', '管理员调整')\n\n        user = User.query.get_or_404(user_id)\n\n        # 创建积分交易记录\n        transaction = PointTransaction(\n            user_id=user_id,\n            points=points,\n            transaction_type='admin_adjust',\n            description=description\n        )\n\n        # 更新用户积分\n        user.points = (user.points or 0) + points\n\n        db.session.add(transaction)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '积分调整成功'})\n    except Exception as e:\n        logger.error(f\"调整用户积分失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/points/statistics', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_points_statistics():\n    \"\"\"获取积分统计信息API\"\"\"\n    try:\n        from models import PointTransaction\n        from sqlalchemy import func\n        from datetime import date\n\n        # 总积分发放（正数）\n        total_issued = db.session.query(func.sum(PointTransaction.points)).filter(\n            PointTransaction.points > 0\n        ).scalar() or 0\n\n        # 总积分消费（负数的绝对值）\n        total_spent = db.session.query(func.sum(PointTransaction.points)).filter(\n            PointTransaction.points < 0\n        ).scalar() or 0\n        total_spent = abs(total_spent)\n\n        # 有积分的用户数\n        active_users = db.session.query(func.count(func.distinct(User.id))).filter(\n            User.points > 0\n        ).scalar() or 0\n\n        # 今日交易数\n        today = date.today()\n        today_transactions = PointTransaction.query.filter(\n            func.date(PointTransaction.created_at) == today\n        ).count()\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'total_issued': total_issued,\n                'total_spent': total_spent,\n                'active_users': active_users,\n                'today_transactions': today_transactions\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取积分统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/users/search', methods=['GET'])\n@login_required\n@admin_required\ndef api_search_users():\n    \"\"\"搜索用户API\"\"\"\n    try:\n        query = request.args.get('q', '').strip()\n        if len(query) < 2:\n            return jsonify({'success': True, 'data': []})\n\n        # 搜索用户名或邮箱\n        users = User.query.filter(\n            db.or_(\n                User.username.ilike(f'%{query}%'),\n                User.email.ilike(f'%{query}%')\n            )\n        ).limit(10).all()\n\n        result = []\n        for user in users:\n            result.append({\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'points': user.points or 0\n            })\n\n        return jsonify({'success': True, 'data': result})\n    except Exception as e:\n        logger.error(f\"搜索用户失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# --- 文章媒体文件管理 ---\n@admin_bp.route('/api/articles/<int:article_id>', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_article_info(article_id):\n    \"\"\"获取文章完整信息API - 用于Vue3管理后台编辑页面\"\"\"\n    try:\n        article = Article.query.get_or_404(article_id)\n\n        return jsonify({\n            'success': True,\n            'article': {\n                'id': article.id,\n                'title': article.title,\n                'content': article.content,\n                'premium_content': article.premium_content,\n                'status': article.status,\n                'reader_id': article.reader_id,\n                'author_id': article.author_id,\n                'tags': [{'id': tag.id, 'name': tag.name} for tag in article.tags] if article.tags else [],\n                'publish_date': article.publish_date.isoformat() if article.publish_date else None,\n                'language_code': article.language_code,\n                'content_type': article.content_type,\n                'price': article.price,\n                'premium_price': article.premium_price,\n                'cover_image': article.cover_image,\n                'cover_url': article.cover_url,\n                'created_at': article.created_at.isoformat() if article.created_at else None,\n                'updated_at': article.updated_at.isoformat() if article.updated_at else None,\n                'views': article.views,\n                'original_id': article.original_id,\n                'author': article.author,\n                'original_audio_url': article.original_audio_url,\n                'content_images': article.content_images,\n                'audio_sync_data': article.audio_sync_data\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取文章信息失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/media', methods=['GET'])\n@login_required\n@staff_required\ndef api_get_article_media(article_id):\n    \"\"\"获取文章关联的媒体文件API\"\"\"\n    try:\n        from models import MediaFile\n        article = Article.query.get_or_404(article_id)\n\n        # 获取与文章关联的媒体文件\n        media_files = MediaFile.query.filter_by(article_id=article_id).order_by(MediaFile.created_at.desc()).all()\n\n        # 按文件类型分组\n        audio_files = []\n        subtitle_files = []\n        image_files = []\n\n        for file in media_files:\n            file_dict = file.to_dict()\n            if file.file_type == 'audio':\n                audio_files.append(file_dict)\n            elif file.file_type == 'subtitle':\n                subtitle_files.append(file_dict)\n            elif file.file_type == 'image':\n                image_files.append(file_dict)\n\n        # 如果没有字幕文件记录，尝试从audio_sync_data中提取\n        if not subtitle_files and article.audio_sync_data:\n            try:\n                import json\n                sync_data = json.loads(article.audio_sync_data)\n                subtitle_url = sync_data.get('subtitle_url')\n                if subtitle_url and article.original_id:\n                    # 构建虚拟字幕文件记录\n                    subtitle_files.append({\n                        'id': f'virtual_{article.original_id}_subtitle',\n                        'filename': f'{article.original_id}_subtitle.srt',\n                        'original_filename': f'{article.original_id}_subtitle.srt',\n                        'file_type': 'subtitle',\n                        'file_category': 'subtitle',\n                        'url': subtitle_url,\n                        'upload_status': 'uploaded',\n                        'created_at': article.created_at.isoformat() if article.created_at else None\n                    })\n                    logger.info(f\"从audio_sync_data中提取到字幕文件: {subtitle_url}\")\n            except (json.JSONDecodeError, Exception) as e:\n                logger.warning(f\"解析audio_sync_data失败: {str(e)}\")\n\n        # 如果没有图片文件记录，尝试从content_images中提取\n        if not image_files and article.content_images:\n            try:\n                import json\n                image_urls = json.loads(article.content_images)\n                for i, image_url in enumerate(image_urls, 1):\n                    if image_url and image_url.strip():\n                        image_files.append({\n                            'id': f'virtual_{article.original_id}_image_{i:02d}',\n                            'filename': f'{article.original_id}_image_{i:02d}.jpg',\n                            'original_filename': f'{article.original_id}_image_{i:02d}.jpg',\n                            'file_type': 'image',\n                            'file_category': 'content',\n                            'url': image_url,\n                            'upload_status': 'uploaded',\n                            'created_at': article.created_at.isoformat() if article.created_at else None\n                        })\n                logger.info(f\"从content_images中提取到{len(image_files)}个图片文件\")\n            except (json.JSONDecodeError, Exception) as e:\n                logger.warning(f\"解析content_images失败: {str(e)}\")\n\n        # 如果没有音频文件记录，尝试从original_audio_url中提取\n        if not audio_files and article.original_audio_url and article.original_id:\n            audio_files.append({\n                'id': f'virtual_{article.original_id}_audio',\n                'filename': f'{article.original_id}_audio.mp3',\n                'original_filename': f'{article.original_id}_audio.mp3',\n                'file_type': 'audio',\n                'file_category': 'original_audio',\n                'url': article.original_audio_url,\n                'upload_status': 'uploaded',\n                'created_at': article.created_at.isoformat() if article.created_at else None\n            })\n            logger.info(f\"从original_audio_url中提取到音频文件: {article.original_audio_url}\")\n\n        # 如果没有封面文件记录，尝试从cover_url中提取\n        cover_files = []\n        cover_media_files = [f for f in media_files if f.file_category == 'cover']\n        if not cover_media_files and article.cover_url and article.original_id:\n            cover_files.append({\n                'id': f'virtual_{article.original_id}_cover',\n                'filename': f'{article.original_id}_cover.jpg',\n                'original_filename': f'{article.original_id}_cover.jpg',\n                'file_type': 'image',\n                'file_category': 'cover',\n                'url': article.cover_url,\n                'upload_status': 'uploaded',\n                'created_at': article.created_at.isoformat() if article.created_at else None\n            })\n            logger.info(f\"从cover_url中提取到封面文件: {article.cover_url}\")\n        else:\n            # 如果有MediaFile记录的封面，使用它们\n            for file in cover_media_files:\n                cover_files.append(file.to_dict())\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'audio': audio_files,\n                'subtitle': subtitle_files,\n                'image': image_files,\n                'cover': cover_files\n            },\n            'article': {\n                'id': article.id,\n                'title': article.title,\n                'original_id': article.original_id\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取文章媒体文件失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/media/upload', methods=['POST'])\n@login_required\n@staff_required\ndef api_upload_article_media(article_id):\n    \"\"\"为文章上传媒体文件API\"\"\"\n    try:\n        from models import MediaFile\n        article = Article.query.get_or_404(article_id)\n\n        if 'file' not in request.files:\n            return jsonify({'success': False, 'error': '没有选择文件'}), 400\n\n        file = request.files['file']\n        if file.filename == '':\n            return jsonify({'success': False, 'error': '没有选择文件'}), 400\n\n        # 这里应该调用R2上传功能\n        # 暂时创建媒体文件记录\n        media_file = MediaFile(\n            article_id=article_id,\n            original_id=article.original_id,\n            filename=file.filename,\n            original_filename=file.filename,\n            file_type='image',  # 需要根据文件扩展名判断\n            url='https://example.com/placeholder.jpg',  # 临时URL\n            r2_key='placeholder'\n        )\n\n        db.session.add(media_file)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '文件上传成功', 'data': media_file.to_dict()})\n    except Exception as e:\n        logger.error(f\"上传文章媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/media/<int:file_id>', methods=['DELETE'])\n@login_required\n@staff_required\ndef api_delete_media_file(file_id):\n    \"\"\"删除媒体文件API\"\"\"\n    try:\n        from models import MediaFile\n        file = MediaFile.query.get_or_404(file_id)\n\n        # 这里应该同时删除R2上的文件\n        db.session.delete(file)\n        db.session.commit()\n\n        return jsonify({'success': True, 'message': '文件删除成功'})\n    except Exception as e:\n        logger.error(f\"删除媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n\n# --- 音频同步功能管理 ---\n@admin_bp.route('/audio-sync-test')\n@login_required\n@admin_required\ndef audio_sync_test():\n    \"\"\"音频同步测试页面\"\"\"\n    # 查找测试文章\n    test_article = Article.query.filter_by(title=\"【测试】惊天预兆！即将发生的事！\").first()\n\n    return render_template('audio_sync_test.html', article=test_article)\n\n@admin_bp.route('/api/media/match-by-original-id', methods=['POST'])\n@login_required\n@admin_required\ndef api_match_media_by_original_id():\n    \"\"\"根据原始ID匹配媒体文件到文章API\"\"\"\n    try:\n        from models import MediaFile\n        data = request.get_json()\n        original_id = data.get('original_id')\n\n        if not original_id:\n            return jsonify({'success': False, 'error': '缺少原始ID'}), 400\n\n        # 查找具有该原始ID的文章\n        article = Article.query.filter_by(original_id=original_id).first()\n        if not article:\n            return jsonify({'success': False, 'error': f'未找到原始ID为 {original_id} 的文章'}), 404\n\n        # 查找未关联的媒体文件（通过文件名中的OID匹配）\n        unmatched_files = MediaFile.query.filter(\n            MediaFile.article_id.is_(None),\n            MediaFile.original_filename.like(f'%OID{original_id}%')\n        ).all()\n\n        matched_count = 0\n        for file in unmatched_files:\n            file.article_id = article.id\n            file.original_id = original_id\n            matched_count += 1\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': f'成功匹配 {matched_count} 个媒体文件到文章 \"{article.title}\"',\n            'matched_count': matched_count\n        })\n    except Exception as e:\n        logger.error(f\"匹配媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/tickets/export')\n@login_required\n@staff_required\ndef export_tickets():\n    \"\"\"导出工单API\"\"\"\n    try:\n        from models import SupportTicket, User\n        from datetime import datetime\n        from flask import make_response\n        import csv\n        import io\n\n        # 获取参数\n        start_date = request.args.get('start_date')\n        end_date = request.args.get('end_date')\n        status = request.args.get('status')\n        format_type = request.args.get('format', 'txt')\n\n        if not start_date or not end_date:\n            return jsonify({'success': False, 'error': '请提供开始和结束日期'}), 400\n\n        try:\n            start_dt = datetime.strptime(start_date, '%Y-%m-%d')\n            end_dt = datetime.strptime(end_date, '%Y-%m-%d')\n            # 结束日期包含当天的所有时间\n            end_dt = end_dt.replace(hour=23, minute=59, second=59)\n        except ValueError:\n            return jsonify({'success': False, 'error': '日期格式错误，请使用YYYY-MM-DD格式'}), 400\n\n        # 构建查询 - 明确指定join条件\n        query = SupportTicket.query.join(User, SupportTicket.user_id == User.id).filter(\n            SupportTicket.created_at >= start_dt,\n            SupportTicket.created_at <= end_dt\n        )\n\n        if status:\n            query = query.filter(SupportTicket.status == status)\n\n        tickets = query.order_by(SupportTicket.created_at.asc()).all()\n\n        if format_type == 'txt':\n            # TXT格式 - 适合AI处理\n            output = io.StringIO()\n\n            # 写入AI指令模板\n            ai_template = \"\"\"请根据以下工单信息生成专业的客服回复，严格按照以下要求：\n\n【格式要求】\n1. 每行一个回复，格式为：工单ID|回复内容\n2. 工单ID必须与下方工单信息中的ID完全一致\n3. 回复内容要专业、友好、有针对性\n4. 不要包含标题行、说明文字或其他格式\n5. 直接输出可导入系统的格式\n\n【网站介绍】\n本站提供基础翻译和高级翻译服务。基础翻译包含常规文章内容，所有用户都可以阅读。高级翻译包含更详细的内容、原文对照、音频朗读等功能，需要VIP权限才能访问。VIP用户每月获得一定的阅读配额。\n\n【回复原则】\n- 充值/支付问题：请等待客服上线处理，我们会尽快为您解决充值相关问题\n\n- VIP会员问题：VIP权限激活需要1-3分钟，建议重新登录或清除浏览器缓存，如仍有问题请等待客服处理\n\n- 配额问题：基础翻译免费阅读，高级翻译需要消耗VIP配额。VIP用户每月有固定配额，可在个人中心查看剩余额度\n\n- 文章阅读问题：先记录问题，如果着急可以查看相关视频或原文（高级内容中有原文对照）\n\n- 技术故障：我们已收集您的反馈并转交技术团队，正在积极解决中，请耐心等待\n\n- 其他问题：请等待客服上线为您详细解答，或查看网站帮助文档\n\n【示例格式】\n123|您好，我们已经查看了您的账户，余额显示异常是由于系统同步延迟造成的。您的充值已经到账，请刷新页面查看。\n456|您好，感谢您购买VIP会员。请先退出账户重新登录，然后清除浏览器缓存。VIP权限需要几分钟时间同步。\n\n【工单信息】\n格式：工单ID|用户名|主题|内容|状态|创建时间\n\n\"\"\"\n            output.write(ai_template)\n\n            # 写入工单数据\n            for ticket in tickets:\n                # 格式：工单ID|用户名|主题|内容|状态|创建时间\n                content = ticket.content.replace('\\n', ' ').replace('\\r', ' ')\n                subject = ticket.subject.replace('\\n', ' ').replace('\\r', ' ')\n                line = f\"{ticket.id}|{ticket.user.username}|{subject}|{content}|{ticket.status}|{ticket.created_at.strftime('%Y-%m-%d %H:%M:%S')}\\n\"\n                output.write(line)\n\n            response = make_response(output.getvalue())\n            response.headers['Content-Type'] = 'text/plain; charset=utf-8'\n            response.headers['Content-Disposition'] = f'attachment; filename=tickets_{start_date}_to_{end_date}.txt'\n\n        else:  # CSV格式\n            output = io.StringIO()\n            writer = csv.writer(output)\n\n            # 写入标题行\n            writer.writerow(['工单ID', '用户名', '主题', '内容', '分类', '状态', '优先级', '创建时间', '最后更新'])\n\n            # 写入数据行\n            for ticket in tickets:\n                writer.writerow([\n                    ticket.id,\n                    ticket.user.username,\n                    ticket.subject,\n                    ticket.content,\n                    ticket.category or '',\n                    ticket.status,\n                    ticket.priority,\n                    ticket.created_at.strftime('%Y-%m-%d %H:%M:%S'),\n                    ticket.updated_at.strftime('%Y-%m-%d %H:%M:%S')\n                ])\n\n            response = make_response(output.getvalue())\n            response.headers['Content-Type'] = 'text/csv; charset=utf-8'\n            response.headers['Content-Disposition'] = f'attachment; filename=tickets_{start_date}_to_{end_date}.csv'\n\n        return response\n\n    except Exception as e:\n        logger.error(f\"导出工单失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'导出失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/tickets/import-replies', methods=['POST'])\n@login_required\n@staff_required\ndef import_ticket_replies():\n    \"\"\"导入工单回复API\"\"\"\n    try:\n        from models import SupportTicket, SupportMessage\n        from datetime import datetime\n\n        # 检查文件\n        if 'file' not in request.files:\n            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400\n\n        file = request.files['file']\n        if file.filename == '':\n            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400\n\n        if not file.filename.endswith('.txt'):\n            return jsonify({'success': False, 'error': '只支持TXT格式文件'}), 400\n\n        auto_close = request.form.get('auto_close') == 'true'\n\n        # 读取文件内容\n        try:\n            content = file.read().decode('utf-8')\n        except UnicodeDecodeError:\n            try:\n                content = file.read().decode('gbk')\n            except UnicodeDecodeError:\n                return jsonify({'success': False, 'error': '文件编码不支持，请使用UTF-8或GBK编码'}), 400\n\n        lines = content.strip().split('\\n')\n        processed = 0\n        success_count = 0\n        error_count = 0\n        errors = []\n\n        for line_num, line in enumerate(lines, 1):\n            line = line.strip()\n            if not line:\n                continue\n\n            # 跳过AI指令模板和说明文字\n            if (line.startswith('请根据以下工单信息') or\n                line.startswith('【') or\n                line.startswith('-') or\n                line.startswith('格式：') or\n                line.startswith('1.') or\n                line.startswith('2.') or\n                line.startswith('3.') or\n                line.startswith('4.') or\n                line.startswith('5.') or\n                '格式要求' in line or\n                '回复原则' in line or\n                '示例格式' in line or\n                '工单信息' in line or\n                not line or\n                line.isspace()):\n                continue\n\n            # 跳过原始工单数据行（包含多个|分隔符且格式为：ID|用户名|主题|内容|状态|时间）\n            parts_check = line.split('|')\n            if len(parts_check) >= 6:  # 原始工单数据有6个字段\n                # 检查最后一个字段是否像时间格式\n                last_field = parts_check[-1].strip()\n                if ('-' in last_field and ':' in last_field and len(last_field) >= 10):\n                    continue  # 跳过原始工单数据行\n\n            processed += 1\n\n            # 解析格式：工单ID|回复内容\n            parts = line.split('|', 1)\n            if len(parts) != 2:\n                # 检查是否是纯数字开头的有效回复行\n                if not (parts[0].strip().isdigit() if parts else False):\n                    # 不是有效的回复行，跳过但不计入错误\n                    processed -= 1\n                    continue\n                error_count += 1\n                errors.append(f\"第{line_num}行格式错误：{line}\")\n                continue\n\n            try:\n                ticket_id = int(parts[0])\n                reply_content = parts[1].strip()\n\n                if not reply_content:\n                    error_count += 1\n                    errors.append(f\"第{line_num}行回复内容为空\")\n                    continue\n\n                # 查找工单\n                ticket = SupportTicket.query.get(ticket_id)\n                if not ticket:\n                    error_count += 1\n                    errors.append(f\"第{line_num}行工单ID {ticket_id} 不存在\")\n                    continue\n\n                # 创建回复消息\n                message = SupportMessage(\n                    ticket_id=ticket.id,\n                    message=reply_content,\n                    is_staff_reply=True,\n                    staff_id=current_user.id,\n                    created_at=datetime.utcnow()\n                )\n                db.session.add(message)\n\n                # 更新工单状态\n                ticket.admin_reply = reply_content\n                ticket.admin_id = current_user.id\n                ticket.updated_at = datetime.utcnow()\n\n                if auto_close:\n                    ticket.status = 'closed'\n                    ticket.resolved_at = datetime.utcnow()\n                else:\n                    ticket.status = 'processing'\n\n                success_count += 1\n\n            except ValueError:\n                error_count += 1\n                errors.append(f\"第{line_num}行工单ID格式错误：{parts[0]}\")\n                continue\n            except Exception as e:\n                error_count += 1\n                errors.append(f\"第{line_num}行处理失败：{str(e)}\")\n                continue\n\n        # 提交数据库更改\n        try:\n            db.session.commit()\n        except Exception as e:\n            db.session.rollback()\n            return jsonify({'success': False, 'error': f'数据库保存失败: {str(e)}'}), 500\n\n        result = {\n            'success': True,\n            'processed': processed,\n            'success_count': success_count,\n            'error_count': error_count\n        }\n\n        if errors:\n            result['errors'] = errors[:10]  # 只返回前10个错误\n\n        return jsonify(result)\n\n    except Exception as e:\n        logger.error(f\"导入工单回复失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': f'导入失败: {str(e)}'}), 500\n\n# ==================== 媒体文件管理 API ====================\n\n@admin_bp.route('/api/articles/<int:article_id>/media', methods=['GET'])\n@login_required\ndef get_article_media(article_id):\n    \"\"\"获取文章的媒体文件列表\"\"\"\n    if not current_user.is_admin():\n        return jsonify({'success': False, 'error': '权限不足'}), 403\n\n    try:\n        # 验证文章存在\n        article = Article.query.get_or_404(article_id)\n\n        # 获取关联的媒体文件\n        media_files = MediaFile.query.filter_by(article_id=article_id).all()\n\n        # 按类型分组\n        audio_files = []\n        image_files = []\n\n        for media in media_files:\n            file_data = {\n                'id': media.id,\n                'filename': media.filename,\n                'original_filename': media.original_filename,\n                'file_type': media.file_type,\n                'file_category': media.file_category,\n                'file_size': media.file_size,\n                'url': media.url,\n                'created_at': media.created_at.isoformat() if media.created_at else None\n            }\n\n            if media.file_type == 'audio':\n                audio_files.append(file_data)\n            elif media.file_type == 'image':\n                image_files.append(file_data)\n\n        return jsonify({\n            'success': True,\n            'audio_files': audio_files,\n            'image_files': image_files\n        })\n\n    except Exception as e:\n        logger.error(f\"获取文章媒体文件失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/media', methods=['POST'])\n@login_required\ndef upload_article_media(article_id):\n    \"\"\"上传文章媒体文件\"\"\"\n    if not current_user.is_admin():\n        return jsonify({'success': False, 'error': '权限不足'}), 403\n\n    try:\n        # 验证文章存在\n        article = Article.query.get_or_404(article_id)\n\n        # 检查文件\n        if 'file' not in request.files:\n            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400\n\n        file = request.files['file']\n        if file.filename == '':\n            return jsonify({'success': False, 'error': '请选择要上传的文件'}), 400\n\n        file_type = request.form.get('file_type', 'image')\n        file_category = request.form.get('file_category', 'content')\n\n        # 验证文件类型\n        if file_type == 'audio':\n            allowed_extensions = {'.mp3', '.wav', '.m4a', '.aac', '.ogg'}\n        elif file_type == 'image':\n            allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}\n        else:\n            return jsonify({'success': False, 'error': '不支持的文件类型'}), 400\n\n        file_ext = os.path.splitext(file.filename)[1].lower()\n        if file_ext not in allowed_extensions:\n            return jsonify({'success': False, 'error': f'不支持的文件格式: {file_ext}'}), 400\n\n        # 生成唯一文件名\n        unique_filename = f\"{uuid.uuid4()}{file_ext}\"\n\n        # 这里应该上传到R2存储，暂时返回模拟数据\n        # TODO: 实现实际的R2上传逻辑\n        file_url = f\"https://your-r2-domain.com/{unique_filename}\"\n\n        # 创建媒体文件记录\n        media_file = MediaFile(\n            article_id=article_id,\n            filename=unique_filename,\n            original_filename=file.filename,\n            file_type=file_type,\n            file_category=file_category,\n            file_size=len(file.read()),\n            url=file_url,\n            r2_key=unique_filename,\n            created_at=datetime.utcnow()\n        )\n\n        db.session.add(media_file)\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '文件上传成功',\n            'file': {\n                'id': media_file.id,\n                'filename': media_file.filename,\n                'original_filename': media_file.original_filename,\n                'file_type': media_file.file_type,\n                'file_size': media_file.file_size,\n                'url': media_file.url\n            }\n        })\n\n    except Exception as e:\n        logger.error(f\"上传媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/media/<int:media_id>', methods=['DELETE'])\n@login_required\ndef delete_media_file(media_id):\n    \"\"\"删除媒体文件\"\"\"\n    if not current_user.is_admin():\n        return jsonify({'success': False, 'error': '权限不足'}), 403\n\n    try:\n        media_file = MediaFile.query.get_or_404(media_id)\n\n        # TODO: 从R2存储中删除实际文件\n        # delete_from_r2(media_file.r2_key)\n\n        db.session.delete(media_file)\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '文件删除成功'\n        })\n\n    except Exception as e:\n        logger.error(f\"删除媒体文件失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': f'删除失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/articles/<int:article_id>/audio-sync', methods=['PUT'])\n@login_required\ndef save_audio_sync_config(article_id):\n    \"\"\"保存音频同步配置\"\"\"\n    if not current_user.is_admin():\n        return jsonify({'success': False, 'error': '权限不足'}), 403\n\n    try:\n        article = Article.query.get_or_404(article_id)\n        data = request.get_json()\n\n        audio_sync_data = data.get('audio_sync_data')\n        has_audio_sync = data.get('has_audio_sync', False)\n\n        # 验证JSON格式\n        if audio_sync_data:\n            try:\n                import json\n                json.loads(audio_sync_data)\n            except json.JSONDecodeError:\n                return jsonify({'success': False, 'error': '音频同步数据格式错误'}), 400\n\n        # 更新文章\n        article.audio_sync_data = audio_sync_data\n        article.has_audio_sync = has_audio_sync\n        article.updated_at = datetime.utcnow()\n\n        db.session.commit()\n\n        return jsonify({\n            'success': True,\n            'message': '音频同步配置保存成功'\n        })\n\n    except Exception as e:\n        logger.error(f\"保存音频同步配置失败: {str(e)}\")\n        db.session.rollback()\n        return jsonify({'success': False, 'error': f'保存失败: {str(e)}'}), 500\n\n\n@admin_bp.route('/api/proxy-subtitle', methods=['GET'])\ndef proxy_subtitle():\n    \"\"\"代理获取字幕文件，解决跨域问题\"\"\"\n    try:\n        import requests\n        from flask import Response\n\n        # 获取要代理的字幕URL\n        subtitle_url = request.args.get('url')\n        if not subtitle_url:\n            return jsonify({'error': '缺少url参数'}), 400\n\n        logger.info(f\"代理获取字幕文件: {subtitle_url}\")\n\n        # 发起请求获取字幕文件\n        try:\n            response = requests.get(subtitle_url, timeout=10)\n\n            # 检查请求是否成功\n            if response.status_code != 200:\n                logger.warning(f\"无法获取字幕文件，状态码: {response.status_code}, URL: {subtitle_url}\")\n                return jsonify({'error': f'字幕文件不存在 (状态码: {response.status_code})'}), response.status_code\n\n            # 获取字幕内容\n            subtitle_content = response.text\n            logger.info(f\"成功获取字幕文件，大小: {len(subtitle_content)} 字符\")\n\n            # 创建响应并设置正确的内容类型\n            proxy_response = Response(subtitle_content, content_type='text/plain; charset=utf-8')\n\n            return proxy_response\n\n        except requests.Timeout:\n            logger.warning(f\"获取字幕文件超时: {subtitle_url}\")\n            return jsonify({'error': '字幕文件获取超时'}), 408\n\n        except requests.RequestException as e:\n            logger.error(f\"获取字幕文件失败: {str(e)}\")\n            return jsonify({'error': f'字幕文件获取失败: {str(e)}'}), 500\n\n    except Exception as e:\n        logger.error(f\"代理字幕文件失败: {str(e)}\")\n        return jsonify({'error': f'服务器错误: {str(e)}'}), 500\n\n\n\n\n# ==================== Vue3 管理后台认证API ====================\n\n@admin_bp.route('/api/auth/login', methods=['POST'])\ndef api_auth_login():\n    \"\"\"Vue3管理后台登录API\"\"\"\n    try:\n        from flask_login import login_user\n        from werkzeug.security import check_password_hash\n\n        data = request.get_json()\n        if not data or not data.get('username') or not data.get('password'):\n            return jsonify({'success': False, 'error': '用户名和密码不能为空'}), 400\n\n        username = data['username']\n        password = data['password']\n\n        # 查找用户\n        user = User.query.filter_by(username=username).first()\n        if not user:\n            return jsonify({'success': False, 'error': '用户名或密码错误'}), 401\n\n        # 验证密码\n        if not check_password_hash(user.password_hash, password):\n            return jsonify({'success': False, 'error': '用户名或密码错误'}), 401\n\n        # 检查用户权限\n        if not user.can_access_admin():\n            return jsonify({'success': False, 'error': '权限不足，无法访问管理后台'}), 403\n\n        # 登录用户\n        login_user(user, remember=True)\n\n        # 更新最后登录时间\n        user.last_login = datetime.now()\n        db.session.commit()\n\n        # 返回用户信息\n        user_data = {\n            'id': user.id,\n            'username': user.username,\n            'email': user.email,\n            'is_admin': user.is_admin(),\n            'is_staff': user.can_access_admin(),\n            'role': user.role,\n            'membership_type': user.membership_type,\n            'created_at': user.created_at.isoformat() if user.created_at else None\n        }\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'token': 'flask-session',  # 使用Flask会话\n                'user': user_data\n            },\n            'message': '登录成功'\n        })\n\n    except Exception as e:\n        logger.error(f\"Vue3登录失败: {str(e)}\")\n        return jsonify({'success': False, 'error': '登录失败，请稍后重试'}), 500\n\n@admin_bp.route('/api/auth/logout', methods=['POST'])\n@login_required\ndef api_auth_logout():\n    \"\"\"Vue3管理后台登出API\"\"\"\n    try:\n        from flask_login import logout_user\n        logout_user()\n        return jsonify({'success': True, 'message': '登出成功'})\n    except Exception as e:\n        logger.error(f\"Vue3登出失败: {str(e)}\")\n        return jsonify({'success': False, 'error': '登出失败'}), 500\n\n@admin_bp.route('/api/auth/me')\n@login_required\n@staff_required\ndef api_auth_me():\n    \"\"\"获取当前用户信息 - 用于Vue3应用\"\"\"\n    try:\n        user_data = {\n            'id': current_user.id,\n            'username': current_user.username,\n            'email': current_user.email,\n            'is_admin': current_user.is_admin(),  # 调用方法而不是属性\n            'is_staff': current_user.can_access_admin(),  # 使用can_access_admin方法\n            'role': current_user.role,\n            'membership_type': current_user.membership_type,\n            'created_at': current_user.created_at.isoformat() if current_user.created_at else None\n        }\n\n        return jsonify({\n            'success': True,\n            'data': user_data\n        })\n    except Exception as e:\n        logger.error(f\"获取当前用户信息失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取用户信息失败: {str(e)}'}), 500\n\n# ==================== Vue3 管理后台 Dashboard API 路由 ====================\n\n@admin_bp.route('/api/admin/dashboard/stats')\n@login_required\n@staff_required\ndef api_dashboard_stats():\n    \"\"\"获取仪表盘统计数据\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 用户统计\n        total_users = User.query.count()\n        new_users_today = User.query.filter(\n            User.created_at >= datetime.now().date()\n        ).count()\n\n        # 文章统计\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter_by(status='published').count()\n\n        # 订单统计\n        total_purchases = Purchase.query.count() if Purchase else 0\n\n        # VIP用户统计\n        vip_users = User.query.filter(User.membership_type != 'free').count()\n\n        stats = {\n            'user_count': total_users,\n            'new_users_today': new_users_today,\n            'article_count': total_articles,\n            'published_articles': published_articles,\n            'purchase_count': total_purchases,\n            'vip_users': vip_users\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取仪表盘统计数据失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取统计数据失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/admin/dashboard/recent-users')\n@login_required\n@staff_required\ndef api_dashboard_recent_users():\n    \"\"\"获取最近注册用户\"\"\"\n    try:\n        limit = request.args.get('limit', 5, type=int)\n        recent_users = User.query.order_by(User.created_at.desc()).limit(limit).all()\n\n        users_data = []\n        for user in recent_users:\n            users_data.append({\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'created_at': user.created_at.isoformat() if user.created_at else None,\n                'is_active': getattr(user, 'is_active', True),\n                'membership_type': getattr(user, 'membership_type', 'free')\n            })\n\n        return jsonify({\n            'success': True,\n            'data': users_data\n        })\n    except Exception as e:\n        logger.error(f\"获取最近用户失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取最近用户失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/admin/dashboard/recent-articles')\n@login_required\n@staff_required\ndef api_dashboard_recent_articles():\n    \"\"\"获取最近发布文章\"\"\"\n    try:\n        limit = request.args.get('limit', 5, type=int)\n        recent_articles = Article.query.filter_by(status='published').order_by(\n            Article.created_at.desc()\n        ).limit(limit).all()\n\n        articles_data = []\n        for article in recent_articles:\n            articles_data.append({\n                'id': article.id,\n                'title': article.title,\n                'created_at': article.created_at.isoformat() if article.created_at else None,\n                'author_name': article.author.username if article.author else '未知',\n                'view_count': getattr(article, 'view_count', 0),\n                'status': article.status\n            })\n\n        return jsonify({\n            'success': True,\n            'data': articles_data\n        })\n    except Exception as e:\n        logger.error(f\"获取最近文章失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取最近文章失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/admin/dashboard')\n@login_required\n@staff_required\ndef api_dashboard_all():\n    \"\"\"获取完整仪表盘数据\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 获取统计数据\n        total_users = User.query.count()\n        new_users_today = User.query.filter(\n            User.created_at >= datetime.now().date()\n        ).count()\n\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter_by(status='published').count()\n        total_purchases = Purchase.query.count() if Purchase else 0\n        vip_users = User.query.filter(User.membership_type != 'free').count()\n\n        # 获取最近用户\n        recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()\n        users_data = []\n        for user in recent_users:\n            users_data.append({\n                'id': user.id,\n                'username': user.username,\n                'email': user.email,\n                'created_at': user.created_at.isoformat() if user.created_at else None,\n                'is_active': getattr(user, 'is_active', True),\n                'membership_type': getattr(user, 'membership_type', 'free')\n            })\n\n        # 获取最近文章\n        recent_articles = Article.query.filter_by(status='published').order_by(\n            Article.created_at.desc()\n        ).limit(5).all()\n        articles_data = []\n        for article in recent_articles:\n            articles_data.append({\n                'id': article.id,\n                'title': article.title,\n                'created_at': article.created_at.isoformat() if article.created_at else None,\n                'author_name': article.author.username if article.author else '未知',\n                'view_count': getattr(article, 'view_count', 0),\n                'status': article.status\n            })\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'stats': {\n                    'user_count': total_users,\n                    'new_users_today': new_users_today,\n                    'article_count': total_articles,\n                    'published_articles': published_articles,\n                    'purchase_count': total_purchases,\n                    'vip_users': vip_users\n                },\n                'recent_users': users_data,\n                'recent_articles': articles_data\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取完整仪表盘数据失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取仪表盘数据失败: {str(e)}'}), 500\n\n# ==================== Vue3 管理后台财务API ====================\n\n@admin_bp.route('/api/finance/stats')\n@login_required\n@staff_required\ndef api_finance_stats():\n    \"\"\"获取财务统计数据\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 计算总收入\n        total_revenue = Purchase.query.with_entities(\n            func.sum(Purchase.amount)\n        ).scalar() or 0\n\n        # 计算今日收入\n        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)\n        today_revenue = Purchase.query.filter(\n            Purchase.purchase_date >= today_start\n        ).with_entities(func.sum(Purchase.amount)).scalar() or 0\n\n        # VIP用户数\n        vip_users = UserFinance.query.filter(\n            UserFinance.vip_level > 0,\n            UserFinance.vip_expire_at > datetime.now()\n        ).count()\n\n        # 今日订单数\n        today_orders = Purchase.query.filter(\n            Purchase.purchase_date >= today_start\n        ).count()\n\n        # 待处理异常数（模拟数据）\n        pending_anomalies = 3\n\n        stats = {\n            'total_revenue': total_revenue,\n            'today_revenue': today_revenue,\n            'vip_users': vip_users,\n            'today_orders': today_orders,\n            'pending_anomalies': pending_anomalies\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取财务统计数据失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取财务统计失败: {str(e)}'}), 500\n\n@admin_bp.route('/api/finance/orders')\n@login_required\n@staff_required\ndef api_finance_orders():\n    \"\"\"获取订单列表\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        search = request.args.get('search', '')\n\n        query = Purchase.query.join(User)\n\n        if search:\n            query = query.filter(\n                or_(\n                    Purchase.id.like(f'%{search}%'),\n                    User.username.like(f'%{search}%')\n                )\n            )\n\n        pagination = query.order_by(Purchase.purchase_date.desc()).paginate(\n            page=page, per_page=per_page, error_out=False\n        )\n\n        orders_data = []\n        for purchase in pagination.items:\n            orders_data.append({\n                'id': purchase.id,\n                'user_id': purchase.user_id,\n                'username': purchase.user.username if purchase.user else '未知',\n                'product_type': purchase.product_type or 'VIP',\n                'amount': purchase.amount,\n                'payment_method': purchase.payment_method,\n                'status': 'success',  # Purchase表中的都是成功的\n                'created_at': purchase.purchase_date.isoformat() if purchase.purchase_date else None\n            })\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'data': orders_data,\n                'pagination': {\n                    'page': pagination.page,\n                    'pages': pagination.pages,\n                    'per_page': pagination.per_page,\n                    'total': pagination.total,\n                    'has_prev': pagination.has_prev,\n                    'has_next': pagination.has_next\n                }\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取订单列表失败: {str(e)}\")\n        return jsonify({'success': False, 'error': f'获取订单列表失败: {str(e)}'}), 500\n\n# ==================== 系统设置API ====================\n\n@admin_bp.route('/api/system/settings', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_system_settings():\n    \"\"\"获取系统设置API\"\"\"\n    try:\n        # 从数据库或配置文件获取系统设置\n        settings = {\n            # 基础设置\n            'site_name': app.config.get('SITE_NAME', '塔罗嗅嗅'),\n            'site_description': app.config.get('SITE_DESCRIPTION', '专业的塔罗牌占卜平台'),\n            'admin_email': app.config.get('ADMIN_EMAIL', '<EMAIL>'),\n            'contact_phone': app.config.get('CONTACT_PHONE', '************'),\n\n            # 用户设置\n            'allow_registration': app.config.get('ALLOW_REGISTRATION', True),\n            'require_email_verification': app.config.get('REQUIRE_EMAIL_VERIFICATION', True),\n            'default_user_quota': app.config.get('DEFAULT_USER_QUOTA', 100),\n            'max_login_attempts': app.config.get('MAX_LOGIN_ATTEMPTS', 5),\n\n            # 内容设置\n            'articles_per_page': app.config.get('ARTICLES_PER_PAGE', 20),\n            'auto_approve_articles': app.config.get('AUTO_APPROVE_ARTICLES', False),\n            'max_upload_size': app.config.get('MAX_UPLOAD_SIZE', 10),\n            'allowed_file_types': app.config.get('ALLOWED_FILE_TYPES', 'jpg,png,gif,pdf,doc,docx'),\n\n            # 安全设置\n            'enable_csrf_protection': app.config.get('WTF_CSRF_ENABLED', True),\n            'session_timeout': app.config.get('PERMANENT_SESSION_LIFETIME', 30),\n            'enable_rate_limiting': app.config.get('RATELIMIT_ENABLED', True),\n            'backup_frequency': app.config.get('BACKUP_FREQUENCY', 'daily'),\n\n            # 邮件设置\n            'smtp_server': app.config.get('MAIL_SERVER', 'smtp.gmail.com'),\n            'smtp_port': app.config.get('MAIL_PORT', 587),\n            'smtp_username': app.config.get('MAIL_USERNAME', ''),\n            'smtp_use_tls': app.config.get('MAIL_USE_TLS', True)\n        }\n\n        return jsonify({\n            'success': True,\n            'data': settings\n        })\n    except Exception as e:\n        logger.error(f\"获取系统设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/system/settings', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_system_settings():\n    \"\"\"更新系统设置API\"\"\"\n    try:\n        data = request.get_json()\n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n\n        # 这里应该将设置保存到数据库或配置文件\n        # 为了演示，我们只是记录日志\n        logger.info(f\"管理员 {current_user.username} 更新了系统设置\")\n\n        # 可以在这里添加设置验证逻辑\n        # 例如验证邮箱格式、端口号范围等\n\n        return jsonify({\n            'success': True,\n            'message': '系统设置更新成功'\n        })\n    except Exception as e:\n        logger.error(f\"更新系统设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# ==================== 价格设置API ====================\n\n@admin_bp.route('/api/system/prices', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_price_settings():\n    \"\"\"获取价格设置API\"\"\"\n    try:\n        # 从数据库或配置获取价格设置\n        prices = {\n            # 内容价格\n            'basic_content_price': 9.90,\n            'premium_content_price': 19.90,\n            'vip_content_price': 39.90,\n            'consultation_price': 99.00,\n\n            # 会员价格\n            'monthly_vip_price': 29.90,\n            'quarterly_vip_price': 79.90,\n            'yearly_vip_price': 299.90,\n\n            # 积分价格\n            'points_package_small': 9.90,\n            'points_small_amount': 100,\n            'points_package_large': 49.90,\n            'points_large_amount': 600,\n\n            # 折扣设置\n            'vip_discount': 0.85,\n            'bulk_discount': 0.90\n        }\n\n        return jsonify({\n            'success': True,\n            'data': prices\n        })\n    except Exception as e:\n        logger.error(f\"获取价格设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/system/prices', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_price_settings():\n    \"\"\"更新价格设置API\"\"\"\n    try:\n        data = request.get_json()\n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n\n        # 验证价格数据\n        for key, value in data.items():\n            if 'price' in key or 'discount' in key:\n                try:\n                    float(value)\n                except (ValueError, TypeError):\n                    return jsonify({'success': False, 'error': f'无效的价格值: {key}'}), 400\n\n        logger.info(f\"管理员 {current_user.username} 更新了价格设置\")\n\n        return jsonify({\n            'success': True,\n            'message': '价格设置更新成功'\n        })\n    except Exception as e:\n        logger.error(f\"更新价格设置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# ==================== 上传配置API ====================\n\n@admin_bp.route('/api/uploader/config', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_uploader_config():\n    \"\"\"获取上传配置API\"\"\"\n    try:\n        config = {\n            'website_url': app.config.get('UPLOADER_WEBSITE_URL', 'http://0.0.0.0:5000'),\n            'username': app.config.get('UPLOADER_USERNAME', 'admin'),\n            'password': '',  # 出于安全考虑，不返回密码\n            'upload_path': app.config.get('UPLOADER_PATH', '/uploads'),\n            'batch_size': app.config.get('UPLOADER_BATCH_SIZE', 10),\n            'max_retries': app.config.get('UPLOADER_MAX_RETRIES', 3),\n            'timeout': app.config.get('UPLOADER_TIMEOUT', 30),\n            'concurrent_uploads': app.config.get('UPLOADER_CONCURRENT', 3),\n            'auto_create_tags': app.config.get('UPLOADER_AUTO_TAGS', True),\n            'auto_publish': app.config.get('UPLOADER_AUTO_PUBLISH', False),\n            'backup_files': app.config.get('UPLOADER_BACKUP', True),\n            'delete_after_upload': app.config.get('UPLOADER_DELETE_AFTER', False)\n        }\n\n        return jsonify({\n            'success': True,\n            'data': config\n        })\n    except Exception as e:\n        logger.error(f\"获取上传配置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/config', methods=['PUT'])\n@login_required\n@admin_required\ndef api_update_uploader_config():\n    \"\"\"更新上传配置API\"\"\"\n    try:\n        data = request.get_json()\n        if not data:\n            return jsonify({'success': False, 'error': '无效的请求数据'}), 400\n\n        # 验证配置数据\n        if 'website_url' in data and not data['website_url'].startswith(('http://', 'https://')):\n            return jsonify({'success': False, 'error': '无效的网站地址'}), 400\n\n        logger.info(f\"管理员 {current_user.username} 更新了上传配置\")\n\n        return jsonify({\n            'success': True,\n            'message': '上传配置更新成功'\n        })\n    except Exception as e:\n        logger.error(f\"更新上传配置失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/test-connection', methods=['POST'])\n@login_required\n@admin_required\ndef api_test_uploader_connection():\n    \"\"\"测试上传连接API\"\"\"\n    try:\n        data = request.get_json()\n        website_url = data.get('website_url', '')\n        username = data.get('username', '')\n        password = data.get('password', '')\n\n        # 这里应该实现实际的连接测试逻辑\n        # 例如发送HTTP请求到目标服务器\n\n        # 模拟连接测试\n        import time\n        time.sleep(1)  # 模拟网络延迟\n\n        return jsonify({\n            'success': True,\n            'message': '连接测试成功'\n        })\n    except Exception as e:\n        logger.error(f\"连接测试失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/upload', methods=['POST'])\n@login_required\n@admin_required\ndef api_manual_upload():\n    \"\"\"手动上传文件API\"\"\"\n    try:\n        if 'files' not in request.files:\n            return jsonify({'success': False, 'error': '没有选择文件'}), 400\n\n        files = request.files.getlist('files')\n        if not files or files[0].filename == '':\n            return jsonify({'success': False, 'error': '没有选择文件'}), 400\n\n        upload_results = []\n        for file in files:\n            if file and file.filename:\n                # 这里应该实现实际的文件上传逻辑\n                # 例如保存到本地或云存储\n\n                upload_results.append({\n                    'filename': file.filename,\n                    'status': 'success',\n                    'message': '上传成功'\n                })\n\n        logger.info(f\"管理员 {current_user.username} 手动上传了 {len(files)} 个文件\")\n\n        return jsonify({\n            'success': True,\n            'data': upload_results,\n            'message': f'成功上传 {len(upload_results)} 个文件'\n        })\n    except Exception as e:\n        logger.error(f\"文件上传失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# ==================== 上传日志API ====================\n\n@admin_bp.route('/api/uploader/logs', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_upload_logs():\n    \"\"\"获取上传日志API\"\"\"\n    try:\n        page = request.args.get('page', 1, type=int)\n        per_page = request.args.get('per_page', 20, type=int)\n        date_from = request.args.get('date_from', '')\n        date_to = request.args.get('date_to', '')\n        status = request.args.get('status', '')\n\n        # 模拟上传日志数据\n        logs = [\n            {\n                'id': 1,\n                'filename': 'tarot_reading_guide.pdf',\n                'file_size': 2048576,\n                'status': 'success',\n                'upload_time': '2025-08-03 14:30:25',\n                'duration': 2.5,\n                'error_message': None\n            },\n            {\n                'id': 2,\n                'filename': 'card_meanings.docx',\n                'file_size': 1024000,\n                'status': 'failed',\n                'upload_time': '2025-08-03 14:25:10',\n                'duration': None,\n                'error_message': '文件格式不支持'\n            }\n        ]\n\n        # 统计数据\n        stats = {\n            'success_count': 156,\n            'failed_count': 12,\n            'total_size': 2048576000,\n            'today_count': 23\n        }\n\n        return jsonify({\n            'success': True,\n            'data': {\n                'logs': logs,\n                'stats': stats,\n                'pagination': {\n                    'page': page,\n                    'pages': 1,\n                    'per_page': per_page,\n                    'total': len(logs)\n                }\n            }\n        })\n    except Exception as e:\n        logger.error(f\"获取上传日志失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/logs/stats', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_upload_logs_stats():\n    \"\"\"获取上传日志统计信息API\"\"\"\n    try:\n        from datetime import datetime, timedelta\n\n        # 模拟上传日志统计数据（实际应该从上传日志表获取）\n        total_uploads = 1234\n        successful_uploads = 1156\n        failed_uploads = 78\n\n        # 时间统计\n        today = datetime.now().date()\n        week_ago = today - timedelta(days=7)\n\n        today_uploads = 23\n        week_uploads = 156\n\n        # 文件大小统计\n        total_file_size = 2048576000  # 2GB\n        avg_file_size = total_file_size // total_uploads if total_uploads > 0 else 0\n\n        # 文件类型分布\n        file_type_distribution = {\n            'pdf': 456,\n            'docx': 234,\n            'txt': 123,\n            'md': 89\n        }\n\n        # 上传趋势（最近7天）\n        upload_trend = []\n        for i in range(7):\n            date = today - timedelta(days=i)\n            # 模拟数据\n            count = 20 + (hash(str(date)) % 10)\n            success = count - (hash(str(date)) % 3)\n            failed = count - success\n\n            upload_trend.append({\n                'date': date.strftime('%Y-%m-%d'),\n                'count': count,\n                'success': success,\n                'failed': failed\n            })\n        upload_trend.reverse()\n\n        stats = {\n            'total_uploads': total_uploads,\n            'successful_uploads': successful_uploads,\n            'failed_uploads': failed_uploads,\n            'today_uploads': today_uploads,\n            'this_week_uploads': week_uploads,\n            'total_file_size': total_file_size,\n            'avg_file_size': avg_file_size,\n            'file_type_distribution': file_type_distribution,\n            'upload_trend': upload_trend\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取上传日志统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/logs', methods=['DELETE'])\n@login_required\n@admin_required\ndef api_clear_upload_logs():\n    \"\"\"清空上传日志API\"\"\"\n    try:\n        # 这里应该实现清空日志的逻辑\n        logger.info(f\"管理员 {current_user.username} 清空了上传日志\")\n\n        return jsonify({\n            'success': True,\n            'message': '上传日志已清空'\n        })\n    except Exception as e:\n        logger.error(f\"清空上传日志失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/uploader/logs/<int:log_id>', methods=['DELETE'])\n@login_required\n@admin_required\ndef api_delete_upload_log(log_id):\n    \"\"\"删除单个上传日志API\"\"\"\n    try:\n        # 这里应该实现删除单个日志的逻辑\n        logger.info(f\"管理员 {current_user.username} 删除了上传日志 {log_id}\")\n\n        return jsonify({\n            'success': True,\n            'message': '上传日志已删除'\n        })\n    except Exception as e:\n        logger.error(f\"删除上传日志失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n# ==================== 数据统计API ====================\n\n@admin_bp.route('/api/statistics/overview', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_statistics_overview():\n    \"\"\"获取统计概览API\"\"\"\n    try:\n        date_from = request.args.get('date_from', '')\n        date_to = request.args.get('date_to', '')\n        period = request.args.get('period', 'month')\n\n        # 从数据库获取实际统计数据\n        from models import User, Article, Order\n\n        # 用户统计\n        total_users = User.query.count()\n        active_users = User.query.filter(User.last_login.isnot(None)).count()\n        vip_users = User.query.filter(User.is_vip == True).count()\n\n        # 文章统计\n        total_articles = Article.query.count()\n        published_articles = Article.query.filter(Article.status == 'published').count()\n        draft_articles = Article.query.filter(Article.status == 'draft').count()\n\n        # 今日新增统计\n        from datetime import datetime, timedelta\n        today = datetime.now().date()\n        new_users_today = User.query.filter(User.created_at >= today).count()\n        new_articles_today = Article.query.filter(Article.created_at >= today).count()\n\n        # 收入统计（如果有订单表）\n        total_revenue = 234567  # 这里应该从订单表计算\n        today_revenue = 1234\n        month_revenue = 45678\n\n        stats = {\n            # 用户统计\n            'total_users': total_users,\n            'active_users': active_users,\n            'vip_users': vip_users,\n            'new_users_today': new_users_today,\n            'new_users_week': 312,  # 这里应该计算本周新增\n            'user_retention': 78.5,\n\n            # 内容统计\n            'total_articles': total_articles,\n            'published_articles': published_articles,\n            'draft_articles': draft_articles,\n            'new_articles_today': new_articles_today,\n            'total_views': 456789,  # 这里应该从阅读记录计算\n\n            # 财务统计\n            'total_revenue': total_revenue,\n            'vip_revenue': 156789,\n            'content_revenue': 77778,\n            'today_revenue': today_revenue,\n            'month_revenue': month_revenue,\n            'avg_order_value': 89.5,\n\n            # 系统统计\n            'total_visits': 789456,\n            'unique_visitors': 234567,\n            'page_views': 1234567,\n            'avg_session_duration': 12.5,\n            'bounce_rate': 35.2,\n            'avg_response_time': 245\n        }\n\n        return jsonify({\n            'success': True,\n            'data': stats\n        })\n    except Exception as e:\n        logger.error(f\"获取统计概览失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/statistics/users', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_user_statistics():\n    \"\"\"获取用户统计API\"\"\"\n    try:\n        # 这里应该实现详细的用户统计逻辑\n        user_stats = {\n            'registration_trend': [],  # 注册趋势数据\n            'activity_distribution': [],  # 活跃度分布\n            'retention_analysis': []  # 留存分析\n        }\n\n        return jsonify({\n            'success': True,\n            'data': user_stats\n        })\n    except Exception as e:\n        logger.error(f\"获取用户统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/statistics/content', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_content_statistics():\n    \"\"\"获取内容统计API\"\"\"\n    try:\n        # 这里应该实现详细的内容统计逻辑\n        content_stats = {\n            'publish_trend': [],  # 发布趋势\n            'category_distribution': [],  # 分类分布\n            'popular_articles': []  # 热门文章\n        }\n\n        return jsonify({\n            'success': True,\n            'data': content_stats\n        })\n    except Exception as e:\n        logger.error(f\"获取内容统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n\n@admin_bp.route('/api/statistics/revenue', methods=['GET'])\n@login_required\n@admin_required\ndef api_get_revenue_statistics():\n    \"\"\"获取收入统计API\"\"\"\n    try:\n        # 这里应该实现详细的收入统计逻辑\n        revenue_stats = {\n            'revenue_trend': [],  # 收入趋势\n            'payment_methods': [],  # 支付方式分布\n            'product_revenue': []  # 产品收入分析\n        }\n\n        return jsonify({\n            'success': True,\n            'data': revenue_stats\n        })\n    except Exception as e:\n        logger.error(f\"获取收入统计失败: {str(e)}\")\n        return jsonify({'success': False, 'error': str(e)}), 500\n"}