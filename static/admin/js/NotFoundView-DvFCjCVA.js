var h=Object.defineProperty,V=Object.defineProperties;var B=Object.getOwnPropertyDescriptors;var y=Object.getOwnPropertySymbols;var E=Object.prototype.hasOwnProperty,I=Object.prototype.propertyIsEnumerable;var v=(o,t,e)=>t in o?h(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,b=(o,t)=>{for(var e in t||(t={}))E.call(t,e)&&v(o,e,t[e]);if(y)for(var e of y(t))I.call(t,e)&&v(o,e,t[e]);return o},g=(o,t)=>V(o,B(t));import{P as S,aL as w,aM as R,aN as T,aO as M,d as m,a5 as P,j,c,o as r,a as _,e as p,ab as d,ac as D,a0 as n,n as l,ad as F,t as k,a2 as z,aj as H,f as C,w as $,g as L,y as O}from"./index-Cr1r-Y_5.js";import{E as q}from"./index-DBjzU7Hi.js";import{_ as x}from"./_plugin-vue_export-helper-DlAUqK2U.js";const a={primary:"icon-primary",success:"icon-success",warning:"icon-warning",error:"icon-error",info:"icon-info"},N={[a.primary]:w,[a.success]:M,[a.warning]:T,[a.error]:R,[a.info]:w},A=S({title:{type:String,default:""},subTitle:{type:String,default:""},icon:{type:String,values:["primary","success","warning","info","error"],default:"info"}}),G=m({name:"ElResult"}),J=m(g(b({},G),{props:A,setup(o){const t=o,e=P("result"),u=j(()=>{const s=t.icon,i=s&&a[s]?a[s]:"icon-info",f=N[i]||N["icon-info"];return{class:i,component:f}});return(s,i)=>(r(),c("div",{class:l(n(e).b())},[_("div",{class:l(n(e).e("icon"))},[d(s.$slots,"icon",{},()=>[n(u).component?(r(),D(F(n(u).component),{key:0,class:l(n(u).class)},null,8,["class"])):p("v-if",!0)])],2),s.title||s.$slots.title?(r(),c("div",{key:0,class:l(n(e).e("title"))},[d(s.$slots,"title",{},()=>[_("p",null,k(s.title),1)])],2)):p("v-if",!0),s.subTitle||s.$slots["sub-title"]?(r(),c("div",{key:1,class:l(n(e).e("subtitle"))},[d(s.$slots,"sub-title",{},()=>[_("p",null,k(s.subTitle),1)])],2)):p("v-if",!0),s.$slots.extra?(r(),c("div",{key:2,class:l(n(e).e("extra"))},[d(s.$slots,"extra")],2)):p("v-if",!0)],2))}}));var K=z(J,[["__file","result.vue"]]);const Q=H(K),U={class:"not-found"},W=m({__name:"NotFoundView",setup(o){const t=O(),e=()=>{t.push("/dashboard")};return(u,s)=>{const i=q,f=Q;return r(),c("div",U,[C(f,{icon:"warning",title:"404","sub-title":"抱歉，您访问的页面不存在"},{extra:$(()=>[C(i,{type:"primary",onClick:e},{default:$(()=>s[0]||(s[0]=[L(" 返回首页 ",-1)])),_:1,__:[0]})]),_:1})])}}}),se=x(W,[["__scopeId","data-v-fdd41ea7"]]);export{se as default};
