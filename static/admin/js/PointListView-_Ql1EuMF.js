import{d as k,i as f,m as c,k as h,c as d,a as t,g as a,t as l,p as i,v as u,s as V,b as C,e as D,F as M,l as T,n as b,o as p}from"./index-Cr1r-Y_5.js";const F={class:"points-management"},N={class:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6"},S={class:"admin-card p-4 bg-gradient-to-br from-blue-900 to-indigo-900 border border-blue-700"},U={class:"flex items-center justify-between"},B={class:"text-2xl font-bold text-white"},L={class:"admin-card p-4 bg-gradient-to-br from-green-900 to-emerald-900 border border-green-700"},P={class:"flex items-center justify-between"},z={class:"text-2xl font-bold text-white"},A={class:"admin-card p-4 bg-gradient-to-br from-yellow-900 to-amber-900 border border-yellow-700"},E={class:"flex items-center justify-between"},I={class:"text-2xl font-bold text-white"},q={class:"admin-card p-4 bg-gradient-to-br from-purple-900 to-indigo-900 border border-purple-700"},G={class:"flex items-center justify-between"},H={class:"text-2xl font-bold text-white"},J={class:"admin-card p-4 mb-6"},K={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},O={class:"admin-card p-4 mb-6"},Q={class:"overflow-x-auto"},R={class:"table-admin w-full"},W={id:"transactions-table-body"},X={class:"px-4 py-3 border-t border-gray-700"},Y={class:"px-4 py-3 border-t border-gray-700"},Z={class:"px-4 py-3 border-t border-gray-700"},$={class:"px-4 py-3 border-t border-gray-700"},tt={class:"px-4 py-3 border-t border-gray-700 max-w-xs truncate"},et={class:"px-4 py-3 border-t border-gray-700 text-sm text-gray-400"},st={key:0},rt=k({__name:"PointListView",setup(lt){const y=()=>{console.log("显示调整积分")},m=()=>{console.log("刷新数据")},g=r=>new Date(r).toLocaleDateString(),v=r=>({earn:"px-2 py-1 rounded-full text-xs bg-green-900 text-green-300",spend:"px-2 py-1 rounded-full text-xs bg-red-900 text-red-300",admin_adjust:"px-2 py-1 rounded-full text-xs bg-blue-900 text-blue-300",referral:"px-2 py-1 rounded-full text-xs bg-purple-900 text-purple-300",daily_bonus:"px-2 py-1 rounded-full text-xs bg-yellow-900 text-yellow-300"})[r]||"px-2 py-1 rounded-full text-xs bg-gray-700 text-gray-300",_=r=>({earn:"获得积分",spend:"消费积分",admin_adjust:"管理员调整",referral:"推荐奖励",daily_bonus:"每日奖励"})[r]||r,w=()=>{console.log("应用筛选器")},j=()=>{console.log("清除筛选器")};f(!1);const x=f([]),n=c({total_points_issued:125680,total_points_consumed:89420,today_transactions:156,active_users:1234}),o=c({user:"",type:"",date_from:"",date_to:""});return h(()=>{console.log("积分管理页面已加载")}),(r,e)=>(p(),d("div",F,[t("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[e[6]||(e[6]=t("h1",{class:"text-2xl font-bold text-white"},"积分管理",-1)),t("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[t("button",{onClick:y,class:"admin-btn-primary px-4 py-2 rounded-lg text-center"},e[4]||(e[4]=[t("i",{class:"fas fa-plus-minus mr-2"},null,-1),a("调整积分 ",-1)])),t("button",{onClick:m,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},e[5]||(e[5]=[t("i",{class:"fas fa-sync-alt mr-2"},null,-1),a("刷新列表 ",-1)]))])]),t("div",N,[t("div",S,[t("div",U,[t("div",null,[e[7]||(e[7]=t("h2",{class:"text-gray-300 mb-1"},"总积分发放",-1)),t("p",B,l(n.total_points_issued||0),1)]),e[8]||(e[8]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-blue-800 text-blue-200"},[t("i",{class:"fas fa-coins text-xl"})],-1))])]),t("div",L,[t("div",P,[t("div",null,[e[9]||(e[9]=t("h2",{class:"text-gray-300 mb-1"},"总积分消费",-1)),t("p",z,l(n.total_points_consumed||0),1)]),e[10]||(e[10]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-green-800 text-green-200"},[t("i",{class:"fas fa-shopping-cart text-xl"})],-1))])]),t("div",A,[t("div",E,[t("div",null,[e[11]||(e[11]=t("h2",{class:"text-gray-300 mb-1"},"有积分用户",-1)),t("p",I,l(n.active_users||0),1)]),e[12]||(e[12]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-yellow-800 text-yellow-200"},[t("i",{class:"fas fa-users text-xl"})],-1))])]),t("div",q,[t("div",G,[t("div",null,[e[13]||(e[13]=t("h2",{class:"text-gray-300 mb-1"},"今日交易",-1)),t("p",H,l(n.today_transactions||0),1)]),e[14]||(e[14]=t("div",{class:"flex items-center justify-center w-12 h-12 rounded-full bg-purple-800 text-purple-200"},[t("i",{class:"fas fa-calendar-day text-xl"})],-1))])])]),t("div",J,[t("div",K,[t("div",null,[e[15]||(e[15]=t("label",{for:"filter-user",class:"block mb-2 text-gray-300"},"用户搜索",-1)),i(t("input",{"onUpdate:modelValue":e[0]||(e[0]=s=>o.user=s),type:"text",id:"filter-user",class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"用户名或邮箱"},null,512),[[u,o.user]])]),t("div",null,[e[17]||(e[17]=t("label",{for:"filter-type",class:"block mb-2 text-gray-300"},"交易类型",-1)),i(t("select",{"onUpdate:modelValue":e[1]||(e[1]=s=>o.type=s),id:"filter-type",class:"form-input w-full px-3 py-2 rounded-lg"},e[16]||(e[16]=[C('<option value="">全部类型</option><option value="earn">获得积分</option><option value="spend">消费积分</option><option value="admin_adjust">管理员调整</option><option value="referral">推荐奖励</option><option value="daily_bonus">每日奖励</option>',6)]),512),[[V,o.type]])]),t("div",null,[e[18]||(e[18]=t("label",{for:"filter-date-from",class:"block mb-2 text-gray-300"},"开始日期",-1)),i(t("input",{"onUpdate:modelValue":e[2]||(e[2]=s=>o.date_from=s),type:"date",id:"filter-date-from",class:"form-input w-full px-3 py-2 rounded-lg"},null,512),[[u,o.date_from]])]),t("div",null,[e[19]||(e[19]=t("label",{for:"filter-date-to",class:"block mb-2 text-gray-300"},"结束日期",-1)),i(t("input",{"onUpdate:modelValue":e[3]||(e[3]=s=>o.date_to=s),type:"date",id:"filter-date-to",class:"form-input w-full px-3 py-2 rounded-lg"},null,512),[[u,o.date_to]])])]),t("div",{class:"mt-4 flex space-x-3"},[t("button",{onClick:w,id:"apply-filter-btn",class:"admin-btn-primary px-4 py-2 rounded-lg"},e[20]||(e[20]=[t("i",{class:"fas fa-search mr-2"},null,-1),a("筛选 ",-1)])),t("button",{onClick:j,id:"clear-filter-btn",class:"admin-btn-secondary px-4 py-2 rounded-lg"},e[21]||(e[21]=[t("i",{class:"fas fa-times mr-2"},null,-1),a("清除 ",-1)]))])]),t("div",O,[t("div",Q,[t("table",R,[e[23]||(e[23]=t("thead",null,[t("tr",null,[t("th",{class:"px-4 py-2 text-left"},"ID"),t("th",{class:"px-4 py-2 text-left"},"用户"),t("th",{class:"px-4 py-2 text-left"},"积分变动"),t("th",{class:"px-4 py-2 text-left"},"交易类型"),t("th",{class:"px-4 py-2 text-left"},"描述"),t("th",{class:"px-4 py-2 text-left"},"时间")])],-1)),t("tbody",W,[(p(!0),d(M,null,T(x.value,s=>(p(),d("tr",{key:s.id,class:"hover:bg-gray-700/50 transition-colors"},[t("td",X,l(s.id),1),t("td",Y,l(s.username),1),t("td",Z,[t("span",{class:b([s.points>0?"text-green-400":"text-red-400","font-mono font-bold"])},l(s.points>0?"+":"")+l(s.points),3)]),t("td",$,[t("span",{class:b(v(s.transaction_type))},l(_(s.transaction_type)),3)]),t("td",tt,l(s.description),1),t("td",et,l(g(s.created_at)),1)]))),128)),x.value.length===0?(p(),d("tr",st,e[22]||(e[22]=[t("td",{colspan:"6",class:"px-4 py-8 text-center text-gray-400"},[t("i",{class:"fas fa-coins text-4xl mb-4"}),t("p",null,"暂无积分交易记录")],-1)]))):D("",!0)])])])])]))}});export{rt as default};
