var k=(V,b,n)=>new Promise((w,r)=>{var d=i=>{try{l(n.next(i))}catch(y){r(y)}},C=i=>{try{l(n.throw(i))}catch(y){r(y)}},l=i=>i.done?w(i.value):Promise.resolve(i.value).then(d,C);l((n=n.apply(V,b)).next())});import{d as N,i as v,m as D,k as P,c as f,a as t,e as F,g as u,t as x,F as M,l as z,B as A,p as o,v as p,x as _,E as g,n as B,o as c}from"./index-Bo6OtMFR.js";import{_ as G}from"./_plugin-vue_export-helper-DlAUqK2U.js";const K={class:"uploader-config"},L={class:"admin-card p-6 mb-6"},$={class:"upload-area border-2 border-dashed border-gray-600 rounded-lg p-8 text-center"},q={class:"text-gray-500 text-sm"},H={key:0,class:"mt-6"},J={class:"space-y-2"},O={class:"flex items-center"},Q={class:"text-gray-300"},R={class:"text-gray-500 ml-2"},W=["onClick"],X=["disabled"],Y={class:"admin-card p-6 mb-6"},Z={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},ee={class:"mt-6"},te={class:"space-y-3"},se={class:"flex items-center"},le={class:"flex items-center"},ae={class:"flex items-center"},oe={class:"flex items-center"},re={key:0,class:"admin-card p-6"},ne={class:"space-y-2"},ie={class:"flex items-center"},de={class:"text-gray-300"},ue=N({__name:"UploaderConfigView",setup(V){const b=v(!1),n=v(!1),w=v(),r=v([]),d=v([]),C=["txt","md","doc","docx","pdf"],l=D({website_url:"http://0.0.0.0:5000",username:"admin",password:"",upload_path:"/uploads",batch_size:10,max_retries:3,timeout:30,concurrent_uploads:3,auto_create_tags:!0,auto_publish:!1,backup_files:!0,delete_after_upload:!1}),i=()=>{var a;(a=w.value)==null||a.click()},y=a=>{const e=a.target;e.files&&(r.value=Array.from(e.files))},S=a=>{r.value.splice(a,1)},I=a=>{if(a===0)return"0 Bytes";const e=1024,s=["Bytes","KB","MB","GB"],m=Math.floor(Math.log(a)/Math.log(e));return parseFloat((a/Math.pow(e,m)).toFixed(2))+" "+s[m]},j=()=>k(this,null,function*(){if(r.value.length!==0){n.value=!0,d.value=[];try{for(const a of r.value){d.value.push({filename:a.name,status:"uploading",message:"上传中..."}),yield new Promise(s=>setTimeout(s,1e3));const e=d.value.findIndex(s=>s.filename===a.name);e!==-1&&(d.value[e]={filename:a.name,status:"success",message:"上传成功"})}g.success("所有文件上传完成"),r.value=[]}catch(a){console.error("上传失败:",a),g.error("上传失败")}finally{n.value=!1}}}),U=()=>k(this,null,function*(){b.value=!0;try{console.log("测试连接:",l.website_url),yield new Promise(a=>setTimeout(a,1e3)),g.success("连接测试成功")}catch(a){console.error("连接测试失败:",a),g.error("连接测试失败")}finally{b.value=!1}}),h=()=>k(this,null,function*(){b.value=!0;try{console.log("保存上传配置:",l),g.success("配置保存成功")}catch(a){console.error("保存配置失败:",a),g.error("保存配置失败")}finally{b.value=!1}}),T=a=>{switch(a){case"uploading":return"fas fa-spinner fa-spin text-blue-400";case"success":return"fas fa-check-circle text-green-400";case"error":return"fas fa-times-circle text-red-400";default:return"fas fa-clock text-gray-400"}},E=a=>{switch(a){case"uploading":return"text-blue-400";case"success":return"text-green-400";case"error":return"text-red-400";default:return"text-gray-400"}};return P(()=>{console.log("自动上传配置页面已加载")}),(a,e)=>(c(),f("div",K,[t("div",{class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},[e[14]||(e[14]=t("h1",{class:"text-2xl font-bold text-white"},"自动上传配置",-1)),t("div",{class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},[t("button",{onClick:U,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},e[12]||(e[12]=[t("i",{class:"fas fa-plug mr-2"},null,-1),u("测试连接 ",-1)])),t("button",{onClick:h,class:"admin-btn-primary px-4 py-2 rounded-lg"},e[13]||(e[13]=[t("i",{class:"fas fa-save mr-2"},null,-1),u("保存配置 ",-1)]))])]),t("div",L,[e[21]||(e[21]=t("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[t("i",{class:"fas fa-upload mr-2 text-blue-500"}),u("手动上传文件 ")],-1)),t("div",$,[t("input",{type:"file",ref_key:"fileInput",ref:w,multiple:"",onChange:y,class:"hidden",accept:".txt,.md,.doc,.docx,.pdf"},null,544),t("div",{onClick:i,class:"cursor-pointer"},[e[15]||(e[15]=t("i",{class:"fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"},null,-1)),e[16]||(e[16]=t("p",{class:"text-gray-300 mb-2"},"点击选择文件或拖拽文件到此处",-1)),t("p",q,"支持的文件类型: "+x(C.join(", ")),1)]),r.value.length>0?(c(),f("div",H,[e[20]||(e[20]=t("h4",{class:"text-white mb-3"},"已选择的文件:",-1)),t("div",J,[(c(!0),f(M,null,z(r.value,(s,m)=>(c(),f("div",{key:m,class:"flex items-center justify-between bg-gray-700 p-3 rounded"},[t("div",O,[e[17]||(e[17]=t("i",{class:"fas fa-file text-blue-400 mr-3"},null,-1)),t("span",Q,x(s.name),1),t("span",R,"("+x(I(s.size))+")",1)]),t("button",{onClick:pe=>S(m),class:"text-red-400 hover:text-red-300"},e[18]||(e[18]=[t("i",{class:"fas fa-times"},null,-1)]),8,W)]))),128))]),t("button",{onClick:j,disabled:n.value,class:"admin-btn-primary px-6 py-2 rounded-lg mt-4"},[e[19]||(e[19]=t("i",{class:"fas fa-upload mr-2"},null,-1)),u(" "+x(n.value?"上传中...":"开始上传"),1)],8,X)])):F("",!0)])]),t("div",Y,[e[37]||(e[37]=t("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[t("i",{class:"fas fa-cog mr-2 text-yellow-500"}),u("上传工具配置 ")],-1)),t("form",{onSubmit:A(h,["prevent"])},[t("div",Z,[t("div",null,[e[22]||(e[22]=t("label",{for:"website_url",class:"block mb-2 text-gray-300"},"网站地址",-1)),o(t("input",{type:"url",id:"website_url","onUpdate:modelValue":e[0]||(e[0]=s=>l.website_url=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"http://0.0.0.0:5000"},null,512),[[p,l.website_url]])]),t("div",null,[e[23]||(e[23]=t("label",{for:"username",class:"block mb-2 text-gray-300"},"用户名",-1)),o(t("input",{type:"text",id:"username","onUpdate:modelValue":e[1]||(e[1]=s=>l.username=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"admin"},null,512),[[p,l.username]])]),t("div",null,[e[24]||(e[24]=t("label",{for:"password",class:"block mb-2 text-gray-300"},"密码",-1)),o(t("input",{type:"password",id:"password","onUpdate:modelValue":e[2]||(e[2]=s=>l.password=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"••••••••"},null,512),[[p,l.password]])]),t("div",null,[e[25]||(e[25]=t("label",{for:"upload_path",class:"block mb-2 text-gray-300"},"上传路径",-1)),o(t("input",{type:"text",id:"upload_path","onUpdate:modelValue":e[3]||(e[3]=s=>l.upload_path=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"/uploads"},null,512),[[p,l.upload_path]])]),t("div",null,[e[26]||(e[26]=t("label",{for:"batch_size",class:"block mb-2 text-gray-300"},"批处理大小",-1)),o(t("input",{type:"number",id:"batch_size","onUpdate:modelValue":e[4]||(e[4]=s=>l.batch_size=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"10",min:"1"},null,512),[[p,l.batch_size]])]),t("div",null,[e[27]||(e[27]=t("label",{for:"max_retries",class:"block mb-2 text-gray-300"},"最大重试次数",-1)),o(t("input",{type:"number",id:"max_retries","onUpdate:modelValue":e[5]||(e[5]=s=>l.max_retries=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"3",min:"1"},null,512),[[p,l.max_retries]])]),t("div",null,[e[28]||(e[28]=t("label",{for:"timeout",class:"block mb-2 text-gray-300"},"超时时间 (秒)",-1)),o(t("input",{type:"number",id:"timeout","onUpdate:modelValue":e[6]||(e[6]=s=>l.timeout=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"30",min:"5"},null,512),[[p,l.timeout]])]),t("div",null,[e[29]||(e[29]=t("label",{for:"concurrent_uploads",class:"block mb-2 text-gray-300"},"并发上传数",-1)),o(t("input",{type:"number",id:"concurrent_uploads","onUpdate:modelValue":e[7]||(e[7]=s=>l.concurrent_uploads=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"3",min:"1",max:"10"},null,512),[[p,l.concurrent_uploads]])])]),t("div",ee,[e[34]||(e[34]=t("h4",{class:"text-white mb-3"},"功能选项",-1)),t("div",te,[t("div",se,[o(t("input",{type:"checkbox",id:"auto_create_tags","onUpdate:modelValue":e[8]||(e[8]=s=>l.auto_create_tags=s),class:"form-checkbox-admin mr-3"},null,512),[[_,l.auto_create_tags]]),e[30]||(e[30]=t("label",{for:"auto_create_tags",class:"text-gray-300"},"从文件名和内容中自动创建标签",-1))]),t("div",le,[o(t("input",{type:"checkbox",id:"auto_publish","onUpdate:modelValue":e[9]||(e[9]=s=>l.auto_publish=s),class:"form-checkbox-admin mr-3"},null,512),[[_,l.auto_publish]]),e[31]||(e[31]=t("label",{for:"auto_publish",class:"text-gray-300"},"上传后自动发布文章",-1))]),t("div",ae,[o(t("input",{type:"checkbox",id:"backup_files","onUpdate:modelValue":e[10]||(e[10]=s=>l.backup_files=s),class:"form-checkbox-admin mr-3"},null,512),[[_,l.backup_files]]),e[32]||(e[32]=t("label",{for:"backup_files",class:"text-gray-300"},"上传前备份原文件",-1))]),t("div",oe,[o(t("input",{type:"checkbox",id:"delete_after_upload","onUpdate:modelValue":e[11]||(e[11]=s=>l.delete_after_upload=s),class:"form-checkbox-admin mr-3"},null,512),[[_,l.delete_after_upload]]),e[33]||(e[33]=t("label",{for:"delete_after_upload",class:"text-gray-300"},"上传成功后删除本地文件",-1))])])]),t("div",{class:"flex justify-end space-x-4 mt-6"},[t("button",{type:"button",onClick:U,class:"admin-btn-secondary px-6 py-2 rounded-lg"},e[35]||(e[35]=[t("i",{class:"fas fa-plug mr-2"},null,-1),u("测试连接 ",-1)])),e[36]||(e[36]=t("button",{type:"submit",class:"admin-btn-primary px-6 py-2 rounded-lg"},[t("i",{class:"fas fa-save mr-2"}),u("保存配置 ")],-1))])],32)]),d.value.length>0?(c(),f("div",re,[e[38]||(e[38]=t("h3",{class:"text-lg font-semibold text-white mb-4 border-b border-gray-600 pb-2"},[t("i",{class:"fas fa-list mr-2 text-green-500"}),u("上传状态 ")],-1)),t("div",ne,[(c(!0),f(M,null,z(d.value,(s,m)=>(c(),f("div",{key:m,class:"flex items-center justify-between bg-gray-700 p-3 rounded"},[t("div",ie,[t("i",{class:B([T(s.status),"mr-3"])},null,2),t("span",de,x(s.filename),1)]),t("span",{class:B(E(s.status))},x(s.message),3)]))),128))])])):F("",!0)]))}}),be=G(ue,[["__scopeId","data-v-e595baf7"]]);export{be as default};
