var p=(A,$,b)=>new Promise((u,y)=>{var w=o=>{try{n(b.next(o))}catch(c){y(c)}},x=o=>{try{n(b.throw(o))}catch(c){y(c)}},n=o=>o.done?u(o.value):Promise.resolve(o.value).then(w,x);n((b=b.apply(A,$)).next())});import{d as ot,u as at,i as V,m as N,j as T,z as it,k as rt,c as i,a as e,t as f,e as _,g as m,p as v,v as k,s as F,F as h,l as C,b as nt,h as g,y as dt,x as ut,o as r}from"./index-Cr1r-Y_5.js";import{_ as ct}from"./_plugin-vue_export-helper-DlAUqK2U.js";const pt=["data-article-id"],mt={class:"mb-6 flex flex-col md:flex-row justify-between md:items-center space-y-4 md:space-y-0"},vt={class:"text-2xl font-bold text-white"},gt={class:"flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-3"},ft={class:"grid grid-cols-1 lg:grid-cols-3 gap-6"},yt={class:"lg:col-span-2 space-y-6"},bt={class:"admin-card p-4"},xt={id:"basic-content-form",class:"space-y-4"},_t={class:"flex justify-end"},kt=["disabled"],wt={class:"admin-card p-4"},ht={id:"premium-content-form",class:"space-y-4"},Ct={class:"flex justify-end"},$t=["disabled"],jt={class:"space-y-6"},Ut={class:"admin-card p-4"},Vt={id:"article-info-form",class:"space-y-4"},Ft={key:0},Et={class:"text-white"},It={key:1},Lt={class:"text-white"},Rt={key:2},At={class:"text-white"},St={key:3},Bt={class:"text-white"},Dt=["value"],Mt={class:"mb-4"},Pt={id:"article-tags-container",class:"p-3 border border-gray-600 rounded-lg max-h-40 overflow-y-auto space-y-2 bg-gray-700"},Nt=["value"],Tt={class:"ml-2"},zt={key:0,class:"text-gray-400 text-sm"},Gt={class:"mb-4"},qt={class:"mb-4"},Wt={class:"flex justify-end"},Xt=["disabled"],Jt={class:"admin-card p-4"},Ot={id:"price-form",class:"space-y-4"},Ht={class:"flex justify-end"},Kt=["disabled"],Qt={class:"admin-card p-4"},Yt={class:"mb-4"},Zt={class:"w-full h-40 bg-gray-700 rounded-lg flex items-center justify-center overflow-hidden relative"},te=["src"],ee={key:1,class:"text-gray-500"},se={id:"cover-form",class:"space-y-4"},le={class:"flex justify-end"},oe=["disabled"],ae={id:"cover-url-form",class:"space-y-4 mt-4"},ie={class:"flex justify-end"},re=["disabled"],ne={class:"admin-card p-4"},de={class:"space-y-4"},ue={id:"audio-files-list",class:"space-y-2 mb-4"},ce={key:0,class:"text-gray-400 text-sm"},pe={key:1,class:"text-gray-400 text-sm"},me={key:2},ve={class:"text-white text-sm"},ge=["onClick"],fe={class:"border-2 border-dashed border-gray-600 rounded-lg p-4"},ye={class:"text-center"},be={class:"mt-6"},xe={id:"subtitle-files-list",class:"space-y-2 mb-4"},_e={key:0,class:"text-gray-400 text-sm"},ke={key:1,class:"text-gray-400 text-sm"},we={key:2},he={class:"flex items-center space-x-3"},Ce={class:"text-white text-sm"},$e={class:"flex items-center space-x-2"},je=["href"],Ue=["href"],Ve=["onClick"],Fe={class:"border-2 border-dashed border-gray-600 rounded-lg p-4"},Ee={class:"text-center"},Ie={class:"mt-6"},Le={id:"cover-files-list",class:"mb-4"},Re={key:0,class:"text-gray-400 text-sm"},Ae={key:1,class:"text-gray-400 text-sm"},Se={key:2,class:"grid grid-cols-1 gap-3"},Be=["src","alt","onLoad"],De={class:"absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center"},Me=["href"],Pe=["onClick"],Ne=["onClick"],Te={class:"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 p-2"},ze={class:"text-white text-sm"},Ge={class:"border-2 border-dashed border-gray-600 rounded-lg p-4"},qe={class:"text-center"},We={class:"mt-6"},Xe={id:"image-files-list",class:"mb-4"},Je={key:0,class:"text-gray-400 text-sm"},Oe={key:1,class:"text-gray-400 text-sm"},He={key:2,class:"grid grid-cols-2 gap-3"},Ke=["src","alt","onLoad"],Qe={class:"absolute inset-0 bg-black bg-opacity-50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center"},Ye=["href"],Ze=["onClick"],ts=["onClick"],es={class:"absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 p-1"},ss={class:"text-white text-xs"},ls={class:"border-2 border-dashed border-gray-600 rounded-lg p-4"},os={class:"text-center"},as=ot({__name:"ArticleEditView",setup(A){const $=it(),b=dt();at();const u=V(!1),y=V(!1),w=V([]),x=V([]),n=N({audio:[],subtitle:[],image:[],cover:[]}),o=N({title:"",content:"",premium_content:"",status:"draft",reader_id:null,tag_ids:[],publish_date:"",language_code:"zh",content_type:"normal",price:0,premium_price:19.9,cover_image:"",cover_url:"",created_at:"",updated_at:"",views:0}),c=T(()=>!!$.params.id),d=T(()=>Number($.params.id)||null),S=l=>l?new Date(l).toLocaleString("zh-CN"):"",E=l=>{const t=l.target;t.src="/static/img/placeholder.svg",console.error("封面图片加载失败")},z=l=>{const t=l.target;console.log("图片加载成功",t.src)},G=()=>p(this,null,function*(){var l;if(!(!c.value||!d.value))try{const t=yield g.get(`/articles/${d.value}`);if(console.log("文章API响应:",t),t.success&&t.article){const s=t.article;console.log("文章数据:",s),console.log("原始发布日期:",s.publish_date),Object.assign(o,{title:s.title||"",content:s.content||"",premium_content:s.premium_content||"",status:s.status||"draft",reader_id:s.reader_id||null,tag_ids:((l=s.tags)==null?void 0:l.map(a=>a.id))||[],publish_date:s.publish_date?s.publish_date.split("T")[0]:"",language_code:s.language_code||"zh",content_type:s.content_type||"normal",price:s.price?s.price/100:0,premium_price:s.premium_price?s.premium_price/100:19.9,cover_image:s.cover_image||"",cover_url:s.cover_url||"",created_at:s.created_at||"",updated_at:s.updated_at||"",views:s.views||0}),console.log("设置后的发布日期:",o.publish_date),console.log("设置后的表单数据:",o)}}catch(t){console.error("Failed to load article:",t),alert("加载文章失败")}}),q=()=>p(this,null,function*(){try{const l=yield g.get("/readers");l.success&&l.data?w.value=l.data:(console.error("Failed to load readers:",l.message),w.value=[{id:1,name:"张三"},{id:2,name:"李四"}])}catch(l){console.error("Failed to load readers:",l),w.value=[{id:1,name:"张三"},{id:2,name:"李四"}]}}),W=()=>p(this,null,function*(){try{const l=yield g.get("/tags");l.success&&l.data?x.value=l.data:(console.error("Failed to load tags:",l.message),x.value=[{id:1,name:"塔罗牌"},{id:2,name:"占卜"},{id:3,name:"爱情"},{id:4,name:"运势"}])}catch(l){console.error("Failed to load tags:",l),x.value=[{id:1,name:"塔罗牌"},{id:2,name:"占卜"},{id:3,name:"爱情"},{id:4,name:"运势"}]}}),I=()=>p(this,null,function*(){if(!(!c.value||!d.value)){y.value=!0;try{const l=yield g.get(`/articles/${d.value}/media`);console.log("媒体文件API响应:",l),l.success&&l.data?(console.log("媒体文件数据:",l.data),n.audio=l.data.audio||[],n.subtitle=l.data.subtitle||[],n.image=l.data.image||[],n.cover=l.data.cover||[],console.log("设置后的媒体文件:",{audio:n.audio,subtitle:n.subtitle,image:n.image,cover:n.cover})):(console.error("Failed to load media files:",l.message),n.audio=[],n.subtitle=[],n.image=[],n.cover=[])}catch(l){console.error("Failed to load media files:",l),n.audio=[],n.subtitle=[],n.image=[],n.cover=[]}finally{y.value=!1}}}),X=()=>p(this,null,function*(){if(!o.title.trim()){alert("请输入文章标题");return}if(!o.content.trim()){alert("请输入文章内容");return}u.value=!0;try{if(d.value){const l=yield g.put(`/articles/${d.value}`,{title:o.title,content:o.content});l.success?alert("基本内容保存成功"):alert(`保存失败: ${l.error||"未知错误"}`)}else{const l=yield g.post("/articles",{title:o.title,content:o.content,status:"draft"});l.success?(alert("文章创建成功，正在刷新页面"),b.push(`/articles/${l.id}/edit`)):alert(`创建失败: ${l.error||"未知错误"}`)}}catch(l){console.error("保存基本内容出错:",l),alert("保存失败，请查看控制台获取详细信息")}finally{u.value=!1}}),J=()=>p(this,null,function*(){if(!d.value){alert("请先保存基本内容");return}u.value=!0;try{const l=yield g.put(`/articles/${d.value}`,{premium_content:o.premium_content});l.success?alert("高级内容保存成功"):alert(`保存失败: ${l.error||"未知错误"}`)}catch(l){console.error("保存高级内容出错:",l),alert("保存失败，请查看控制台获取详细信息")}finally{u.value=!1}}),O=()=>p(this,null,function*(){if(!d.value){alert("请先保存基本内容");return}u.value=!0;try{const l=yield g.put(`/articles/${d.value}`,{status:o.status,reader_id:o.reader_id,tags:o.tag_ids,publish_date:o.publish_date,language_code:o.language_code,content_type:o.content_type});l.success?alert("文章设置保存成功"):alert(`保存失败: ${l.error||"未知错误"}`)}catch(l){console.error("保存文章设置出错:",l),alert("保存失败，请查看控制台获取详细信息")}finally{u.value=!1}}),H=()=>p(this,null,function*(){if(!d.value){alert("请先保存基本内容");return}u.value=!0;try{const l=yield g.put(`/articles/${d.value}`,{price:Math.round(o.price*100),premium_price:Math.round(o.premium_price*100)});l.success?alert("价格设置保存成功"):alert(`保存失败: ${l.error||"未知错误"}`)}catch(l){console.error("保存价格设置出错:",l),alert("保存失败，请查看控制台获取详细信息")}finally{u.value=!1}}),K=()=>{o.cover_image="",o.cover_url=""},B=l=>{var a;const s=(a=l.target.files)==null?void 0:a[0];s&&console.log("选择了封面文件:",s.name)},Q=()=>p(this,null,function*(){var s,a;if(!d.value){alert("请先保存基本内容");return}const l=document.getElementById("cover-image"),t=(s=l==null?void 0:l.files)==null?void 0:s[0];if(!t){alert("请选择要上传的图片文件");return}u.value=!0;try{const j=new FormData;j.append("image",t);const M={"X-Requested-With":"XMLHttpRequest"},P=(a=document.querySelector('meta[name="csrf-token"]'))==null?void 0:a.getAttribute("content");P&&(M["X-CSRFToken"]=P);const R=yield(yield fetch(`/admin/api/articles/${d.value}/cover`,{method:"POST",headers:M,credentials:"same-origin",body:j})).json();R.success?(j.cover_image=R.cover_url,alert("封面上传成功"),l.value=""):alert(`上传失败: ${R.error||"未知错误"}`)}catch(j){console.error("上传封面出错:",j),alert("上传失败，请查看控制台获取详细信息")}finally{u.value=!1}}),Y=()=>p(this,null,function*(){if(!d.value){alert("请先保存基本内容");return}u.value=!0;try{const l=yield g.put(`/articles/${d.value}/cover-url`,{cover_url:o.cover_url});l.success?alert("封面URL保存成功"):alert(`保存失败: ${l.error||"未知错误"}`)}catch(l){console.error("保存封面URL出错:",l),alert("保存失败，请查看控制台获取详细信息")}finally{u.value=!1}}),Z=l=>{var a;const s=(a=l.target.files)==null?void 0:a[0];s&&L(s,"audio")},tt=l=>{var a;const s=(a=l.target.files)==null?void 0:a[0];s&&L(s,"subtitle")},et=l=>{const s=l.target.files;s&&s.length>0&&Array.from(s).forEach(a=>{L(a,"image")})},D=l=>{navigator.clipboard.writeText(l).then(()=>{alert("图片URL已复制到剪贴板")}).catch(t=>{console.error("复制失败:",t),alert("复制失败，请手动复制")})},L=(l,t)=>p(this,null,function*(){if(!d.value){alert("请先保存基本内容");return}const s=new FormData;s.append("file",l),s.append("file_type",t==="cover"?"image":t),s.append("file_category",t==="audio"?"original_audio":t==="cover"?"cover":t==="subtitle"?"subtitle":"content");try{const a=yield g.post(`/articles/${d.value}/media/upload`,s,{headers:{"Content-Type":"multipart/form-data"}});a.success?(alert(`${t}文件上传成功`),yield I()):alert(`上传失败: ${a.message||"未知错误"}`)}catch(a){console.error("上传媒体文件出错:",a),alert("上传失败，请查看控制台获取详细信息")}}),U=l=>p(this,null,function*(){if(confirm("确定要删除这个文件吗？"))try{const t=yield g.delete(`/media/${l}`);t.success?(alert("文件删除成功"),yield I()):alert(`删除失败: ${t.message||"未知错误"}`)}catch(t){console.error("删除媒体文件出错:",t),alert("删除失败，请查看控制台获取详细信息")}}),st=()=>{b.push("/articles")},lt=()=>{if(d.value){const l=`/admin/reading/${d.value}`;window.open(l,"_blank")}};return rt(()=>p(this,null,function*(){console.log("ArticleEditView 组件已挂载"),console.log("当前路由参数:",$.params),console.log("是否编辑模式:",c.value),console.log("文章ID:",d.value),yield q(),yield W(),c.value&&d.value&&(yield G(),yield I())})),(l,t)=>(r(),i("div",null,[e("div",{id:"article-context-data","data-article-id":d.value,style:{display:"none"}},null,8,pt),e("div",mt,[e("h1",vt,f(c.value?"编辑文章":"新建文章"),1),e("div",gt,[e("button",{onClick:st,class:"admin-btn-secondary px-4 py-2 rounded-lg text-center"},t[16]||(t[16]=[e("i",{class:"fas fa-arrow-left mr-2"},null,-1),m("返回列表 ",-1)])),c.value?(r(),i("button",{key:0,onClick:lt,class:"admin-btn-primary px-4 py-2 rounded-lg text-center"},t[17]||(t[17]=[e("i",{class:"fas fa-eye mr-2"},null,-1),m("预览文章 ",-1)]))):_("",!0)])]),e("div",ft,[e("div",yt,[e("div",bt,[t[21]||(t[21]=e("h2",{class:"text-lg font-semibold text-yellow-400 mb-4"},"基本内容",-1)),e("form",xt,[e("div",null,[t[18]||(t[18]=e("label",{for:"article-title",class:"block mb-2 text-gray-300"},"文章标题",-1)),v(e("input",{type:"text",id:"article-title","onUpdate:modelValue":t[0]||(t[0]=s=>o.title=s),class:"form-input w-full px-3 py-2 rounded-lg",required:""},null,512),[[k,o.title]])]),e("div",null,[t[19]||(t[19]=e("label",{for:"basic-content",class:"block mb-2 text-gray-300"},"基本内容",-1)),v(e("textarea",{id:"basic-content","onUpdate:modelValue":t[1]||(t[1]=s=>o.content=s),class:"form-input w-full px-3 py-2 rounded-lg",rows:"15",required:""},null,512),[[k,o.content]])]),e("div",_t,[e("button",{type:"button",onClick:X,disabled:u.value,class:"admin-btn-primary px-4 py-2 rounded-lg"},t[20]||(t[20]=[e("i",{class:"fas fa-save mr-2"},null,-1),m("保存基本内容 ",-1)]),8,kt)])])]),e("div",wt,[t[25]||(t[25]=e("h2",{class:"text-lg font-semibold text-yellow-400 mb-4"},"高级内容",-1)),e("form",ht,[e("div",null,[t[22]||(t[22]=e("label",{for:"premium-content",class:"block mb-2 text-gray-300"},"高级内容",-1)),v(e("textarea",{id:"premium-content","onUpdate:modelValue":t[2]||(t[2]=s=>o.premium_content=s),name:"premium_content",class:"form-input w-full px-3 py-2 rounded-lg",rows:"15"},null,512),[[k,o.premium_content]])]),e("div",Ct,[e("button",{type:"button",onClick:J,disabled:u.value,class:"admin-btn-primary px-4 py-2 rounded-lg"},t[23]||(t[23]=[e("i",{class:"fas fa-save mr-2"},null,-1),m("保存高级内容 ",-1)]),8,$t)]),t[24]||(t[24]=e("div",{id:"verification-result",class:"mt-2 p-2 rounded hidden"},null,-1))])])]),e("div",jt,[e("div",Ut,[t[41]||(t[41]=e("h2",{class:"text-lg font-semibold text-yellow-400 mb-4"},"文章信息",-1)),e("form",Vt,[c.value?(r(),i("div",Ft,[t[26]||(t[26]=e("p",{class:"text-gray-400 text-sm"},"文章ID",-1)),e("p",Et,f(d.value),1)])):_("",!0),c.value&&o.created_at?(r(),i("div",It,[t[27]||(t[27]=e("p",{class:"text-gray-400 text-sm"},"创建时间",-1)),e("p",Lt,f(S(o.created_at)),1)])):_("",!0),c.value&&o.updated_at?(r(),i("div",Rt,[t[28]||(t[28]=e("p",{class:"text-gray-400 text-sm"},"最后更新",-1)),e("p",At,f(S(o.updated_at)),1)])):_("",!0),c.value?(r(),i("div",St,[t[29]||(t[29]=e("p",{class:"text-gray-400 text-sm"},"浏览量",-1)),e("p",Bt,f(o.views||0),1)])):_("",!0),e("div",null,[t[31]||(t[31]=e("label",{for:"article-status",class:"block mb-2 text-gray-300"},"状态",-1)),v(e("select",{id:"article-status","onUpdate:modelValue":t[3]||(t[3]=s=>o.status=s),class:"form-input w-full px-3 py-2 rounded-lg"},t[30]||(t[30]=[e("option",{value:"draft"},"草稿",-1),e("option",{value:"published"},"已发布",-1),e("option",{value:"archived"},"已归档",-1)]),512),[[F,o.status]])]),e("div",null,[t[33]||(t[33]=e("label",{for:"article-reader",class:"block mb-2 text-gray-300"},"塔罗师",-1)),v(e("select",{id:"article-reader","onUpdate:modelValue":t[4]||(t[4]=s=>o.reader_id=s),class:"form-input w-full px-3 py-2 rounded-lg"},[t[32]||(t[32]=e("option",{value:""},"-- 选择塔罗师 --",-1)),(r(!0),i(h,null,C(w.value,s=>(r(),i("option",{key:s.id,value:s.id},f(s.name),9,Dt))),128))],512),[[F,o.reader_id]])]),e("div",Mt,[t[34]||(t[34]=e("label",{class:"block mb-2 text-gray-300"},"标签",-1)),e("div",Pt,[(r(!0),i(h,null,C(x.value,s=>(r(),i("label",{key:s.id,class:"flex items-center text-gray-200"},[v(e("input",{type:"checkbox",value:s.id,"onUpdate:modelValue":t[5]||(t[5]=a=>o.tag_ids=a),class:"form-checkbox h-4 w-4 text-yellow-500 border-gray-500 rounded focus:ring-yellow-400 bg-gray-600"},null,8,Nt),[[ut,o.tag_ids]]),e("span",Tt,f(s.name),1)]))),128)),x.value.length?_("",!0):(r(),i("p",zt,'暂无可用标签。请先在"标签管理"中添加标签。'))])]),e("div",Gt,[t[35]||(t[35]=e("label",{for:"article-publish-date",class:"block mb-2 text-gray-300"},"发布日期",-1)),v(e("input",{type:"date",id:"article-publish-date","onUpdate:modelValue":t[6]||(t[6]=s=>o.publish_date=s),class:"form-input w-full px-3 py-2 rounded-lg bg-gray-700 border-gray-600 text-white"},null,512),[[k,o.publish_date]])]),e("div",qt,[t[37]||(t[37]=e("label",{for:"article-language-code",class:"block mb-2 text-gray-300"},"语言",-1)),v(e("select",{id:"article-language-code","onUpdate:modelValue":t[7]||(t[7]=s=>o.language_code=s),class:"form-input w-full px-3 py-2 rounded-lg bg-gray-700 border-gray-600 text-white"},t[36]||(t[36]=[nt('<option value="" data-v-0cd34dd1>-- 选择语言 --</option><option value="zh" data-v-0cd34dd1>中文 (zh)</option><option value="en" data-v-0cd34dd1>英文 (en)</option><option value="ja" data-v-0cd34dd1>日文 (ja)</option><option value="ko" data-v-0cd34dd1>韩文 (ko)</option><option value="fr" data-v-0cd34dd1>法文 (fr)</option><option value="de" data-v-0cd34dd1>德文 (de)</option><option value="es" data-v-0cd34dd1>西班牙文 (es)</option>',8)]),512),[[F,o.language_code]])]),e("div",null,[t[39]||(t[39]=e("label",{for:"article-content-type",class:"block mb-2 text-gray-300"},"内容类型",-1)),v(e("select",{id:"article-content-type","onUpdate:modelValue":t[8]||(t[8]=s=>o.content_type=s),class:"form-input w-full px-3 py-2 rounded-lg"},t[38]||(t[38]=[e("option",{value:"normal"},"普通",-1),e("option",{value:"free"},"免费",-1),e("option",{value:"test"},"测试",-1)]),512),[[F,o.content_type]])]),e("div",Wt,[e("button",{type:"button",onClick:O,disabled:u.value,class:"admin-btn-primary px-4 py-2 rounded-lg"},t[40]||(t[40]=[e("i",{class:"fas fa-save mr-2"},null,-1),m("保存设置 ",-1)]),8,Xt)])])]),e("div",Jt,[t[45]||(t[45]=e("h2",{class:"text-lg font-semibold text-yellow-400 mb-4"},"价格设置",-1)),e("form",Ot,[e("div",null,[t[42]||(t[42]=e("label",{for:"basic-price",class:"block mb-2 text-gray-300"},"基础内容价格 (元)",-1)),v(e("input",{type:"number",id:"basic-price","onUpdate:modelValue":t[9]||(t[9]=s=>o.price=s),class:"form-input w-full px-3 py-2 rounded-lg",min:"0",step:"0.01"},null,512),[[k,o.price]])]),e("div",null,[t[43]||(t[43]=e("label",{for:"premium-price",class:"block mb-2 text-gray-300"},"高级内容价格 (元)",-1)),v(e("input",{type:"number",id:"premium-price","onUpdate:modelValue":t[10]||(t[10]=s=>o.premium_price=s),class:"form-input w-full px-3 py-2 rounded-lg",min:"0",step:"0.01"},null,512),[[k,o.premium_price]])]),e("div",Ht,[e("button",{type:"button",onClick:H,disabled:u.value,class:"admin-btn-primary px-4 py-2 rounded-lg"},t[44]||(t[44]=[e("i",{class:"fas fa-save mr-2"},null,-1),m("保存价格 ",-1)]),8,Kt)])])]),e("div",Qt,[t[52]||(t[52]=e("h2",{class:"text-lg font-semibold text-yellow-400 mb-4"},"封面图片",-1)),e("div",Yt,[t[47]||(t[47]=e("p",{class:"text-gray-300 mb-2"},"当前封面",-1)),e("div",Zt,[o.cover_image||o.cover_url?(r(),i("img",{key:0,src:o.cover_image||o.cover_url,alt:"文章封面",class:"w-full h-full object-cover",onError:E,onLoad:z},null,40,te)):(r(),i("div",ee,"无封面图片")),o.cover_image||o.cover_url?(r(),i("button",{key:2,onClick:K,class:"absolute top-2 right-2 bg-red-600 text-white rounded-full p-1"},t[46]||(t[46]=[e("i",{class:"fas fa-times"},null,-1)]))):_("",!0)])]),e("form",se,[e("div",null,[t[48]||(t[48]=e("label",{for:"cover-image",class:"block mb-2 text-gray-300"},"上传封面图片",-1)),e("input",{type:"file",id:"cover-image",onChange:B,class:"form-input w-full px-3 py-2 rounded-lg",accept:"image/*"},null,32)]),e("div",le,[e("button",{type:"button",onClick:Q,disabled:u.value,class:"admin-btn-primary px-4 py-2 rounded-lg"},t[49]||(t[49]=[e("i",{class:"fas fa-upload mr-2"},null,-1),m("上传封面 ",-1)]),8,oe)])]),e("form",ae,[e("div",null,[t[50]||(t[50]=e("label",{for:"cover-url",class:"block mb-2 text-gray-300"},"封面图片URL",-1)),v(e("input",{type:"url",id:"cover-url","onUpdate:modelValue":t[11]||(t[11]=s=>o.cover_url=s),class:"form-input w-full px-3 py-2 rounded-lg",placeholder:"https://example.com/image.jpg"},null,512),[[k,o.cover_url]])]),e("div",ie,[e("button",{type:"button",onClick:Y,disabled:u.value,class:"admin-btn-primary px-4 py-2 rounded-lg"},t[51]||(t[51]=[e("i",{class:"fas fa-save mr-2"},null,-1),m("保存URL ",-1)]),8,re)])])]),e("div",ne,[t[82]||(t[82]=e("h2",{class:"text-lg font-semibold text-yellow-400 mb-4"},"🎵 媒体管理",-1)),e("div",de,[e("div",null,[t[57]||(t[57]=e("h3",{class:"text-md font-medium text-gray-300 mb-3"},"音频文件",-1)),e("div",ue,[y.value?(r(),i("div",ce,"正在加载音频文件...")):n.audio.length?(r(),i("div",me,[(r(!0),i(h,null,C(n.audio,s=>(r(),i("div",{key:s.id,class:"flex items-center justify-between p-2 bg-gray-600 rounded"},[e("span",ve,f(s.filename),1),e("button",{onClick:a=>U(s.id),class:"text-red-400 hover:text-red-300"},t[53]||(t[53]=[e("i",{class:"fas fa-trash"},null,-1)]),8,ge)]))),128))])):(r(),i("div",pe,"暂无音频文件"))]),e("div",fe,[e("div",ye,[t[55]||(t[55]=e("i",{class:"fas fa-music text-gray-400 text-2xl mb-2"},null,-1)),t[56]||(t[56]=e("p",{class:"text-gray-400 mb-2"},"上传音频文件",-1)),e("input",{type:"file",id:"audio-upload",onChange:Z,accept:"audio/*",class:"hidden"},null,32),e("button",{type:"button",onClick:t[12]||(t[12]=s=>{var a;return(a=l.document.getElementById("audio-upload"))==null?void 0:a.click()}),class:"admin-btn-secondary px-4 py-2 rounded-lg"},t[54]||(t[54]=[e("i",{class:"fas fa-upload mr-2"},null,-1),m("选择音频文件 ",-1)]))])])]),e("div",be,[t[66]||(t[66]=e("h3",{class:"text-md font-medium text-gray-300 mb-3"},"字幕文件",-1)),e("div",xe,[y.value?(r(),i("div",_e,"正在加载字幕文件...")):n.subtitle.length?(r(),i("div",we,[(r(!0),i(h,null,C(n.subtitle,s=>(r(),i("div",{key:s.id,class:"flex items-center justify-between bg-gray-700 p-3 rounded-lg"},[e("div",he,[t[59]||(t[59]=e("i",{class:"fas fa-closed-captioning text-purple-400"},null,-1)),e("div",null,[e("p",Ce,f(s.filename),1),t[58]||(t[58]=e("p",{class:"text-gray-400 text-xs"},"✅ 已上传到R2存储",-1))])]),e("div",$e,[e("a",{href:s.url,target:"_blank",class:"text-blue-400 hover:text-blue-300",title:"查看字幕文件"},t[60]||(t[60]=[e("i",{class:"fas fa-eye"},null,-1)]),8,je),e("a",{href:s.url,download:"",class:"text-green-400 hover:text-green-300",title:"下载字幕文件"},t[61]||(t[61]=[e("i",{class:"fas fa-download"},null,-1)]),8,Ue),e("button",{onClick:a=>U(s.id),class:"text-red-400 hover:text-red-300",title:"删除文件"},t[62]||(t[62]=[e("i",{class:"fas fa-trash"},null,-1)]),8,Ve)])]))),128))])):(r(),i("div",ke,"暂无字幕文件"))]),e("div",Fe,[e("div",Ee,[t[64]||(t[64]=e("i",{class:"fas fa-closed-captioning text-gray-400 text-2xl mb-2"},null,-1)),t[65]||(t[65]=e("p",{class:"text-gray-400 mb-2"},"上传字幕文件",-1)),e("input",{type:"file",id:"subtitle-upload",onChange:tt,accept:".srt,.vtt,.ass",class:"hidden"},null,32),e("button",{type:"button",onClick:t[13]||(t[13]=s=>{var a;return(a=l.document.getElementById("subtitle-upload"))==null?void 0:a.click()}),class:"admin-btn-secondary px-4 py-2 rounded-lg"},t[63]||(t[63]=[e("i",{class:"fas fa-upload mr-2"},null,-1),m("选择字幕文件 (SRT/VTT/ASS) ",-1)]))])])]),e("div",Ie,[t[74]||(t[74]=e("h3",{class:"text-md font-medium text-gray-300 mb-3"},"封面文件",-1)),e("div",Le,[y.value?(r(),i("div",Re,"正在加载封面文件...")):n.cover.length?(r(),i("div",Se,[(r(!0),i(h,null,C(n.cover,s=>(r(),i("div",{key:s.id,class:"relative bg-gray-700 rounded-lg overflow-hidden"},[e("img",{src:s.url,alt:`封面${s.id}`,class:"w-full h-32 object-cover",onError:E,onLoad:a=>console.log("封面加载成功:",s.url)},null,40,Be),e("div",De,[e("a",{href:s.url,target:"_blank",class:"text-blue-400 hover:text-blue-300 mr-2",title:"在新窗口查看"},t[67]||(t[67]=[e("i",{class:"fas fa-external-link-alt"},null,-1)]),8,Me),e("button",{onClick:a=>D(s.url),class:"text-green-400 hover:text-green-300 mr-2",title:"复制URL"},t[68]||(t[68]=[e("i",{class:"fas fa-copy"},null,-1)]),8,Pe),e("button",{onClick:a=>U(s.id),class:"text-red-400 hover:text-red-300",title:"删除文件"},t[69]||(t[69]=[e("i",{class:"fas fa-trash"},null,-1)]),8,Ne)]),e("div",Te,[e("p",ze,f(s.filename)+" ✅",1),t[70]||(t[70]=e("p",{class:"text-gray-300 text-xs"},"封面图片",-1))])]))),128))])):(r(),i("div",Ae,"暂无封面文件"))]),e("div",Ge,[e("div",qe,[t[72]||(t[72]=e("i",{class:"fas fa-image text-gray-400 text-2xl mb-2"},null,-1)),t[73]||(t[73]=e("p",{class:"text-gray-400 mb-2"},"上传封面文件",-1)),e("input",{type:"file",id:"cover-upload",onChange:B,accept:".jpg,.jpeg,.png,.gif,.webp",class:"hidden"},null,32),e("button",{type:"button",onClick:t[14]||(t[14]=s=>{var a;return(a=l.document.getElementById("cover-upload"))==null?void 0:a.click()}),class:"admin-btn-secondary px-4 py-2 rounded-lg"},t[71]||(t[71]=[e("i",{class:"fas fa-upload mr-2"},null,-1),m("选择封面文件 (JPG/PNG/GIF/WEBP) ",-1)]))])])]),e("div",We,[t[81]||(t[81]=e("h3",{class:"text-md font-medium text-gray-300 mb-3"},"图鉴文件",-1)),e("div",Xe,[y.value?(r(),i("div",Je,"正在加载图鉴文件...")):n.image.length?(r(),i("div",He,[(r(!0),i(h,null,C(n.image,s=>(r(),i("div",{key:s.id,class:"relative bg-gray-700 rounded-lg overflow-hidden"},[e("img",{src:s.url,alt:`图鉴${s.id}`,class:"w-full h-20 object-cover",onError:E,onLoad:a=>console.log("图鉴加载成功:",s.url)},null,40,Ke),e("div",Qe,[e("a",{href:s.url,target:"_blank",class:"text-blue-400 hover:text-blue-300 mr-2",title:"在新窗口查看"},t[75]||(t[75]=[e("i",{class:"fas fa-external-link-alt"},null,-1)]),8,Ye),e("button",{onClick:a=>D(s.url),class:"text-green-400 hover:text-green-300 mr-2",title:"复制URL"},t[76]||(t[76]=[e("i",{class:"fas fa-copy"},null,-1)]),8,Ze),e("button",{onClick:a=>U(s.id),class:"text-red-400 hover:text-red-300",title:"删除文件"},t[77]||(t[77]=[e("i",{class:"fas fa-trash"},null,-1)]),8,ts)]),e("div",es,[e("p",ss,f(s.filename)+" ✅",1)])]))),128))])):(r(),i("div",Oe,"暂无图鉴文件"))]),e("div",ls,[e("div",os,[t[79]||(t[79]=e("i",{class:"fas fa-images text-gray-400 text-2xl mb-2"},null,-1)),t[80]||(t[80]=e("p",{class:"text-gray-400 mb-2"},"上传图鉴文件",-1)),e("input",{type:"file",id:"images-upload",onChange:et,accept:".jpg,.jpeg,.png,.gif,.webp",multiple:"",class:"hidden"},null,32),e("button",{type:"button",onClick:t[15]||(t[15]=s=>{var a;return(a=l.document.getElementById("images-upload"))==null?void 0:a.click()}),class:"admin-btn-secondary px-4 py-2 rounded-lg"},t[78]||(t[78]=[e("i",{class:"fas fa-upload mr-2"},null,-1),m("选择图鉴文件 (JPG/PNG/GIF/WEBP) ",-1)]))])])])])])])])]))}}),us=ct(as,[["__scopeId","data-v-0cd34dd1"]]);export{us as default};
