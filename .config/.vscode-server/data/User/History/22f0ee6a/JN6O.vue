<template>
  <div class="article-buyers">
    <!-- 页面标题 - 完全复刻原版Flask -->
    <div class="mb-6 flex justify-between items-center">
      <h1 class="text-xl font-bold text-white">{{ article?.title || '文章购买者列表' }}</h1>
      <router-link to="/articles" class="admin-btn-secondary px-4 py-2 rounded-lg">
        <i class="fas fa-arrow-left mr-2"></i>返回文章列表
      </router-link>
    </div>

    <!-- 文章基本信息 - 复刻原版Flask -->
    <div v-if="article" class="admin-card p-4 mb-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <span class="text-gray-400">ID:</span>
          <span class="ml-2 text-white">{{ article.id }}</span>
        </div>
        <div>
          <span class="text-gray-400">状态:</span>
          <span class="ml-2 px-2 py-1 rounded-full text-xs"
                :class="getStatusClass(article.status)">
            {{ getStatusText(article.status) }}
          </span>
        </div>
        <div>
          <span class="text-gray-400">创建时间:</span>
          <span class="ml-2 text-white">{{ formatDateTime(article.created_at) }}</span>
        </div>
      </div>
    </div>

    <!-- 购买者列表 - 复刻原版Flask -->
    <div class="admin-card p-4">
      <h2 class="text-lg font-bold text-white mb-4">购买者列表</h2>

      <!-- 加载状态 -->
      <div v-if="loading" id="buyers-loading" class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-yellow-500"></div>
        <p class="mt-2 text-gray-400">正在加载购买者数据...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" id="buyers-error" class="text-center py-8">
        <div class="text-red-400 mb-4">
          <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
          <p id="error-message">{{ error }}</p>
        </div>
        <button @click="loadBuyers" class="admin-btn-primary px-4 py-2 rounded-lg">
          <i class="fas fa-redo mr-2"></i>重新加载
        </button>
      </div>

      <!-- 无数据状态 -->
      <div v-else-if="buyers.length === 0" id="buyers-empty" class="text-center py-8">
        <div class="text-gray-400 mb-4">
          <i class="fas fa-users text-4xl mb-4"></i>
          <p class="text-lg">暂无购买者</p>
          <p class="text-sm">该文章还没有用户购买</p>
        </div>
      </div>

      <!-- 购买者列表 -->
      <div v-else id="buyers-list" class="overflow-x-auto">
        <table class="table-admin w-full">
          <thead>
            <tr>
              <th class="px-3 py-2 text-left">用户信息</th>
              <th class="px-3 py-2 text-left">内容类型</th>
              <th class="px-3 py-2 text-left">支付信息</th>
              <th class="px-3 py-2 text-left">配额消耗</th>
              <th class="px-3 py-2 text-left">积分消耗</th>
              <th class="px-3 py-2 text-left">用户状态</th>
              <th class="px-3 py-2 text-left">购买时间</th>
            </tr>
          </thead>
          <tbody id="buyers-table-body">
            <tr
              v-for="buyer in buyers"
              :key="buyer.user_id"
              class="hover:bg-gray-800 transition-colors"
            >
              <!-- 用户信息 -->
              <td class="px-3 py-2">
                <div class="text-white font-medium">{{ buyer.username }}</div>
                <div class="text-xs text-gray-400">ID: {{ buyer.user_id }}</div>
                <div class="text-xs text-gray-400">{{ buyer.email }}</div>
              </td>

              <!-- 内容类型 -->
              <td class="px-3 py-2">
                <span class="px-2 py-1 rounded text-xs font-medium"
                      :class="getContentTypeClass(buyer.content_type)">
                  {{ getContentTypeText(buyer.content_type) }}
                </span>
              </td>

              <!-- 支付信息 -->
              <td class="px-3 py-2">
                <div class="text-green-400 font-medium">
                  <span class="px-2 py-1 rounded text-xs"
                        :class="getCostTypeClass(buyer.cost_type)">
                    {{ getCostTypeText(buyer.cost_type) }}
                  </span>
                </div>
                <div class="text-xs text-gray-400 mt-1">
                  总价值: ¥{{ buyer.cost_amount.toFixed(2) }}
                </div>
                <div class="text-xs text-gray-400">
                  实付: ¥{{ buyer.amount_paid.toFixed(2) }}
                </div>
                <div v-if="buyer.transaction_id" class="text-xs text-gray-500 mt-1">
                  单号: {{ buyer.transaction_id }}
                </div>
              </td>

              <!-- 配额消耗 -->
              <td class="px-3 py-2">
                <div v-if="buyer.quota_used > 0" class="text-yellow-400 font-medium">
                  -{{ buyer.quota_used }} 配额
                </div>
                <div v-else class="text-gray-500">无配额消耗</div>
                <div v-if="buyer.quota_used > 0" class="text-xs text-gray-400">
                  类型: {{ getQuotaTypeText(buyer.quota_type) }}
                </div>
              </td>

              <!-- 积分消耗 -->
              <td class="px-3 py-2">
                <div v-if="buyer.points_used > 0" class="text-blue-400 font-medium">
                  -{{ buyer.points_used }} 积分
                </div>
                <div v-else class="text-gray-500">无积分消耗</div>
                <div v-if="buyer.points_balance_before !== null" class="text-xs text-gray-400">
                  余额: {{ buyer.points_balance_before }} → {{ buyer.points_balance_after }}
                </div>
              </td>

              <!-- 用户状态 -->
              <td class="px-3 py-2">
                <div class="text-xs">
                  <span class="px-2 py-1 rounded text-xs"
                        :class="getVipLevelClass(buyer.vip_level_at_purchase)">
                    {{ getVipLevelText(buyer.vip_level_at_purchase) }}
                  </span>
                  <div class="text-xs text-gray-400 mt-1">
                    购买时等级
                  </div>
                </div>

                <!-- 支付构成 -->
                <div class="text-xs text-gray-400 mt-2">
                  <div class="font-medium text-white">支付构成:</div>
                  <div class="text-xs">{{ buyer.cost_type }}</div>
                </div>

                  <!-- 配额部分 -->
                  <div v-if="buyer.quota_used > 0" class="flex justify-between">
                    <span>配额:</span>
                    <span class="text-yellow-400">{{ buyer.quota_used }}篇</span>
                  </div>

                  <!-- 免费购买标识 -->
                  <div v-if="buyer.is_free_purchase" class="text-xs text-orange-400 mt-1">
                    <i class="fas fa-gift mr-1"></i>免费获得
                  </div>
                </div>
              </td>

              <!-- 购买时间 -->
              <td class="px-3 py-2 text-gray-300 text-sm">
                {{ formatDateTime(buyer.purchase_time) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { articlesApi } from '@/api/articles'

// 路由
const route = useRoute()
const articleId = parseInt(route.params.id as string)

// 响应式数据
const loading = ref(false)
const error = ref('')
const article = ref(null)
const buyers = ref([])
const totalCount = ref(0)

// 状态映射
const getStatusText = (status: string) => {
  const statusMap = {
    'published': '已发布',
    'draft': '草稿',
    'archived': '已归档'
  }
  return statusMap[status] || status
}

const getStatusClass = (status: string) => {
  const classMap = {
    'published': 'bg-green-900 text-green-300',
    'draft': 'bg-gray-700 text-gray-300',
    'archived': 'bg-red-900 text-red-300'
  }
  return classMap[status] || 'bg-gray-700 text-gray-300'
}

// 内容类型映射
const getContentTypeText = (type: string) => {
  const typeMap = {
    'premium': '高级内容',
    'basic': '基础内容',
    'full': '完整内容'
  }
  return typeMap[type] || type
}

const getContentTypeClass = (type: string) => {
  const classMap = {
    'premium': 'bg-yellow-600 text-yellow-100',
    'basic': 'bg-blue-600 text-blue-100',
    'full': 'bg-green-600 text-green-100'
  }
  return classMap[type] || 'bg-gray-600 text-gray-100'
}

// 配额类型映射
const getQuotaTypeText = (type: string) => {
  const typeMap = {
    'premium': '高级配额',
    'basic': '基础配额',
    'vip': 'VIP配额',
    'recharge': '充值配额'
  }
  return typeMap[type] || type || '未知'
}

// VIP等级映射
const getVipLevelText = (level: number) => {
  const levelMap = {
    0: '普通用户',
    1: '初级VIP',
    2: 'VIP Pro'
  }
  return levelMap[level] || '未知'
}

const getVipLevelClass = (level: number) => {
  const classMap = {
    0: 'bg-gray-700 text-gray-300',
    1: 'bg-blue-700 text-blue-300',
    2: 'bg-purple-700 text-purple-300'
  }
  return classMap[level] || 'bg-gray-700 text-gray-300'
}

// 消费方式映射
const getCostTypeText = (type: string) => {
  const typeMap = {
    '现金支付': '现金支付',
    '积分支付': '积分支付',
    '基础配额': '基础配额',
    '高级配额': '高级配额',
    '混合支付': '混合支付',
    '自动赠送': '自动赠送',
    'cash': '现金支付',
    'quota': '配额使用',
    'points': '积分支付',
    'mixed': '混合支付',
    'grant': '自动赠送',
    'balance': '余额支付',
    'unknown': '未知'
  }
  return typeMap[type] || type
}

const getCostTypeClass = (type: string) => {
  const classMap = {
    '现金支付': 'bg-green-700 text-green-300',
    '积分支付': 'bg-blue-700 text-blue-300',
    '基础配额': 'bg-yellow-700 text-yellow-300',
    '高级配额': 'bg-orange-700 text-orange-300',
    '混合支付': 'bg-purple-700 text-purple-300',
    '自动赠送': 'bg-gray-700 text-gray-300',
    'cash': 'bg-green-700 text-green-300',
    'quota': 'bg-yellow-700 text-yellow-300',
    'points': 'bg-blue-700 text-blue-300',
    'mixed': 'bg-purple-700 text-purple-300',
    'grant': 'bg-orange-700 text-orange-300',
    'balance': 'bg-teal-700 text-teal-300',
    'unknown': 'bg-gray-700 text-gray-300'
  }
  return classMap[type] || 'bg-gray-700 text-gray-300'
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 加载购买者数据
const loadBuyers = async () => {
  loading.value = true
  error.value = ''

  try {
    // 调用真实API
    const response = await articlesApi.getBuyers(articleId)

    if (response.success) {
      article.value = response.article
      buyers.value = response.buyers || []
      totalCount.value = response.total_count || 0
    } else {
      error.value = response.error || '加载购买者数据失败'
    }
  } catch (err) {
    console.error('加载购买者失败:', err)
    error.value = '网络错误，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  if (articleId) {
    loadBuyers()
  } else {
    error.value = '无效的文章ID'
  }
})
</script>

<style scoped>
/* 复用管理后台的样式 */
.admin-card {
  @apply bg-gray-800 border border-gray-700 rounded-lg;
}

.admin-btn-primary {
  @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium transition-colors;
}

.admin-btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium transition-colors;
}

.table-admin {
  @apply bg-gray-800;
}

.table-admin th {
  @apply bg-gray-700 text-gray-300 font-medium;
}

.table-admin td {
  @apply border-t border-gray-700;
}
</style>
