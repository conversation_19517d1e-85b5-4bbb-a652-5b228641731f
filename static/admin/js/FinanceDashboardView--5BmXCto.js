var x=(v,i,r)=>new Promise((a,o)=>{var c=s=>{try{e(r.next(s))}catch(l){o(l)}},n=s=>{try{e(r.throw(s))}catch(l){o(l)}},e=s=>s.done?a(s.value):Promise.resolve(s.value).then(c,n);e((r=r.apply(v,i)).next())});import{f as _}from"./finance-Jd3QRa1X.js";import{d as w,i as u,k as h,c as p,a as t,t as d,F as j,l as k,b as y,g as f,f as b,w as g,r as C,n as F,o as m}from"./index-Bo6OtMFR.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";const D={class:"min-h-screen bg-gray-900 text-white"},N={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-6"},S={class:"finance-card bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-yellow-500 transition-colors"},B={class:"flex items-center justify-between"},z={class:"text-2xl font-bold text-yellow-400"},I={class:"finance-card bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-green-500 transition-colors"},L={class:"flex items-center justify-between"},P={class:"text-2xl font-bold text-green-400"},A={class:"finance-card bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-purple-500 transition-colors"},E={class:"flex items-center justify-between"},M={class:"text-2xl font-bold text-purple-400"},T={class:"finance-card bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-blue-500 transition-colors"},$={class:"flex items-center justify-between"},q={class:"text-2xl font-bold text-blue-400"},G={class:"mb-6"},H={class:"bg-gray-800 rounded-lg p-6 border border-gray-700"},J={class:"flex items-center justify-between mb-4"},K={class:"flex space-x-2"},O=["onClick"],Q={class:"h-64 bg-gray-700 rounded-lg flex items-center justify-center"},R={class:"text-center text-gray-400"},U={class:"text-sm"},W={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},X={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},Y={class:"space-y-3"},Z={class:"flex items-center justify-between"},tt={class:"text-orange-400"},et={class:"bg-gray-800 rounded-lg p-4 border border-gray-700"},st={class:"space-y-3"},at=w({__name:"FinanceDashboardView",setup(v){const i=u(!1),r=u("30d"),a=u({total_revenue:0,today_revenue:0,vip_users:0,today_orders:0,pending_anomalies:0}),o=n=>(n/100).toLocaleString("zh-CN",{minimumFractionDigits:2,maximumFractionDigits:2}),c=()=>x(this,null,function*(){i.value=!0;try{const n=yield _.getFinanceStats();n.success&&(a.value=n.data)}catch(n){console.error("获取财务数据失败:",n),a.value={total_revenue:123456,today_revenue:1234,vip_users:156,today_orders:23,pending_anomalies:3}}finally{i.value=!1}});return h(()=>{c()}),(n,e)=>{const s=C("router-link");return m(),p("div",D,[e[20]||(e[20]=t("div",{class:"mb-6"},[t("h1",{class:"text-2xl font-bold text-white"},"财务控制面板"),t("p",{class:"text-gray-400 mt-1"},"财务数据统计和分析")],-1)),t("div",N,[t("div",S,[t("div",B,[t("div",null,[e[0]||(e[0]=t("h3",{class:"text-sm font-medium text-gray-400"},"总收入",-1)),t("p",z,"¥"+d(o(a.value.total_revenue||0)),1)]),e[1]||(e[1]=t("div",{class:"h-12 w-12 flex items-center justify-center rounded-full bg-yellow-900/40 text-yellow-300"},[t("i",{class:"fas fa-coins text-xl"})],-1))])]),t("div",I,[t("div",L,[t("div",null,[e[2]||(e[2]=t("h3",{class:"text-sm font-medium text-gray-400"},"今日收入",-1)),t("p",P,"¥"+d(o(a.value.today_revenue||0)),1)]),e[3]||(e[3]=t("div",{class:"h-12 w-12 flex items-center justify-center rounded-full bg-green-900/40 text-green-300"},[t("i",{class:"fas fa-chart-line text-xl"})],-1))])]),t("div",A,[t("div",E,[t("div",null,[e[4]||(e[4]=t("h3",{class:"text-sm font-medium text-gray-400"},"VIP用户",-1)),t("p",M,d(a.value.vip_users||0)+"人",1)]),e[5]||(e[5]=t("div",{class:"h-12 w-12 flex items-center justify-center rounded-full bg-purple-900/40 text-purple-300"},[t("i",{class:"fas fa-crown text-xl"})],-1))])]),t("div",T,[t("div",$,[t("div",null,[e[6]||(e[6]=t("h3",{class:"text-sm font-medium text-gray-400"},"今日订单",-1)),t("p",q,d(a.value.today_orders||0),1)]),e[7]||(e[7]=t("div",{class:"h-12 w-12 flex items-center justify-center rounded-full bg-blue-900/40 text-blue-300"},[t("i",{class:"fas fa-shopping-cart text-xl"})],-1))])])]),t("div",G,[t("div",H,[t("div",J,[e[8]||(e[8]=t("h3",{class:"text-lg font-semibold text-yellow-400"},"月度收入趋势",-1)),t("div",K,[(m(),p(j,null,k(["7d","30d","90d"],l=>t("button",{key:l,onClick:nt=>r.value=l,class:F(["px-3 py-1 rounded text-sm transition-colors",r.value===l?"bg-yellow-600 text-white":"bg-gray-700 text-gray-300 hover:bg-gray-600"])},d(l==="7d"?"7天":l==="30d"?"30天":"90天"),11,O)),64))])]),t("div",Q,[t("div",R,[e[9]||(e[9]=t("i",{class:"fas fa-chart-area fa-3x mb-4"},null,-1)),e[10]||(e[10]=t("p",null,"收入趋势图表",-1)),t("p",U,d(r.value)+" 数据",1)])])])]),t("div",W,[e[19]||(e[19]=y('<div class="bg-gray-800 rounded-lg p-4 border border-gray-700" data-v-d0ff3bda><h3 class="text-lg font-semibold text-yellow-400 mb-4" data-v-d0ff3bda>对账状态</h3><div class="space-y-3" data-v-d0ff3bda><div class="flex items-center justify-between" data-v-d0ff3bda><span class="text-gray-300" data-v-d0ff3bda>今日对账</span><span class="text-green-400" data-v-d0ff3bda><i class="fas fa-check-circle mr-1" data-v-d0ff3bda></i>已完成 </span></div><div class="flex items-center justify-between" data-v-d0ff3bda><span class="text-gray-300" data-v-d0ff3bda>昨日差异</span><span class="text-gray-400" data-v-d0ff3bda>¥0</span></div><div class="flex items-center justify-between" data-v-d0ff3bda><span class="text-gray-300" data-v-d0ff3bda>上次对账</span><span class="text-gray-400" data-v-d0ff3bda>今天</span></div></div></div>',1)),t("div",X,[e[14]||(e[14]=t("h3",{class:"text-lg font-semibold text-yellow-400 mb-4"},"异常监控",-1)),t("div",Y,[t("div",Z,[e[12]||(e[12]=t("span",{class:"text-gray-300"},"待处理异常",-1)),t("span",tt,[e[11]||(e[11]=t("i",{class:"fas fa-exclamation-triangle mr-1"},null,-1)),f(d(a.value.pending_anomalies||0)+"个 ",1)])]),e[13]||(e[13]=y('<div class="flex items-center justify-between" data-v-d0ff3bda><span class="text-gray-300" data-v-d0ff3bda>最新异常</span><span class="text-gray-400" data-v-d0ff3bda>2小时前</span></div><div class="flex items-center justify-between" data-v-d0ff3bda><span class="text-gray-300" data-v-d0ff3bda>异常趋势</span><span class="text-green-400" data-v-d0ff3bda><i class="fas fa-arrow-down mr-1" data-v-d0ff3bda></i>下降 </span></div>',2))])]),t("div",et,[e[18]||(e[18]=t("h3",{class:"text-lg font-semibold text-yellow-400 mb-4"},"快速操作",-1)),t("div",st,[b(s,{to:"/finance/orders",class:"btn-action btn-primary w-full justify-center flex items-center py-2 px-4 bg-blue-600 hover:bg-blue-700 rounded transition-colors"},{default:g(()=>e[15]||(e[15]=[t("i",{class:"fas fa-list mr-2"},null,-1),f("查看所有订单 ",-1)])),_:1,__:[15]}),b(s,{to:"/finance/reconciliation",class:"btn-action btn-success w-full justify-center flex items-center py-2 px-4 bg-green-600 hover:bg-green-700 rounded transition-colors"},{default:g(()=>e[16]||(e[16]=[t("i",{class:"fas fa-calculator mr-2"},null,-1),f("详细收入验证 ",-1)])),_:1,__:[16]}),b(s,{to:"/points",class:"btn-action btn-info w-full justify-center flex items-center py-2 px-4 bg-purple-600 hover:bg-purple-700 rounded transition-colors"},{default:g(()=>e[17]||(e[17]=[t("i",{class:"fas fa-coins mr-2"},null,-1),f("积分管理 ",-1)])),_:1,__:[17]})])])])])}}}),it=V(at,[["__scopeId","data-v-d0ff3bda"]]);export{it as default};
