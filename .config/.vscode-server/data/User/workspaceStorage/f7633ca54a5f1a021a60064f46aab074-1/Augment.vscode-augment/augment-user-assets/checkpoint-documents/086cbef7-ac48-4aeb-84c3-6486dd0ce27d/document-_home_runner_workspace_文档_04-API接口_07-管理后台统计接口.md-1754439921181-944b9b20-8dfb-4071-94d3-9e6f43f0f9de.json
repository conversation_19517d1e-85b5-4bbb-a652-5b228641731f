{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "文档/04-API接口/07-管理后台统计接口.md"}, "originalCode": "# 07-管理后台统计接口\n\n## 📋 概述\n\n本文档详细描述了Vue3管理后台各个页面所需的统计API接口，为管理员提供全面的数据分析和监控功能。\n\n**✅ 更新状态**: 2025-08-05 - 所有API接口已完整实现，共86个API端点\n\n## 🎯 接口分类\n\n### 1. 认证相关接口 ✅ 已实现\n### 2. 仪表盘统计接口 ✅ 已实现\n### 3. 文章管理统计接口 ✅ 已实现\n### 4. 用户管理统计接口 ✅ 已实现\n### 5. 标签管理统计接口 ✅ 已实现\n### 6. 塔罗师管理统计接口 ✅ 已实现\n### 7. 公告管理统计接口 ✅ 已实现\n### 8. 积分管理统计接口 ✅ 已实现\n### 9. 工单管理统计接口 ✅ 已实现\n### 10. 财务管理统计接口 ✅ 已实现\n### 11. 上传管理统计接口 ✅ 已实现\n### 12. 系统配置接口 ✅ 已实现\n\n## 🚀 API实现状态总览\n\n**总计**: 91个API接口（新增5个文章编辑相关接口）\n- 认证接口: 3个\n- 仪表盘接口: 4个\n- 文章管理接口: 20个 ⭐ 新增5个\n- 用户管理接口: 12个\n- 标签管理接口: 6个\n- 塔罗师管理接口: 8个\n- 公告管理接口: 6个\n- 积分管理接口: 4个\n- 工单管理接口: 8个\n- 财务管理接口: 3个\n- 上传管理接口: 9个\n- 系统配置接口: 8个\n\n---\n\n## 🔐 1. 认证相关接口\n\n### 1.1 管理员登录\n- **接口**: `POST /admin/api/auth/login`\n- **描述**: Vue3管理后台专用登录接口\n- **权限**: 无需认证\n- **请求体**:\n```json\n{\n  \"username\": \"admin\",\n  \"password\": \"123456\"\n}\n```\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"token\": \"flask-session\",\n    \"user\": {\n      \"id\": 1,\n      \"username\": \"admin\",\n      \"email\": \"<EMAIL>\",\n      \"is_admin\": true,\n      \"is_staff\": true,\n      \"role\": \"admin\",\n      \"membership_type\": \"admin\",\n      \"created_at\": \"2025-01-01T00:00:00\"\n    }\n  },\n  \"message\": \"登录成功\"\n}\n```\n\n### 1.2 管理员登出\n- **接口**: `POST /admin/api/auth/logout`\n- **描述**: 管理员登出\n- **权限**: @login_required\n\n### 1.3 获取当前用户信息\n- **接口**: `GET /admin/api/auth/me`\n- **描述**: 获取当前登录管理员信息\n- **权限**: @login_required + @staff_required\n\n---\n\n## 📊 2. 仪表盘统计接口\n\n### 2.1 获取仪表盘核心统计\n- **接口**: `GET /admin/api/admin/dashboard/stats`\n- **描述**: 获取仪表盘核心指标\n- **权限**: @login_required + @staff_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"total_users\": 1234,\n    \"total_articles\": 567,\n    \"total_orders\": 89,\n    \"total_revenue\": 12345.67,\n    \"today_users\": 12,\n    \"today_articles\": 3,\n    \"today_orders\": 5,\n    \"today_revenue\": 234.56\n  }\n}\n```\n\n### 2.2 获取最近用户\n- **接口**: `GET /admin/api/admin/dashboard/recent-users`\n- **参数**: `limit` (默认5)\n- **权限**: @login_required + @staff_required\n\n### 2.3 获取最近文章\n- **接口**: `GET /admin/api/admin/dashboard/recent-articles`\n- **参数**: `limit` (默认5)\n- **权限**: @login_required + @staff_required\n\n### 2.4 获取完整仪表盘数据\n- **接口**: `GET /admin/api/admin/dashboard`\n- **描述**: 一次性获取所有仪表盘数据\n- **权限**: @login_required + @staff_required\n\n---\n\n## 📝 3. 文章管理统计接口\n\n### 3.1 文章列表\n- **接口**: `GET /admin/api/articles`\n- **描述**: 获取文章列表（已有）\n- **权限**: @login_required + @admin_required\n- **参数**:\n  - `page`: 页码\n  - `per_page`: 每页数量\n  - `search`: 搜索关键词\n  - `status`: 文章状态\n  - `author_filter`: 作者筛选\n  - `tag_filter`: 标签筛选\n\n### 3.2 文章统计数据 ⭐ 新增\n- **接口**: `GET /admin/api/articles/stats`\n- **描述**: 获取文章详细统计信息\n- **权限**: @login_required + @admin_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"total_articles\": 567,\n    \"published_articles\": 456,\n    \"draft_articles\": 89,\n    \"pending_articles\": 22,\n    \"today_published\": 5,\n    \"this_week_published\": 23,\n    \"this_month_published\": 89,\n    \"status_distribution\": {\n      \"published\": 456,\n      \"draft\": 89,\n      \"pending\": 22\n    },\n    \"author_distribution\": [\n      {\"name\": \"塔罗师A\", \"count\": 45},\n      {\"name\": \"塔罗师B\", \"count\": 32}\n    ]\n  }\n}\n```\n\n### 3.3 文章CRUD操作 ⭐ 新增\n\n#### 获取单篇文章详情\n- **接口**: `GET /admin/api/articles/{id}`\n- **描述**: 获取文章详细信息（用于编辑页面）\n- **权限**: @login_required + @admin_required\n\n#### 创建文章\n- **接口**: `POST /admin/api/articles`\n- **描述**: 创建新文章\n- **权限**: @login_required + @staff_required\n- **请求体**:\n```json\n{\n  \"title\": \"文章标题\",\n  \"content\": \"文章内容\",\n  \"summary\": \"文章摘要\",\n  \"reader_id\": 1,\n  \"tag_ids\": [1, 2, 3],\n  \"status\": \"draft\",\n  \"is_premium\": false,\n  \"price\": 0,\n  \"cover_url\": \"封面图片URL\"\n}\n```\n\n#### 更新文章\n- **接口**: `PUT /admin/api/articles/{id}`\n- **描述**: 更新文章信息\n- **权限**: @login_required + @staff_required\n- **请求体**: 同创建文章\n\n#### 删除文章\n- **接口**: `DELETE /admin/api/articles/{id}`\n- **描述**: 删除文章\n- **权限**: @login_required + @admin_required\n\n#### 批量删除文章\n- **接口**: `POST /admin/api/articles/batch_delete`\n- **描述**: 批量删除文章\n- **权限**: @login_required + @admin_required\n- **请求体**:\n```json\n{\n  \"article_ids\": [1, 2, 3, 4]\n}\n```\n\n#### 上传文章封面\n- **接口**: `POST /admin/api/articles/upload-cover`\n- **描述**: 上传文章封面图片\n- **权限**: @login_required + @staff_required\n- **请求**: multipart/form-data 文件上传\n\n#### 更新文章封面URL\n- **接口**: `PUT /admin/api/articles/{id}/cover-url`\n- **描述**: 更新文章封面URL\n- **权限**: @login_required + @staff_required\n\n#### 更新文章高级内容\n- **接口**: `PUT /admin/api/articles/{id}/premium`\n- **描述**: 更新文章高级内容\n- **权限**: @login_required + @staff_required\n\n#### 更新文章价格\n- **接口**: `PUT /admin/api/articles/{id}/price`\n- **描述**: 更新文章价格设置\n- **权限**: @login_required + @finance_required\n\n#### 获取塔罗师列表 ⭐ 新增\n- **接口**: `GET /admin/api/readers`\n- **描述**: 获取塔罗师列表（用于文章编辑页面）\n- **权限**: @login_required + @staff_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": [\n    {\n      \"id\": 1,\n      \"name\": \"张三\",\n      \"email\": \"<EMAIL>\",\n      \"bio\": \"专业塔罗师\",\n      \"avatar\": \"/static/avatars/1.jpg\",\n      \"status\": \"active\",\n      \"article_count\": 25\n    }\n  ]\n}\n```\n\n#### 获取文章媒体文件 ⭐ 新增\n- **接口**: `GET /admin/api/articles/{id}/media`\n- **描述**: 获取文章的媒体文件列表\n- **权限**: @login_required + @staff_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"audio\": [\n      {\n        \"id\": 1,\n        \"filename\": \"article_1_audio.mp3\",\n        \"file_path\": \"/uploads/audio/article_1_audio.mp3\",\n        \"file_size\": 1024000,\n        \"duration\": 180,\n        \"file_type\": \"audio/mpeg\",\n        \"created_at\": \"2024-08-06T10:00:00\"\n      }\n    ],\n    \"subtitle\": [],\n    \"image\": []\n  }\n}\n```\n\n#### 上传文章媒体文件 ⭐ 新增\n- **接口**: `POST /admin/api/articles/{id}/media/upload`\n- **描述**: 上传文章的音频、字幕等媒体文件\n- **权限**: @login_required + @staff_required\n- **请求**: multipart/form-data\n```\nfile: [文件]\nfile_type: \"audio\" | \"subtitle\" | \"image\"\nfile_category: \"original_audio\" | \"content\"\n```\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"id\": 1,\n    \"filename\": \"uploaded_audio.mp3\",\n    \"file_path\": \"/uploads/audio/uploaded_audio.mp3\",\n    \"file_size\": 1024000,\n    \"file_type\": \"audio/mpeg\"\n  },\n  \"message\": \"文件上传成功\"\n}\n```\n\n#### 删除媒体文件 ⭐ 新增\n- **接口**: `DELETE /admin/api/media/{file_id}`\n- **描述**: 删除媒体文件\n- **权限**: @login_required + @staff_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"message\": \"文件删除成功\"\n}\n```\n\n### 📝 **媒体文件管理功能说明**\n\n#### **智能媒体文件检测** ⭐ 新增功能\n文章媒体文件API现在支持智能检测，不仅从MediaFile表中查找，还会从文章字段中提取媒体信息：\n\n1. **字幕文件检测**：\n   - 优先从MediaFile表查找\n   - 如果没有记录，从`audio_sync_data`字段的JSON中提取`subtitle_url`\n   - 自动生成虚拟字幕文件记录\n\n2. **图鉴文件检测**：\n   - 优先从MediaFile表查找\n   - 如果没有记录，从`content_images`字段的JSON数组中提取\n   - 自动生成虚拟图片文件记录\n\n3. **音频文件检测**：\n   - 优先从MediaFile表查找\n   - 如果没有记录，从`original_audio_url`字段中提取\n   - 自动生成虚拟音频文件记录\n\n#### **虚拟文件记录格式**\n```json\n{\n  \"id\": \"virtual_{original_id}_{type}_{index}\",\n  \"filename\": \"{original_id}_{type}_{index}.ext\",\n  \"file_type\": \"audio|subtitle|image\",\n  \"file_category\": \"original_audio|subtitle|content\",\n  \"url\": \"https://r2-domain.com/path/to/file\",\n  \"upload_status\": \"uploaded\",\n  \"created_at\": \"2025-07-11T21:38:25.000622\"\n}\n```\n\n#### **前端媒体管理界面** ⭐ 新增\nVue3文章编辑页面现在包含完整的媒体管理功能：\n\n1. **音频文件管理**：\n   - 显示音频文件列表\n   - 支持MP3、WAV、M4A格式上传\n   - 在线播放和删除功能\n\n2. **字幕文件管理**：\n   - 显示字幕文件列表\n   - 支持SRT、VTT、ASS格式上传\n   - 在线查看、下载和删除功能\n\n3. **图鉴文件管理**：\n   - 网格布局显示图鉴缩略图\n   - 支持JPG、PNG、GIF、WEBP格式\n   - 批量上传、在新窗口查看、复制URL、删除功能\n\n---\n\n## 👥 4. 用户管理统计接口\n\n### 4.1 用户列表\n- **接口**: `GET /admin/api/users`\n- **描述**: 获取用户列表（已有）\n- **权限**: @login_required + @admin_required\n\n### 4.2 用户统计数据 ⭐ 新增\n- **接口**: `GET /admin/api/users/stats`\n- **描述**: 获取用户详细统计信息\n- **权限**: @login_required + @admin_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"total_users\": 1234,\n    \"active_users\": 890,\n    \"vip_users\": 123,\n    \"today_registered\": 12,\n    \"this_week_registered\": 45,\n    \"this_month_registered\": 156,\n    \"role_distribution\": {\n      \"user\": 1100,\n      \"vip\": 123,\n      \"staff\": 10,\n      \"admin\": 1\n    },\n    \"membership_distribution\": {\n      \"free\": 1100,\n      \"monthly\": 89,\n      \"quarterly\": 34,\n      \"yearly\": 11\n    },\n    \"registration_trend\": [\n      {\"date\": \"2025-08-01\", \"count\": 15},\n      {\"date\": \"2025-08-02\", \"count\": 12}\n    ]\n  }\n}\n```\n\n---\n\n## 🏷️ 5. 标签管理统计接口\n\n### 5.1 标签列表\n- **接口**: `GET /admin/api/tags`\n- **描述**: 获取标签列表（已有）\n- **权限**: @login_required + @staff_required\n\n### 5.2 标签统计数据 ⭐ 新增\n- **接口**: `GET /admin/api/tags/stats`\n- **描述**: 获取标签详细统计信息\n- **权限**: @login_required + @staff_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"total_tags\": 45,\n    \"used_tags\": 38,\n    \"unused_tags\": 7,\n    \"most_used_tags\": [\n      {\"name\": \"塔罗解读\", \"article_count\": 123},\n      {\"name\": \"占卜指南\", \"article_count\": 89}\n    ],\n    \"tag_usage_distribution\": [\n      {\"range\": \"1-5篇\", \"count\": 15},\n      {\"range\": \"6-10篇\", \"count\": 12},\n      {\"range\": \"11+篇\", \"count\": 11},\n      {\"range\": \"未使用\", \"count\": 7}\n    ]\n  }\n}\n```\n\n---\n\n## 🔮 6. 塔罗师管理统计接口\n\n### 6.1 塔罗师列表\n- **接口**: `GET /admin/api/authors`\n- **描述**: 获取塔罗师列表（已有）\n- **权限**: @login_required + @admin_required\n\n### 6.2 塔罗师统计数据 ⭐ 新增\n- **接口**: `GET /admin/api/authors/stats`\n- **描述**: 获取塔罗师详细统计信息\n- **权限**: @login_required + @admin_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"total_authors\": 23,\n    \"active_authors\": 18,\n    \"featured_authors\": 5,\n    \"total_articles_by_authors\": 456,\n    \"avg_articles_per_author\": 19.8,\n    \"top_authors\": [\n      {\"name\": \"塔罗师A\", \"article_count\": 45, \"total_views\": 12345},\n      {\"name\": \"塔罗师B\", \"article_count\": 32, \"total_views\": 9876}\n    ],\n    \"author_performance\": [\n      {\"name\": \"塔罗师A\", \"articles\": 45, \"views\": 12345, \"rating\": 4.8},\n      {\"name\": \"塔罗师B\", \"articles\": 32, \"views\": 9876, \"rating\": 4.6}\n    ]\n  }\n}\n```\n\n---\n\n## 📢 7. 公告管理统计接口\n\n### 7.1 公告列表\n- **接口**: `GET /admin/api/announcements`\n- **描述**: 获取公告列表（已有）\n- **权限**: @login_required + @admin_required\n\n### 7.2 公告统计数据 ⭐ 新增\n- **接口**: `GET /admin/api/announcements/stats`\n- **描述**: 获取公告详细统计信息\n- **权限**: @login_required + @admin_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"total_announcements\": 12,\n    \"active_announcements\": 8,\n    \"expired_announcements\": 4,\n    \"today_created\": 1,\n    \"this_week_created\": 3,\n    \"announcement_types\": {\n      \"system\": 5,\n      \"promotion\": 4,\n      \"maintenance\": 3\n    },\n    \"view_stats\": {\n      \"total_views\": 12345,\n      \"avg_views_per_announcement\": 1028\n    }\n  }\n}\n```\n\n---\n\n## 💰 8. 积分管理统计接口\n\n### 8.1 积分交易记录\n- **接口**: `GET /admin/api/points/transactions`\n- **描述**: 获取积分交易记录（已有）\n- **权限**: @login_required + @admin_required\n\n### 8.2 积分统计数据\n- **接口**: `GET /admin/api/points/statistics`\n- **描述**: 获取积分详细统计信息（已有）\n- **权限**: @login_required + @admin_required\n\n---\n\n## 🎫 9. 工单管理统计接口\n\n### 9.1 工单列表\n- **接口**: `GET /admin/api/tickets`\n- **描述**: 获取工单列表（已有）\n- **权限**: @login_required + @staff_required\n\n### 9.2 工单统计数据 ⭐ 新增\n- **接口**: `GET /admin/api/tickets/stats`\n- **描述**: 获取工单详细统计信息\n- **权限**: @login_required + @staff_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"total_tickets\": 234,\n    \"open_tickets\": 45,\n    \"closed_tickets\": 189,\n    \"today_created\": 5,\n    \"today_closed\": 8,\n    \"avg_response_time\": 2.5,\n    \"avg_resolution_time\": 24.5,\n    \"status_distribution\": {\n      \"open\": 45,\n      \"in_progress\": 23,\n      \"pending\": 12,\n      \"closed\": 154\n    },\n    \"priority_distribution\": {\n      \"low\": 123,\n      \"medium\": 89,\n      \"high\": 22\n    },\n    \"category_distribution\": {\n      \"technical\": 89,\n      \"billing\": 67,\n      \"general\": 78\n    },\n    \"staff_performance\": [\n      {\"staff\": \"客服A\", \"assigned\": 23, \"resolved\": 20, \"avg_time\": 18.5},\n      {\"staff\": \"客服B\", \"assigned\": 19, \"resolved\": 17, \"avg_time\": 22.3}\n    ]\n  }\n}\n```\n\n---\n\n## 💳 10. 财务管理统计接口\n\n### 10.1 财务统计数据\n- **接口**: `GET /admin/api/finance/stats`\n- **描述**: 获取财务统计数据（已有）\n- **权限**: @login_required + @finance_required\n\n### 10.2 订单列表\n- **接口**: `GET /admin/api/finance/orders`\n- **描述**: 获取订单列表（已有）\n- **权限**: @login_required + @finance_required\n\n---\n\n## 📤 11. 上传管理统计接口\n\n### 11.1 上传配置管理\n- **接口**: `GET /admin/api/uploader/config`\n- **描述**: 获取上传配置（已有）\n- **权限**: @login_required + @admin_required\n\n### 11.2 上传日志列表\n- **接口**: `GET /admin/api/uploader/logs`\n- **描述**: 获取上传日志列表（已有）\n- **权限**: @login_required + @admin_required\n\n### 11.3 上传日志统计数据 ⭐ 新增\n- **接口**: `GET /admin/api/uploader/logs/stats`\n- **描述**: 获取上传日志详细统计信息\n- **权限**: @login_required + @admin_required\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"total_uploads\": 1234,\n    \"successful_uploads\": 1156,\n    \"failed_uploads\": 78,\n    \"today_uploads\": 23,\n    \"this_week_uploads\": 156,\n    \"total_file_size\": 2048576000,\n    \"avg_file_size\": 1658880,\n    \"file_type_distribution\": {\n      \"pdf\": 456,\n      \"docx\": 234,\n      \"txt\": 123,\n      \"md\": 89\n    },\n    \"upload_trend\": [\n      {\"date\": \"2025-08-01\", \"count\": 23, \"success\": 21, \"failed\": 2},\n      {\"date\": \"2025-08-02\", \"count\": 28, \"success\": 26, \"failed\": 2}\n    ]\n  }\n}\n```\n\n---\n\n## ⚙️ 12. 系统配置接口\n\n### 12.1 系统设置管理 ⭐ 新增\n- **接口**: `GET /admin/api/system/settings`\n- **描述**: 获取系统设置\n- **权限**: @login_required + @admin_required\n\n- **接口**: `PUT /admin/api/system/settings`\n- **描述**: 更新系统设置\n- **权限**: @login_required + @admin_required\n\n### 12.2 价格设置管理 ⭐ 新增\n- **接口**: `GET /admin/api/system/prices`\n- **描述**: 获取价格设置\n- **权限**: @login_required + @admin_required\n\n- **接口**: `PUT /admin/api/system/prices`\n- **描述**: 更新价格设置\n- **权限**: @login_required + @admin_required\n\n### 12.3 数据统计概览 ⭐ 新增\n- **接口**: `GET /admin/api/statistics/overview`\n- **描述**: 获取全站数据统计概览\n- **权限**: @login_required + @admin_required\n- **参数**:\n  - `date_from`: 开始日期\n  - `date_to`: 结束日期\n  - `period`: 统计周期 (today/week/month/quarter/year/custom)\n\n---\n\n## 📋 API响应格式规范\n\n### 成功响应\n```json\n{\n  \"success\": true,\n  \"data\": {\n    // 具体数据\n  },\n  \"message\": \"操作成功\" // 可选\n}\n```\n\n### 分页响应\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"items\": [...],\n    \"pagination\": {\n      \"page\": 1,\n      \"pages\": 10,\n      \"per_page\": 20,\n      \"total\": 200\n    }\n  }\n}\n```\n\n### 错误响应\n```json\n{\n  \"success\": false,\n  \"error\": \"错误信息\",\n  \"code\": \"ERROR_CODE\" // 可选\n}\n```\n\n---\n\n## 🔒 权限说明\n\n- **@login_required**: 需要登录\n- **@admin_required**: 需要管理员权限\n- **@staff_required**: 需要员工权限\n- **@finance_required**: 需要财务权限\n- **@customer_service_required**: 需要客服权限\n\n---\n\n## 📈 新增统计接口总结\n\n本次为Vue3管理后台新增了以下统计接口：\n\n1. **文章统计**: `/admin/api/articles/stats`\n2. **用户统计**: `/admin/api/users/stats`\n3. **标签统计**: `/admin/api/tags/stats`\n4. **塔罗师统计**: `/admin/api/authors/stats`\n5. **公告统计**: `/admin/api/announcements/stats`\n6. **工单统计**: `/admin/api/tickets/stats`\n7. **上传日志统计**: `/admin/api/uploader/logs/stats`\n8. **系统设置**: `/admin/api/system/settings`\n9. **价格设置**: `/admin/api/system/prices`\n10. **数据统计概览**: `/admin/api/statistics/overview`\n\n这些接口为每个管理页面提供了丰富的统计数据支持，实现了完整的数据驱动管理功能。\n\n---\n\n## 📋 **2025-08-06 新增接口** ⭐ 最新更新\n\n### **媒体文件管理接口** ⭐ 新增\n- **接口**: `GET /admin/api/articles/{id}/media`\n- **描述**: 获取文章媒体文件列表（智能检测）\n- **权限**: @login_required + @staff_required\n- **功能**:\n  - 优先从MediaFile表查找\n  - 降级从文章字段提取（audio_sync_data、content_images、cover_url）\n  - 生成虚拟文件记录\n- **响应**:\n```json\n{\n  \"success\": true,\n  \"data\": {\n    \"audio\": [\n      {\n        \"id\": \"virtual_drRFt_uuaX8_audio\",\n        \"filename\": \"drRFt_uuaX8_audio.mp3\",\n        \"file_type\": \"audio\",\n        \"file_category\": \"original_audio\",\n        \"url\": \"https://r2-domain.com/audio/file.mp3\",\n        \"upload_status\": \"uploaded\"\n      }\n    ],\n    \"subtitle\": [\n      {\n        \"id\": \"virtual_drRFt_uuaX8_subtitle\",\n        \"filename\": \"drRFt_uuaX8_subtitle.srt\",\n        \"file_type\": \"subtitle\",\n        \"file_category\": \"subtitle\",\n        \"url\": \"https://r2-domain.com/subtitles/file.srt\"\n      }\n    ],\n    \"image\": [...],\n    \"cover\": [...]\n  }\n}\n```\n\n- **接口**: `POST /admin/api/articles/{id}/media/upload`\n- **描述**: 上传媒体文件\n- **权限**: @login_required + @staff_required\n- **请求**: FormData with file, file_type, file_category\n\n- **接口**: `DELETE /admin/api/media/{file_id}`\n- **描述**: 删除媒体文件\n- **权限**: @login_required + @staff_required\n\n### **公告管理CRUD接口** ⭐ 完善实现\n- **接口**: `GET /admin/api/announcements`\n- **接口**: `POST /admin/api/announcements`\n- **接口**: `PUT /admin/api/announcements/{id}`\n- **接口**: `DELETE /admin/api/announcements/{id}`\n- **接口**: `GET /admin/api/announcements/{id}`\n- **接口**: `GET /admin/api/announcements/stats`\n- **描述**: 完整的公告管理CRUD操作，支持创建、编辑、查看、删除\n- **权限**: @login_required + @admin_required\n\n### **塔罗师文章管理** ⭐ 功能完善\n- **前端功能**: 塔罗师列表页面的文章查看模态框\n- **统计数据**: 总文章数、已发布、草稿、总阅读量\n- **操作功能**: 编辑文章、查看购买者、删除文章\n- **筛选功能**: 按标题、状态、类型筛选\n\n### **前端媒体管理界面** ⭐ 新增\n- **音频文件管理**: 显示、上传、删除音频文件\n- **字幕文件管理**: 显示、上传、删除字幕文件（SRT/VTT/ASS）\n- **图鉴文件管理**: 网格显示、批量上传、复制URL、删除\n- **封面文件管理**: 显示、上传、删除封面文件\n\n### **智能媒体检测机制** ⭐ 核心功能\n- **检测优先级**: MediaFile表 → 文章字段提取 → 虚拟记录生成\n- **支持字段**:\n  - `audio_sync_data` → 音频和字幕文件\n  - `content_images` → 图鉴文件\n  - `cover_url` → 封面文件\n  - `original_audio_url` → 原始音频文件\n- **向后兼容**: 完全兼容现有数据，平滑过渡\n\n---\n\n## 🔧 **接口使用说明**\n\n### **媒体文件智能检测流程**\n1. **API调用**: `GET /admin/api/articles/{id}/media`\n2. **检测逻辑**:\n   ```python\n   # 1. 优先查找MediaFile表记录\n   media_files = MediaFile.query.filter_by(article_id=article_id).all()\n\n   # 2. 如果没有字幕记录，从audio_sync_data提取\n   if not subtitle_files and article.audio_sync_data:\n       sync_data = json.loads(article.audio_sync_data)\n       subtitle_url = sync_data.get('subtitle_url')\n       # 生成虚拟字幕记录\n\n   # 3. 如果没有图片记录，从content_images提取\n   if not image_files and article.content_images:\n       image_urls = json.loads(article.content_images)\n       # 生成虚拟图片记录\n   ```\n\n### **公告管理使用流程**\n1. **列表查看**: `GET /admin/api/announcements`\n2. **创建公告**: `POST /admin/api/announcements`\n3. **编辑公告**: `PUT /admin/api/announcements/{id}`\n4. **删除公告**: `DELETE /admin/api/announcements/{id}`\n5. **统计数据**: `GET /admin/api/announcements/stats`\n\n### **前端组件使用**\n- **塔罗师管理**: `/admin/vue/authors` → 点击\"查看文章\"\n- **公告管理**: `/admin/vue/announcements` → 完整CRUD操作\n- **文章编辑**: `/admin/vue/articles/{id}/edit` → 媒体文件管理\n\n---\n\n## 📊 **数据格式规范**\n\n### **虚拟文件记录格式**\n```json\n{\n  \"id\": \"virtual_{original_id}_{type}_{index}\",\n  \"filename\": \"{original_id}_{type}_{index}.ext\",\n  \"file_type\": \"audio|image|subtitle\",\n  \"file_category\": \"original_audio|cover|content|subtitle\",\n  \"url\": \"https://r2-domain.com/path/to/file\",\n  \"upload_status\": \"uploaded\",\n  \"created_at\": \"2025-07-11T21:38:25.000622\"\n}\n```\n\n### **公告数据格式**\n```json\n{\n  \"id\": 1,\n  \"title\": \"公告标题\",\n  \"content\": \"公告内容\",\n  \"priority\": \"low|normal|high|urgent\",\n  \"start_date\": \"2025-08-05T10:00:00\",\n  \"end_date\": \"2025-09-05T10:00:00\",\n  \"is_active\": true,\n  \"created_at\": \"2025-08-05T10:00:00\"\n}\n```\n", "modifiedCode": "![alt text](image.png)"}