var M=(w,V,h)=>new Promise((v,m)=>{var d=r=>{try{y(h.next(r))}catch(x){m(x)}},p=r=>{try{y(h.throw(r))}catch(x){m(x)}},y=r=>r.done?v(r.value):Promise.resolve(r.value).then(d,p);y((h=h.apply(w,V)).next())});import{d as K,i as C,m as Z,k as W,c as l,a as t,t as o,g as A,n as c,F as U,l as S,p as q,s as k,b as T,e as X,z as tt,y as et,o as i}from"./index-Cr1r-Y_5.js";import{_ as at}from"./_plugin-vue_export-helper-DlAUqK2U.js";const nt={class:"user-detail"},ot={class:"page-header"},st={class:"page-title"},lt={key:0,class:"loading-container"},it={key:1,class:"error-container"},rt={class:"text-red-400"},pt={key:2,class:"space-y-6"},dt={class:"info-card"},ct={class:"user-info-grid"},ut={class:"info-item"},_t={class:"info-item"},mt={class:"info-item"},yt={class:"info-item"},gt={key:1,class:"vip-badge vip-premium"},vt={class:"info-card"},bt={class:"stats-grid"},ft={class:"stat-item"},ht={class:"stat-value text-green-400"},xt={class:"stat-item"},qt={class:"stat-value text-blue-400"},kt={class:"stat-item"},Tt={class:"stat-value text-purple-400"},Ct={class:"stat-item"},Vt={class:"stat-value text-pink-400"},Zt={class:"stat-item"},It={class:"stat-value text-red-400"},Pt={class:"stat-item"},Dt={class:"stat-value text-yellow-400"},Mt={class:"info-card"},Ut={class:"payment-stats-grid"},St={class:"stat-item"},wt={class:"stat-value text-green-400"},Ft={class:"stat-item"},Nt={class:"stat-value text-cyan-400"},$t={class:"stat-item"},At={class:"stat-value text-orange-400"},Bt={class:"info-card"},Rt={class:"table-container"},Et={class:"data-table"},Ot={class:"text-green-400"},jt={class:"text-red-400"},zt={key:1},Lt={class:"info-card"},Qt={class:"filter-form"},Yt={class:"form-group"},Gt={class:"form-group"},Ht={class:"form-group"},Jt={class:"info-card"},Kt={class:"card-title"},Wt={key:0,class:"loading-container"},Xt={key:1,class:"no-data-container"},te={key:2,class:"table-container"},ee={class:"data-table"},ae={class:"text-sm"},ne={class:"text-sm"},oe={class:"text-sm"},se={key:0,class:"article-link"},le={key:1,class:"text-gray-400"},ie={key:1,class:"text-gray-400"},re={key:2,class:"text-gray-400"},pe={key:0},de={key:1,class:"text-gray-400"},ce={key:0},ue={class:"text-xs"},_e={key:1,class:"text-gray-400"},me={key:0},ye={key:1,class:"text-gray-400"},ge={class:"text-gray-300"},ve={key:0},be={key:1,class:"text-gray-400"},fe={class:"text-yellow-400 font-medium"},he={key:0},xe={key:1,class:"text-gray-400"},qe={class:"text-xs text-gray-400"},ke={key:3,class:"pagination"},Te=["disabled"],Ce={class:"page-numbers"},Ve=["onClick"],Ze=["disabled"],Ie={class:"info-card"},Pe={class:"filter-form"},De={class:"form-group"},Me={class:"form-group"},Ue={class:"form-group"},Se=K({__name:"UserDetailView",setup(w){const V=tt(),h=et(),v=C(!0),m=C(""),d=C({}),p=Z({total_spent:0,total_purchases:0,basic_purchases:0,premium_purchases:0,total_consumption:0,current_balance:0,cash_payments:0,quota_payments:0,points_payments:0}),y=C([]),r=C([]),x=Z({loadTransactionsCalled:!1,loadUserDetailCalled:!1,mountedCalled:!1,mockDataLength:0}),s=Z({record_type:"",time_range:"",content_type:"",page:1,per_page:20}),u=Z({page:1,pages:1,total:0,per_page:20,has_prev:!1,has_next:!1,prev_num:null,next_num:null}),F=n=>{if(!n)return"无";try{const e=new Date(n);return isNaN(e.getTime())?"无效日期":e.toLocaleString("zh-CN")}catch(e){return console.error("formatDateTime error:",e),"格式错误"}},B=n=>{try{return!n||n===0?"免费用户":n===2?"高级VIP":"初级VIP"}catch(e){return console.error("getVipStatus error:",e),"未知"}},R=n=>{try{return!n||n===0?"vip-free":n===2?"vip-premium":"vip-basic"}catch(e){return console.error("getVipClass error:",e),"vip-free"}},E=n=>({purchase:"购买",consumption:"消费",quota_consumption:"配额消费",quota_purchase:"配额购买",points_earned:"积分获得",points_consumption:"积分消费",points_used:"积分消费",subscription:"订阅",recharge:"充值",refund:"退款",quota_reset:"配额重置",vip_upgrade:"VIP升级",vip_expire:"VIP到期"})[n]||n,O=n=>({basic:"基础内容",premium:"高级内容",vip:"VIP服务",recharge:"充值",subscription:"订阅服务",quota_pack:"配额包"})[n]||n,j=n=>({quota:"配额",points:"积分",alipay:"支付宝",wechat:"微信",mock:"模拟支付",stripe:"Stripe",balance:"余额"})[n]||n,z=n=>({quota:"配额",points:"积分",balance:"余额"})[n]||n,L=n=>n>0?"text-green-400":n<0?"text-red-400":"text-gray-400",Q=n=>n>0?"text-green-400":n<0?"text-red-400":"text-gray-400",Y=n=>n>0?"text-green-400":n<0?"text-red-400":"text-gray-400",G=()=>{const n=[],e=u.page,g=u.pages,a=Math.max(1,e-2),b=Math.min(g,e+2);for(let f=a;f<=b;f++)n.push(f);return n},N=()=>M(this,null,function*(){v.value=!0,m.value="";try{const e={success:!0,user:{id:V.params.id,username:"testuser",email:"<EMAIL>",created_at:"2024-01-01T00:00:00Z",finance:{vip_level:2,vip_expire_at:"2024-12-31T00:00:00Z"}},finance_stats:{total_spent:5e4,total_purchases:25,basic_purchases:15,premium_purchases:10,total_consumption:45e3,current_balance:5e3,cash_payments:20,quota_payments:3,points_payments:2},monthly_data:[{month:"2024-01",purchases:1e4,consumption:8e3,net:2e3},{month:"2024-02",purchases:15e3,consumption:12e3,net:3e3},{month:"2024-03",purchases:8e3,consumption:1e4,net:-2e3}]};d.value=e.user,Object.assign(p,e.finance_stats),y.value=e.monthly_data,yield _()}catch(n){m.value="加载用户详情失败: "+n.message}finally{v.value=!1,console.log("🐛 loadUserDetail 完成，loading:",v.value,"error:",m.value)}}),_=()=>M(this,null,function*(){try{x.loadTransactionsCalled=!0,console.log("🐛 loadTransactions 被调用了!");const n=V.params.id,e=new URLSearchParams({page:s.page.toString(),per_page:s.per_page.toString(),record_type:s.record_type||"",time_range:s.time_range||"",content_type:s.content_type||""});console.log("🐛 查询参数:",e.toString());const g=[{id:1,type:"purchase",date:"2024-01-01T10:00:00Z",description:"支付宝充值",article_title:null,content_type:"recharge",payment_method:"alipay",amount:2e4,quota_change:0,quota_type:null,points_change:200,balance_before:0,balance_after:2e4,transaction_id:"ALI_20240101_001"},{id:2,type:"purchase",date:"2024-01-02T09:15:00Z",description:"购买VIP1个月",article_title:null,content_type:"vip",payment_method:"balance",amount:-12800,quota_change:0,quota_type:null,points_change:0,balance_before:2e4,balance_after:7200,transaction_id:"VIP_20240102_001"},{id:3,type:"purchase",date:"2024-01-03T14:20:00Z",description:"购买文章: 塔罗牌高级解读技巧",article_title:"塔罗牌高级解读技巧",content_type:"premium",payment_method:"balance",amount:-1e3,quota_change:0,quota_type:null,points_change:10,balance_before:7200,balance_after:6200,transaction_id:"ART_20240103_001"},{id:4,type:"consumption",date:"2024-01-04T16:45:00Z",description:"消费: 爱情塔罗牌阵详解",article_title:"爱情塔罗牌阵详解",content_type:"premium",payment_method:null,cost_type:"quota",amount:0,quota_change:-1,quota_type:"premium",points_change:0,balance_before:6200,balance_after:6200,transaction_id:null},{id:5,type:"consumption",date:"2024-01-05T11:30:00Z",description:"消费: 塔罗牌入门指南",article_title:"塔罗牌入门指南",content_type:"basic",payment_method:null,cost_type:"balance",amount:-500,quota_change:0,quota_type:null,points_change:0,balance_before:6200,balance_after:5700,transaction_id:null},{id:6,type:"consumption",date:"2024-01-06T13:15:00Z",description:"消费: 塔罗牌牌意解析",article_title:"塔罗牌牌意解析",content_type:"basic",payment_method:null,cost_type:"points",amount:0,quota_change:0,quota_type:null,points_change:-50,balance_before:5700,balance_after:5700,transaction_id:null},{id:7,type:"points_earned",date:"2024-01-07T08:00:00Z",description:"每日签到奖励",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:10,balance_before:5700,balance_after:5700,transaction_id:null},{id:8,type:"quota_reset",date:"2024-01-08T00:00:00Z",description:"VIP配额月度重置",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:10,quota_type:"basic",points_change:0,balance_before:5700,balance_after:5700,transaction_id:null},{id:9,type:"refund",date:"2024-01-09T15:30:00Z",description:"文章购买退款: 塔罗牌高级解读技巧",article_title:"塔罗牌高级解读技巧",content_type:"premium",payment_method:"balance",amount:1e3,quota_change:0,quota_type:null,points_change:-10,balance_before:5700,balance_after:6700,transaction_id:"REF_20240109_001"},{id:10,type:"subscription",date:"2024-01-10T12:00:00Z",description:"订阅塔罗师Alice",article_title:null,content_type:"subscription",payment_method:"balance",amount:-2e3,quota_change:0,quota_type:null,points_change:20,balance_before:6700,balance_after:4700,transaction_id:"SUB_20240110_001"},{id:11,type:"points_consumption",date:"2024-01-11T09:30:00Z",description:"积分兑换高级配额",article_title:null,content_type:null,payment_method:null,cost_type:"points",amount:0,quota_change:5,quota_type:"premium",points_change:-500,balance_before:4700,balance_after:4700,transaction_id:null},{id:12,type:"points_earned",date:"2024-01-12T08:00:00Z",description:"每日签到奖励",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:10,balance_before:4700,balance_after:4700,transaction_id:null},{id:13,type:"points_earned",date:"2024-01-13T14:20:00Z",description:"推荐新用户奖励",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:100,balance_before:4700,balance_after:4700,transaction_id:null},{id:14,type:"quota_consumption",date:"2024-01-14T16:45:00Z",description:"查看高级塔罗解读",article_title:"深度塔罗牌解读：爱情运势",content_type:"premium",payment_method:null,cost_type:"quota",amount:0,quota_change:-1,quota_type:"premium",points_change:0,balance_before:4700,balance_after:4700,transaction_id:null},{id:15,type:"quota_reset",date:"2024-02-01T00:00:00Z",description:"VIP配额月度重置",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:20,quota_type:"premium",points_change:0,balance_before:4700,balance_after:4700,transaction_id:null},{id:16,type:"points_consumption",date:"2024-02-02T11:15:00Z",description:"积分购买文章",article_title:"塔罗牌占卜技巧大全",content_type:"basic",payment_method:null,cost_type:"points",amount:0,quota_change:0,quota_type:null,points_change:-80,balance_before:4700,balance_after:4700,transaction_id:null},{id:17,type:"points_earned",date:"2024-02-03T15:30:00Z",description:"优质评论奖励",article_title:"塔罗牌占卜技巧大全",content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:20,balance_before:4700,balance_after:4700,transaction_id:null},{id:18,type:"quota_consumption",date:"2024-02-04T10:20:00Z",description:"查看基础塔罗内容",article_title:"塔罗牌基础知识入门",content_type:"basic",payment_method:null,cost_type:"quota",amount:0,quota_change:-1,quota_type:"basic",points_change:0,balance_before:4700,balance_after:4700,transaction_id:null},{id:19,type:"points_earned",date:"2024-02-05T18:00:00Z",description:"春节活动奖励",article_title:null,content_type:null,payment_method:null,cost_type:null,amount:0,quota_change:0,quota_type:null,points_change:200,balance_before:4700,balance_after:4700,transaction_id:"EVENT_CNY_2024"},{id:20,type:"quota_purchase",date:"2024-02-06T13:45:00Z",description:"购买高级配额包",article_title:null,content_type:"quota_pack",payment_method:"balance",amount:-1500,quota_change:10,quota_type:"premium",points_change:15,balance_before:4700,balance_after:3200,transaction_id:"QUOTA_20240206_001"}];console.log("🐛 模拟数据长度:",g.length),x.mockDataLength=g.length;let a=g;s.record_type&&(a=a.filter(D=>D.type===s.record_type)),s.content_type&&(a=a.filter(D=>D.content_type===s.content_type)),console.log("🐛 过滤后数据长度:",a.length);const b=a.length,f=Math.ceil(b/s.per_page),$=(s.page-1)*s.per_page,J=$+s.per_page,P=a.slice($,J);console.log("🐛 分页后数据长度:",P.length),console.log("🐛 第一条记录:",P[0]),r.value=P,console.log("🐛 transactions.value 设置完成，长度:",r.value.length),Object.assign(u,{page:s.page,pages:f,total:b,per_page:s.per_page,has_prev:s.page>1,has_next:s.page<f,prev_num:s.page>1?s.page-1:null,next_num:s.page<f?s.page+1:null})}catch(n){console.error("加载交易记录失败:",n)}}),I=n=>{n&&n!==s.page&&(s.page=n,_())},H=()=>{h.push("/users")};return W(()=>{N(),_()}),(n,e)=>{var g;return i(),l("div",nt,[t("div",ot,[t("h1",st,"用户财务详情 - "+o(d.value.username||"加载中..."),1),t("div",{class:"header-actions"},[t("button",{class:"btn-secondary",onClick:H},e[8]||(e[8]=[t("i",{class:"fas fa-arrow-left mr-2"},null,-1),A("返回用户列表 ",-1)]))])]),v.value?(i(),l("div",lt,e[9]||(e[9]=[t("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400"},null,-1),t("p",{class:"text-gray-400 mt-2"},"加载中...",-1)]))):m.value?(i(),l("div",it,[e[10]||(e[10]=t("i",{class:"fas fa-exclamation-triangle text-4xl text-red-400 mb-4"},null,-1)),t("p",rt,o(m.value),1),t("button",{class:"btn-primary mt-4",onClick:N},"重试")])):(i(),l("div",pt,[t("div",dt,[e[15]||(e[15]=t("h2",{class:"card-title"},"用户信息",-1)),t("div",ct,[t("div",ut,[e[11]||(e[11]=t("label",null,"用户名",-1)),t("p",null,o(d.value.username||"testuser"),1)]),t("div",_t,[e[12]||(e[12]=t("label",null,"邮箱",-1)),t("p",null,o(d.value.email||"<EMAIL>"),1)]),t("div",mt,[e[13]||(e[13]=t("label",null,"注册时间",-1)),t("p",null,o(d.value.created_at?F(d.value.created_at):"2024/1/1 08:00:00"),1)]),t("div",yt,[e[14]||(e[14]=t("label",null,"VIP等级",-1)),(g=d.value.finance)!=null&&g.vip_level?(i(),l("span",{key:0,class:c(["vip-badge",R(d.value.finance.vip_level)])},o(B(d.value.finance.vip_level)),3)):(i(),l("span",gt,"高级VIP"))])])]),t("div",vt,[e[22]||(e[22]=t("h2",{class:"card-title"},"财务统计概览",-1)),t("div",bt,[t("div",ft,[t("div",ht,"¥"+o(((p.total_spent||5e4)/100).toFixed(2)),1),e[16]||(e[16]=t("div",{class:"stat-label"},"总购买金额",-1))]),t("div",xt,[t("div",qt,o(p.total_purchases||25),1),e[17]||(e[17]=t("div",{class:"stat-label"},"购买次数",-1))]),t("div",kt,[t("div",Tt,o(p.basic_purchases||15),1),e[18]||(e[18]=t("div",{class:"stat-label"},"基础内容",-1))]),t("div",Ct,[t("div",Vt,o(p.premium_purchases||10),1),e[19]||(e[19]=t("div",{class:"stat-label"},"高级内容",-1))]),t("div",Zt,[t("div",It,"¥"+o(((p.total_consumption||45e3)/100).toFixed(2)),1),e[20]||(e[20]=t("div",{class:"stat-label"},"总消费金额",-1))]),t("div",Pt,[t("div",Dt,"¥"+o(((p.current_balance||5e3)/100).toFixed(2)),1),e[21]||(e[21]=t("div",{class:"stat-label"},"当前余额",-1))])])]),t("div",Mt,[e[26]||(e[26]=t("h2",{class:"card-title"},"支付方式统计",-1)),t("div",Ut,[t("div",St,[t("div",wt,o(p.cash_payments||20),1),e[23]||(e[23]=t("div",{class:"stat-label"},"现金支付",-1))]),t("div",Ft,[t("div",Nt,o(p.quota_payments||3),1),e[24]||(e[24]=t("div",{class:"stat-label"},"配额支付",-1))]),t("div",$t,[t("div",At,o(p.points_payments||2),1),e[25]||(e[25]=t("div",{class:"stat-label"},"积分支付",-1))])])]),t("div",Bt,[e[29]||(e[29]=t("h2",{class:"card-title"},"月度财务统计",-1)),t("div",Rt,[t("table",Et,[e[28]||(e[28]=t("thead",null,[t("tr",null,[t("th",null,"月份"),t("th",null,"购买金额"),t("th",null,"消费金额"),t("th",null,"净收支")])],-1)),t("tbody",null,[y.value&&y.value.length>0?(i(!0),l(U,{key:0},S(y.value.slice(0,12),(a,b)=>(i(),l("tr",{key:`month-${b}`},[t("td",null,o(a.month||"无"),1),t("td",Ot,"¥"+o(((a.purchases||0)/100).toFixed(2)),1),t("td",jt,"¥"+o(((a.consumption||0)/100).toFixed(2)),1),t("td",{class:c((a.net||0)>=0?"text-green-400":"text-red-400")}," ¥"+o(((a.net||0)/100).toFixed(2)),3)]))),128)):(i(),l("tr",zt,e[27]||(e[27]=[t("td",{colspan:"4",class:"text-center text-gray-400"},"暂无月度数据",-1)])))])])])]),t("div",Lt,[e[36]||(e[36]=t("h2",{class:"card-title"},"财务记录筛选",-1)),t("div",Qt,[t("div",Yt,[e[31]||(e[31]=t("label",null,"记录类型",-1)),q(t("select",{"onUpdate:modelValue":e[0]||(e[0]=a=>s.record_type=a),class:"form-select",onChange:_},e[30]||(e[30]=[T('<option value="" data-v-61e4dbf2>全部记录</option><option value="purchase" data-v-61e4dbf2>购买记录</option><option value="consumption" data-v-61e4dbf2>消费记录</option><option value="quota" data-v-61e4dbf2>配额使用</option><option value="points" data-v-61e4dbf2>积分记录</option><option value="subscription" data-v-61e4dbf2>订阅记录</option>',6)]),544),[[k,s.record_type]])]),t("div",Gt,[e[33]||(e[33]=t("label",null,"时间范围",-1)),q(t("select",{"onUpdate:modelValue":e[1]||(e[1]=a=>s.time_range=a),class:"form-select",onChange:_},e[32]||(e[32]=[T('<option value="" data-v-61e4dbf2>全部时间</option><option value="today" data-v-61e4dbf2>今天</option><option value="week" data-v-61e4dbf2>最近一周</option><option value="month" data-v-61e4dbf2>最近一月</option><option value="quarter" data-v-61e4dbf2>最近三月</option>',5)]),544),[[k,s.time_range]])]),t("div",Ht,[e[35]||(e[35]=t("label",null,"内容类型",-1)),q(t("select",{"onUpdate:modelValue":e[2]||(e[2]=a=>s.content_type=a),class:"form-select",onChange:_},e[34]||(e[34]=[T('<option value="" data-v-61e4dbf2>全部类型</option><option value="basic" data-v-61e4dbf2>基础内容</option><option value="premium" data-v-61e4dbf2>高级内容</option><option value="vip" data-v-61e4dbf2>VIP服务</option><option value="recharge" data-v-61e4dbf2>充值</option>',5)]),544),[[k,s.content_type]])])])]),t("div",Jt,[t("h2",Kt,"详细财务流水（原始单据）- 共"+o(r.value.length)+"条记录",1),v.value?(i(),l("div",Wt,e[37]||(e[37]=[t("i",{class:"fas fa-spinner fa-spin text-2xl text-gray-400"},null,-1),t("p",{class:"text-gray-400 mt-2"},"加载中...",-1)]))):r.value.length===0?(i(),l("div",Xt,e[38]||(e[38]=[t("i",{class:"fas fa-receipt text-4xl text-gray-600 mb-4"},null,-1),t("p",{class:"text-gray-400"},"暂无财务记录",-1)]))):(i(),l("div",te,[t("table",ee,[e[39]||(e[39]=t("thead",null,[t("tr",null,[t("th",null,"时间"),t("th",null,"类型"),t("th",null,"描述"),t("th",null,"文章/服务"),t("th",null,"内容类型"),t("th",null,"支付方式"),t("th",null,"金额变动"),t("th",null,"配额变动"),t("th",null,"积分变动"),t("th",null,"交易前余额"),t("th",null,"交易后余额"),t("th",null,"交易ID")])],-1)),t("tbody",null,[(i(!0),l(U,null,S(r.value,a=>(i(),l("tr",{key:`${a.type}-${a.id}`},[t("td",ae,o(F(a.date)),1),t("td",null,[t("span",{class:c(["type-badge",`type-${a.type}`])},o(E(a.type)),3)]),t("td",ne,o(a.description),1),t("td",oe,[a.article_title?(i(),l("span",se,o(a.article_title),1)):(i(),l("span",le,"-"))]),t("td",null,[a.content_type?(i(),l("span",{key:0,class:c(["content-badge",`content-${a.content_type}`])},o(O(a.content_type)),3)):(i(),l("span",ie,"-"))]),t("td",null,[a.payment_method?(i(),l("span",{key:0,class:c(["payment-badge",`payment-${a.payment_method}`])},o(j(a.payment_method)),3)):a.cost_type?(i(),l("span",{key:1,class:c(["payment-badge",`payment-${a.cost_type}`])},o(z(a.cost_type)),3)):(i(),l("span",re,"-"))]),t("td",{class:c(L(a.amount))},[a.amount!==0?(i(),l("span",pe,o(a.amount>0?"+":"")+"¥"+o((Math.abs(a.amount)/100).toFixed(2)),1)):(i(),l("span",de,"-"))],2),t("td",{class:c(Q(a.quota_change))},[a.quota_change?(i(),l("span",ce,[A(o(a.quota_change>0?"+":"")+o(a.quota_change)+" ",1),t("span",ue,"("+o(a.quota_type)+")",1)])):(i(),l("span",_e,"-"))],2),t("td",{class:c(Y(a.points_change))},[a.points_change?(i(),l("span",me,o(a.points_change>0?"+":"")+o(a.points_change),1)):(i(),l("span",ye,"-"))],2),t("td",ge,[a.balance_before!==null?(i(),l("span",ve," ¥"+o((a.balance_before/100).toFixed(2)),1)):(i(),l("span",be,"-"))]),t("td",fe,[a.balance_after!==null?(i(),l("span",he," ¥"+o((a.balance_after/100).toFixed(2)),1)):(i(),l("span",xe,"-"))]),t("td",qe,o(a.transaction_id||"-"),1)]))),128))])])])),u.pages>1?(i(),l("div",ke,[t("button",{class:"btn-secondary",disabled:!u.has_prev,onClick:e[3]||(e[3]=a=>I(u.prev_num))}," 上一页 ",8,Te),t("div",Ce,[(i(!0),l(U,null,S(G(),a=>(i(),l("button",{key:a,class:c(["page-btn",a===u.page?"active":""]),onClick:b=>I(a)},o(a),11,Ve))),128))]),t("button",{class:"btn-secondary",disabled:!u.has_next,onClick:e[4]||(e[4]=a=>I(u.next_num))}," 下一页 ",8,Ze)])):X("",!0)]),t("div",Ie,[e[46]||(e[46]=t("h2",{class:"card-title"},"财务记录筛选",-1)),t("div",Pe,[t("div",De,[e[41]||(e[41]=t("label",null,"记录类型",-1)),q(t("select",{"onUpdate:modelValue":e[5]||(e[5]=a=>s.record_type=a),class:"form-select",onChange:_},e[40]||(e[40]=[T('<option value="" data-v-61e4dbf2>全部记录</option><option value="purchase" data-v-61e4dbf2>购买记录</option><option value="consumption" data-v-61e4dbf2>消费记录</option><option value="quota" data-v-61e4dbf2>配额使用</option><option value="points" data-v-61e4dbf2>积分记录</option><option value="subscription" data-v-61e4dbf2>订阅记录</option>',6)]),544),[[k,s.record_type]])]),t("div",Me,[e[43]||(e[43]=t("label",null,"时间范围",-1)),q(t("select",{"onUpdate:modelValue":e[6]||(e[6]=a=>s.time_range=a),class:"form-select",onChange:_},e[42]||(e[42]=[T('<option value="" data-v-61e4dbf2>全部时间</option><option value="today" data-v-61e4dbf2>今天</option><option value="week" data-v-61e4dbf2>最近一周</option><option value="month" data-v-61e4dbf2>最近一月</option><option value="quarter" data-v-61e4dbf2>最近三月</option>',5)]),544),[[k,s.time_range]])]),t("div",Ue,[e[45]||(e[45]=t("label",null,"内容类型",-1)),q(t("select",{"onUpdate:modelValue":e[7]||(e[7]=a=>s.content_type=a),class:"form-select",onChange:_},e[44]||(e[44]=[T('<option value="" data-v-61e4dbf2>全部类型</option><option value="basic" data-v-61e4dbf2>基础内容</option><option value="premium" data-v-61e4dbf2>高级内容</option><option value="vip" data-v-61e4dbf2>VIP服务</option><option value="recharge" data-v-61e4dbf2>充值</option>',5)]),544),[[k,s.content_type]])])])])]))])}}}),$e=at(Se,[["__scopeId","data-v-61e4dbf2"]]);export{$e as default};
