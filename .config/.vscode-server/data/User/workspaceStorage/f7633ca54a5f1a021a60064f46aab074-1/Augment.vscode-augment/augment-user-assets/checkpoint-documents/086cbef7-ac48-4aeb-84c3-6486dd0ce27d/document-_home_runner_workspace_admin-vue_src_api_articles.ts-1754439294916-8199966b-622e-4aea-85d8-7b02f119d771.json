{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "admin-vue/src/api/articles.ts"}, "originalCode": "import { http } from '@/utils/request'\nimport type { ApiResponse, Article, PaginatedResponse, PaginationParams } from '@/types'\n\nexport interface ArticleListParams extends PaginationParams {\n  search?: string\n  author_id?: number\n  tag_id?: number\n  is_published?: boolean\n  is_premium?: boolean\n  sort_by?: string\n  sort_order?: 'asc' | 'desc'\n}\n\nexport interface ArticleCreateParams {\n  title: string\n  content: string\n  summary?: string\n  author_id: number\n  tag_ids?: number[]\n  is_published?: boolean\n  is_premium?: boolean\n  price?: number\n  cover_image?: string\n}\n\nexport interface ArticleUpdateParams extends Partial<ArticleCreateParams> {\n  id: number\n}\n\nexport interface ArticleStats {\n  total_articles: number\n  published_articles: number\n  premium_articles: number\n  total_views: number\n  total_likes: number\n  today_articles: number\n}\n\nexport const articlesApi = {\n  // 获取文章列表\n  getList(params: ArticleListParams): Promise<ApiResponse<PaginatedResponse<Article>>> {\n    return http.get('/articles', { params })\n  },\n\n  // 获取文章详情\n  getDetail(id: number): Promise<ApiResponse<Article>> {\n    return http.get(`/articles/${id}`)\n  },\n\n  // 创建文章\n  create(data: ArticleCreateParams): Promise<ApiResponse<Article>> {\n    return http.post('/articles', data)\n  },\n\n  // 更新文章\n  update(id: number, data: Partial<ArticleCreateParams>): Promise<ApiResponse<Article>> {\n    return http.put(`/articles/${id}`, data)\n  },\n\n  // 删除文章\n  delete(id: number): Promise<ApiResponse> {\n    return http.delete(`/articles/${id}`)\n  },\n\n  // 批量删除文章\n  batchDelete(ids: number[]): Promise<ApiResponse> {\n    return http.post('/articles/batch_delete', { ids })\n  },\n\n  // 更新文章状态\n  updateStatus(id: number, status: string): Promise<ApiResponse> {\n    return http.put(`/articles/${id}/status`, { status })\n  },\n\n  // 发布/取消发布文章\n  togglePublish(id: number, is_published: boolean): Promise<ApiResponse> {\n    return http.put(`/articles/${id}/publish`, { is_published })\n  },\n\n  // 设置/取消精品文章\n  togglePremium(id: number, is_premium: boolean): Promise<ApiResponse> {\n    return http.put(`/articles/${id}/premium`, { is_premium })\n  },\n\n  // 获取文章统计\n  getStats(): Promise<ApiResponse<ArticleStats>> {\n    return http.get('/articles/stats')\n  },\n\n  // 上传文章封面\n  uploadCover(file: File): Promise<ApiResponse<{ url: string }>> {\n    const formData = new FormData()\n    formData.append('file', file)\n    return http.post('/articles/upload-cover', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    })\n  },\n\n  // 获取文章标签建议\n  getTagSuggestions(query: string): Promise<ApiResponse<string[]>> {\n    return http.get('/articles/tag-suggestions', { params: { q: query } })\n  },\n\n  // 获取文章购买者列表\n  getBuyers(articleId: number): Promise<ApiResponse<{\n    article: { id: number; title: string; status: string; created_at: string }\n    buyers: Array<{\n      // 基础信息\n      purchase_id: number\n      user_id: number\n      username: string\n      email: string\n      content_type: string\n      purchase_time: string\n\n      // 支付信息\n      amount_paid: number\n      payment_method: string\n      transaction_id: string\n\n      // 积分消费详情\n      points_used: number\n      points_balance_before: number | null\n      points_balance_after: number | null\n      points_record_id: number | null\n\n      // 配额消费详情\n      quota_used: number\n      quota_type: string\n      exchange_used: boolean\n      quota_record_id: number | null\n\n      // 购买时用户状态\n      vip_level_at_purchase: number\n\n      // 消费方式分析\n      cost_type: string\n      cost_amount: number\n      consumption_record_id: number | null\n    }>\n    total_count: number\n  }>> {\n    return http.get(`/articles/${articleId}/buyers`)\n  }\n}\n", "modifiedCode": "import { http } from '@/utils/request'\nimport type { ApiResponse, Article, PaginatedResponse, PaginationParams } from '@/types'\n\nexport interface ArticleListParams extends PaginationParams {\n  search?: string\n  author_id?: number\n  tag_id?: number\n  is_published?: boolean\n  is_premium?: boolean\n  sort_by?: string\n  sort_order?: 'asc' | 'desc'\n}\n\nexport interface ArticleCreateParams {\n  title: string\n  content: string\n  summary?: string\n  author_id: number\n  tag_ids?: number[]\n  is_published?: boolean\n  is_premium?: boolean\n  price?: number\n  cover_image?: string\n}\n\nexport interface ArticleUpdateParams extends Partial<ArticleCreateParams> {\n  id: number\n}\n\nexport interface ArticleStats {\n  total_articles: number\n  published_articles: number\n  premium_articles: number\n  total_views: number\n  total_likes: number\n  today_articles: number\n}\n\nexport const articlesApi = {\n  // 获取文章列表\n  getList(params: ArticleListParams): Promise<ApiResponse<PaginatedResponse<Article>>> {\n    return http.get('/articles', { params })\n  },\n\n  // 获取文章详情\n  getDetail(id: number): Promise<ApiResponse<Article>> {\n    return http.get(`/articles/${id}`)\n  },\n\n  // 创建文章\n  create(data: ArticleCreateParams): Promise<ApiResponse<Article>> {\n    return http.post('/articles', data)\n  },\n\n  // 更新文章\n  update(id: number, data: Partial<ArticleCreateParams>): Promise<ApiResponse<Article>> {\n    return http.put(`/articles/${id}`, data)\n  },\n\n  // 删除文章\n  delete(id: number): Promise<ApiResponse> {\n    return http.delete(`/articles/${id}`)\n  },\n\n  // 批量删除文章\n  batchDelete(ids: number[]): Promise<ApiResponse> {\n    return http.post('/articles/batch_delete', { ids })\n  },\n\n  // 更新文章状态\n  updateStatus(id: number, status: string): Promise<ApiResponse> {\n    return http.put(`/articles/${id}/status`, { status })\n  },\n\n  // 发布/取消发布文章\n  togglePublish(id: number, is_published: boolean): Promise<ApiResponse> {\n    return http.put(`/articles/${id}/publish`, { is_published })\n  },\n\n  // 设置/取消精品文章\n  togglePremium(id: number, is_premium: boolean): Promise<ApiResponse> {\n    return http.put(`/articles/${id}/premium`, { is_premium })\n  },\n\n  // 获取文章统计\n  getStats(): Promise<ApiResponse<ArticleStats>> {\n    return http.get('/articles/stats')\n  },\n\n  // 上传文章封面\n  uploadCover(file: File): Promise<ApiResponse<{ url: string }>> {\n    const formData = new FormData()\n    formData.append('file', file)\n    return http.post('/articles/upload-cover', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    })\n  },\n\n  // 获取文章标签建议\n  getTagSuggestions(query: string): Promise<ApiResponse<string[]>> {\n    return http.get('/articles/tag-suggestions', { params: { q: query } })\n  },\n\n  // 获取文章购买者列表\n  getBuyers(articleId: number): Promise<ApiResponse<{\n    article: { id: number; title: string; status: string; created_at: string }\n    buyers: Array<{\n      // 基础信息\n      purchase_id: number\n      user_id: number\n      username: string\n      email: string\n      content_type: string\n      purchase_time: string\n\n      // 支付信息\n      amount_paid: number\n      payment_method: string\n      transaction_id: string\n\n      // 积分消费详情\n      points_used: number\n      points_balance_before: number | null\n      points_balance_after: number | null\n      points_record_id: number | null\n\n      // 配额消费详情\n      quota_used: number\n      quota_type: string\n      exchange_used: boolean\n      quota_record_id: number | null\n\n      // 购买时用户状态\n      vip_level_at_purchase: number\n\n      // 消费方式分析\n      cost_type: string\n      cost_amount: number\n      consumption_record_id: number | null\n    }>\n    total_count: number\n  }>> {\n    return http.get(`/article-buyers/${articleId}`)\n  }\n}\n"}