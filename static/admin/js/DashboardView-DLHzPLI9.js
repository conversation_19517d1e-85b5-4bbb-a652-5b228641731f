var k=(x,h,c)=>new Promise((u,f)=>{var m=a=>{try{y(c.next(a))}catch(p){f(p)}},D=a=>{try{y(c.throw(a))}catch(p){f(p)}},y=a=>a.done?u(a.value):Promise.resolve(a.value).then(m,D);y((c=c.apply(x,h)).next())});import{h as b,d as A,u as I,i as w,j as M,k as R,c as n,a as t,t as i,f as _,w as g,r as C,F as j,l as $,o as d,g as v}from"./index-Cr1r-Y_5.js";import{_ as V}from"./_plugin-vue_export-helper-DlAUqK2U.js";const S={getStats(){return b.get("/admin/dashboard/stats")},getRecentUsers(x=5){return b.get("/admin/dashboard/recent-users",{params:{limit:x}})},getRecentArticles(x=5){return b.get("/admin/dashboard/recent-articles",{params:{limit:x}})},getDashboardData(){return b.get("/admin/dashboard")}},N={class:"welcome-card mb-6 p-4"},O={class:"flex items-center justify-between"},T={class:"flex items-center"},U={class:"font-semibold text-lg"},B={class:"text-sm"},F={class:"flex space-x-4"},L={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6"},z={class:"stat-card p-4 text-center"},E={class:"text-3xl font-bold text-yellow-400"},Y={class:"stat-card p-4 text-center"},q={class:"text-3xl font-bold text-yellow-400"},G={class:"stat-card p-4 text-center"},H={class:"text-3xl font-bold text-yellow-400"},J={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},K={class:"data-card"},P={class:"p-4 border-b border-gray-600 flex items-center justify-between"},Q={class:"p-4"},W={key:0},X={class:"divide-y divide-gray-600"},Z={class:"flex items-center"},tt={class:"font-medium text-gray-100"},et={class:"text-xs text-gray-400"},st={key:1,class:"text-center py-8 text-gray-400"},at={class:"data-card"},lt={class:"p-4 border-b border-gray-600 flex items-center justify-between"},rt={class:"p-4"},ot={key:0},nt={class:"divide-y divide-gray-600"},it={class:"flex items-center"},dt={class:"font-medium text-gray-100"},ct={class:"text-xs text-gray-400"},ut={key:1,class:"text-center py-8 text-gray-400"},ft=A({__name:"DashboardView",setup(x){const h=I(),c=w(!1),u=w({user_count:0,article_count:0,purchase_count:0}),f=w([]),m=w([]),D=M(()=>h.user),y=()=>{const s=new Date;return`${s.getFullYear()}年${(s.getMonth()+1).toString().padStart(2,"0")}月${s.getDate().toString().padStart(2,"0")}日`},a=s=>{const e=new Date(s),r=Math.floor((new Date().getTime()-e.getTime())/1e3);return r<60?"刚刚":r<3600?`${Math.floor(r/60)}分钟前`:r<86400?`${Math.floor(r/3600)}小时前`:r<2592e3?`${Math.floor(r/86400)}天前`:e.toLocaleDateString("zh-CN")},p=()=>k(this,null,function*(){c.value=!0;try{const s=yield S.getStats();s.success&&(u.value=s.data);const e=yield S.getRecentUsers();e.success&&(f.value=e.data);const l=yield S.getRecentArticles();l.success&&(m.value=l.data)}catch(s){console.error("获取仪表盘数据失败:",s),u.value={user_count:1234,article_count:567,purchase_count:890},f.value=[{id:1,username:"user1",created_at:new Date().toISOString()},{id:2,username:"user2",created_at:new Date(Date.now()-864e5).toISOString()}],m.value=[{id:1,title:"示例文章1",created_at:new Date().toISOString()},{id:2,title:"示例文章2",created_at:new Date(Date.now()-864e5).toISOString()}]}finally{c.value=!1}});return R(()=>{p()}),(s,e)=>{var r;const l=C("router-link");return d(),n("div",null,[t("div",N,[t("div",O,[t("div",T,[e[0]||(e[0]=t("i",{class:"fas fa-user-shield fa-lg mr-3"},null,-1)),t("div",null,[t("p",U,"欢迎回来，"+i(((r=D.value)==null?void 0:r.username)||"admin"),1),t("p",B,"今天是 "+i(y())+"，祝您管理愉快！",1)])]),t("div",F,[_(l,{to:"/articles",class:"btn-primary-white inline-flex items-center"},{default:g(()=>e[1]||(e[1]=[t("i",{class:"fas fa-file-alt mr-2"},null,-1),v("管理文章 ",-1)])),_:1,__:[1]}),_(l,{to:"/users",class:"btn-primary-white inline-flex items-center"},{default:g(()=>e[2]||(e[2]=[t("i",{class:"fas fa-users mr-2"},null,-1),v("用户管理 ",-1)])),_:1,__:[2]})])])]),t("div",L,[t("div",z,[e[3]||(e[3]=t("div",{class:"text-yellow-200 text-sm mb-1"},"用户总数",-1)),t("div",E,i(u.value.user_count||0),1),e[4]||(e[4]=t("div",{class:"mt-2 text-yellow-500"},[t("i",{class:"fas fa-users fa-2x"})],-1))]),t("div",Y,[e[5]||(e[5]=t("div",{class:"text-yellow-200 text-sm mb-1"},"文章总数",-1)),t("div",q,i(u.value.article_count||0),1),e[6]||(e[6]=t("div",{class:"mt-2 text-yellow-500"},[t("i",{class:"fas fa-file-alt fa-2x"})],-1))]),t("div",G,[e[7]||(e[7]=t("div",{class:"text-yellow-200 text-sm mb-1"},"订单总数",-1)),t("div",H,i(u.value.purchase_count||0),1),e[8]||(e[8]=t("div",{class:"mt-2 text-yellow-500"},[t("i",{class:"fas fa-shopping-cart fa-2x"})],-1))])]),t("div",J,[t("div",K,[t("div",P,[e[10]||(e[10]=t("h3",{class:"text-lg font-semibold text-yellow-500"},"最近注册用户",-1)),_(l,{to:"/users",class:"text-sm btn-secondary"},{default:g(()=>e[9]||(e[9]=[v(" 查看全部 ",-1),t("i",{class:"fas fa-arrow-right fa-xs ml-1"},null,-1)])),_:1,__:[9]})]),t("div",Q,[f.value.length>0?(d(),n("div",W,[t("ul",X,[(d(!0),n(j,null,$(f.value,o=>(d(),n("li",{key:o.id,class:"py-3 flex items-center justify-between"},[t("div",Z,[e[11]||(e[11]=t("div",{class:"w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center mr-3 ring-1 ring-gray-400"},[t("i",{class:"fas fa-user text-gray-300"})],-1)),t("div",null,[t("div",tt,i(o.username),1),t("div",et,"注册于 "+i(a(o.created_at)),1)])]),_(l,{to:`/users/${o.id}`,class:"btn-secondary text-xs"},{default:g(()=>e[12]||(e[12]=[t("i",{class:"fas fa-eye fa-xs mr-1"},null,-1),v("查看 ",-1)])),_:2,__:[12]},1032,["to"])]))),128))])])):(d(),n("div",st,e[13]||(e[13]=[t("i",{class:"fas fa-inbox fa-2x mb-2 text-gray-500"},null,-1),t("p",null,"暂无注册用户",-1)])))])]),t("div",at,[t("div",lt,[e[15]||(e[15]=t("h3",{class:"text-lg font-semibold text-yellow-500"},"最近发布文章",-1)),_(l,{to:"/articles",class:"text-sm btn-secondary"},{default:g(()=>e[14]||(e[14]=[v(" 查看全部 ",-1),t("i",{class:"fas fa-arrow-right fa-xs ml-1"},null,-1)])),_:1,__:[14]})]),t("div",rt,[m.value.length>0?(d(),n("div",ot,[t("ul",nt,[(d(!0),n(j,null,$(m.value,o=>(d(),n("li",{key:o.id,class:"py-3 flex items-center justify-between"},[t("div",it,[e[16]||(e[16]=t("div",{class:"w-10 h-10 rounded-full bg-gray-500 flex items-center justify-center mr-3 ring-1 ring-gray-400"},[t("i",{class:"fas fa-newspaper text-gray-300"})],-1)),t("div",null,[t("div",dt,i(o.title),1),t("div",ct,"发布于 "+i(a(o.created_at)),1)])]),_(l,{to:`/articles/${o.id}`,class:"btn-secondary text-xs"},{default:g(()=>e[17]||(e[17]=[t("i",{class:"fas fa-eye fa-xs mr-1"},null,-1),v("查看 ",-1)])),_:2,__:[17]},1032,["to"])]))),128))])])):(d(),n("div",ut,e[18]||(e[18]=[t("i",{class:"fas fa-inbox fa-2x mb-2 text-gray-500"},null,-1),t("p",null,"暂无发布文章",-1)])))])])])])}}}),gt=V(ft,[["__scopeId","data-v-50a2aa86"]]);export{gt as default};
